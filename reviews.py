#!/usr/bin/env python3

from src.core.logging import get_logger, configure_logging
from src.core.constants import LOG_LEVEL
from src.reviews.main import process_reviews

configure_logging(log_level=LOG_LEVEL)
logger = get_logger(__name__)

def main():
    logger.info("Application starting")
    try:
        process_reviews()
    except Exception as e:
        logger.error(f"Error in reviews code: {e}")

if __name__ == "__main__":
    main()
