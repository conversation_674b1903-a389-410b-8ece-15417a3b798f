from src.core.constants import LOG_LEVEL
from src.core.logging import configure_logging, get_logger
from src.modules.output_collections.create_earnings_dates_collection import create_earnings_dates_collection

configure_logging(log_level=LOG_LEVEL)
logger = get_logger(__name__)


def main():
    logger.info("Earning dates are being updated")
    create_earnings_dates_collection()


if __name__ == "__main__":
    main()
