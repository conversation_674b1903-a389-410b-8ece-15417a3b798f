#!/usr/bin/env python3

from src.core.logging import get_logger, configure_logging
from src.core.constants import LOG_LEVEL
from src.reviews.main import process_employee_reviews_historical
from datetime import datetime

configure_logging(log_level=LOG_LEVEL)
logger = get_logger(__name__)

def main():
    logger.info("Processing Sales Reviews...")
    start_date = datetime(2025, 7, 11)
    process_employee_reviews_historical(start_date)

if __name__ == "__main__":
    main()
