from src.core.constants import LOG_LEVEL
from src.core.logging import configure_logging, get_logger
from src.interview_questions.ask_followupquestions_insights_driven import ask_follow_up_questions_insightdriven

configure_logging(log_level=LOG_LEVEL)
logger = get_logger(__name__)


def main():
    """
    event_lookback_days: The number of days which event should be looked back for tickers
    insight_lookback_days: Historical insights that we should be looking at

    """
    logger.info("Running the interview questions")
    event_lookback_days = 7
    insight_lookback_days = 60

    ask_follow_up_questions_insightdriven(event_lookback_days=event_lookback_days, insight_lookback_days=insight_lookback_days)


if __name__ == "__main__":
    main()
