from src.services.llm.openai_service import OpenAIService
from src.core.logging import get_logger, configure_logging
import re
from datetime import datetime
from pymongo.collection import Collection
from src.reviews.core.extraction import extract_review_ids
from src.database.factory import DatabaseFactory

logger = get_logger(__name__)
configure_logging()

connection = DatabaseFactory().get_mongo_connection()
sales_prompts_collection: Collection = connection.get_sales_collection("prompts")
product_prompts_collection: Collection = connection.get_product_insight_collection("prompts")
employee_prompts_collection: Collection = connection.get_employee_insight_collection("prompts")
openai_service = OpenAIService()

def sales_update_insights_collection(ticker: str, slug: str, category: str, review_count: int, review_chunks_collection: Collection, insights_collection: Collection, CUTOFF_DATE: datetime, END_DATE: datetime):
    reviews = review_chunks_collection.find({"ticker": ticker, "category": category, "cutoff_date": CUTOFF_DATE}, no_cursor_timeout=True)
    review_text = " ".join([f"[review_id: {review['review_id']}] {review['review_text']}" for review in reviews])   # Check if review_text is empty
    if not review_text:
        logger.info(f"No review text found for category: {category}")
        return ""
    prompt_doc = sales_prompts_collection.find_one({"prompt_name": "sales_summary_prompt", "version": "1.0"})
    if not prompt_doc:
        logger.error("Prompt not found")
        return
    prompt = prompt_doc["prompt"].format(category=category, review_text=review_text)
    summary = openai_service.get_completion(prompt=prompt)
    summary = summary.strip().strip('"')
    review_ids = extract_review_ids(summary)
    logger.info(f"Review IDs: {review_ids}")
    insights_collection.insert_one({
        "ticker": ticker,
        "cutoff_date": CUTOFF_DATE,
        "end_date": END_DATE,
        "slug": slug,
        "category": category,
        "category_count": review_count,
        "summary": summary,
        "review_ids": review_ids,
        "updated_at": datetime.now()
    })

def employee_update_insights_collection(ticker: str, category: str, review_count: int, review_text: str, insights_collection: Collection, CUTOFF_DATE: datetime, END_DATE: datetime):
    prompt_doc = employee_prompts_collection.find_one({"prompt_name": "employee_summary_prompt", "version": "1.0"})
    if not prompt_doc:
        logger.error("Prompt not found")
        return
    prompt = prompt_doc["prompt"].format(category=category, review_text=review_text)
    summary = openai_service.get_completion(prompt)
    logger.info(f"Insight for category '{category}': {summary}")
    summary = summary.strip().strip('"')

    # extract all the ["review_id: "] from summary into a list
    review_ids = re.findall(r'\[review_id: ([\w, ]+)\]', summary)
    review_ids = [review_id.strip() for group in review_ids for review_id in group.split(',')]

    # Insert the summary into the sales_insights collection
    insights_collection.insert_one({
        "ticker": ticker,
        "cutoff_date": CUTOFF_DATE,
        "end_date": END_DATE,
        "category": category,
        "category_count": review_count,
        "summary": summary,
        "review_ids_modified": review_ids,
        "updated_at": datetime.now()
    })

def products_update_insights_collection(ticker: str, product: str, review_count: int, review_text: str, insights_collection: Collection, CUTOFF_DATE: datetime, END_DATE: datetime):
    prompt_doc = product_prompts_collection.find_one({"prompt_name": "products_summary_prompt", "version": "1.0"})
    if not prompt_doc:
        logger.error("Prompt not found")
        return
    prompt = prompt_doc["prompt"].format(category=product, review_text=review_text)
    summary = openai_service.get_completion(prompt)
    summary = summary.strip().strip('"')
    review_ids = extract_review_ids(summary)
    logger.info(f"Review IDs: {review_ids}")
    insights_collection.insert_one({
        "ticker": ticker,
        "cutoff_date": CUTOFF_DATE,
        "end_date": END_DATE,
        "product": product,
        "product_count": review_count,
        "summary": summary,
        "review_ids": review_ids,
        "updated_at": datetime.now()
    })
