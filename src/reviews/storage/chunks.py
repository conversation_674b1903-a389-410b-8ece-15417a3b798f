from src.reviews.schemas import SalesReviewCategory, EmployeeReviewCategory, ProductReviewCategory
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger, configure_logging
from bson import ObjectId
from datetime import datetime
from pymongo.collection import Collection

logger = get_logger(__name__)
configure_logging()

def sales_store_review_chunks(ticker: str, slug: str, review_data: SalesReviewCategory, reviews_collection: Collection, review_chunks_collection: Collection, CUTOFF_DATE: datetime, END_DATE: datetime) -> bool:
    try:
        review_id = ObjectId(review_data.review_id)
    except Exception as e:   
        logger.error(f"Invalid ObjectId: {review_data.review_id}. Error: {e}")
        return False
    
    sales_review = reviews_collection.find_one({"_id": review_id})
    if not sales_review:
        logger.info(f"Review not found for review_id: {review_id}")
        return False
    created_at = sales_review.get("created_at")

    review_chunks_collection.update_one(
        {"review_id": review_id},
        {
            "$set": {
                "ticker": ticker,
                "cutoff_date": CUTOFF_DATE,
                "end_date": END_DATE,
                "created_at": created_at,
                "slug": slug,
                "category": review_data.category,
                "review_text": review_data.review_text,
                "updated_at": datetime.now()
            }
        },
        upsert=True
    )
    return True


def employee_store_review_chunks(ticker: str, comments_collection: Collection, articles_collection: Collection, review_chunks_collection: Collection, review_data: EmployeeReviewCategory, CUTOFF_DATE: datetime, END_DATE: datetime) -> bool:
    logger.info(f"Review ID: {review_data.review_id}")
    logger.info(f"Category: {review_data.category}")

    match review_data.review_id[0]:
        case "a":
            comment_id = review_data.review_id[2:]
            try:
                comment_obj_id = ObjectId(comment_id)
                review_id_obj = comment_obj_id
            except Exception as e:   
                logger.info(f"Invalid ObjectId: {comment_id}. Error: {e}")
                return False
            comment = comments_collection.find_one({"_id": comment_obj_id})
            source = "comment"

            if not comment:
                logger.info(f"Review not found for review_id: {comment_id}")
                created_at = None
                return False
            else:
                created_at = comment.get("date")
        
        case "b":
            article_id = review_data.review_id[2:]
            try:
                article_obj_id = ObjectId(article_id)
                review_id_obj = article_obj_id
            except Exception as e:   
                logger.info(f"Invalid ObjectId: {article_id}. Error: {e}")
                return False
            article = articles_collection.find_one({"_id": article_obj_id})
            source = "article"

            if not article:
                logger.info(f"Review not found for review_id: {article_id}")
                created_at = None
                return False
            else:
                created_at = article.get("date")

        case _:
            logger.info(f"Invalid review_id: {review_data.review_id}")
            created_at = None
            return False

    review_chunks_collection.update_one(
        {"review_id_modified": review_data.review_id},
        {
            "$set": {
                "ticker": ticker,
                "cutoff_date": CUTOFF_DATE,
                "end_date": END_DATE,
                "review_id": review_id_obj,
                "created_at": created_at,
                "category": review_data.category,
                "review_text": review_data.review_text,
                "source": source,
                "updated_at": datetime.now()
            }
        },
        upsert=True
    )

    return True


def products_store_review_chunks(ticker: str, review_data: ProductReviewCategory, reviews_collection: Collection, review_chunks_collection: Collection, CUTOFF_DATE: datetime, END_DATE: datetime):
    try:
        review_id = ObjectId(review_data.review_id)
    except Exception as e:
        logger.error(f"Invalid ObjectId: {review_data.review_id}. Error: {e}")
        return False
    
    trustpilot_review = reviews_collection.find_one({"_id": review_id})
    if not trustpilot_review:
        logger.info(f"Review not found for review_id: {review_id}")
        return False
    created_at = trustpilot_review.get("date")

    review_chunks_collection.update_one(
        {"review_id": review_id},  # Filter by review_id
        {
            "$set": {
                "ticker": ticker,
                "cutoff_date": CUTOFF_DATE,
                "end_date": END_DATE,
                "product": review_data.product,
                "review_text": review_data.review_text,
                "created_at": created_at,
                "updated_at": datetime.now()
            }
        },
        upsert=True  # Perform upsert
    )
    return True