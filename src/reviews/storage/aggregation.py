from src.core.logging import get_logger, configure_logging
from pymongo.collection import Collection
from datetime import datetime

logger = get_logger(__name__)
configure_logging()

def sales_aggregate_insights(ticker: str, slug: str, insights_collection: Collection, CUTOFF_DATE: datetime):
    "Groups reviews by category, counts occurrences, generates  final insights"
    total_category_count = insights_collection.count_documents({"ticker": ticker})
    if total_category_count == 0:
        logger.info(f"No categories found for ticker {ticker}. Skipping...")
        return
    
    #if total category_count is greater than 0, find the most common category
    most_common_category = insights_collection.aggregate([
        {
            "$match": {"ticker": ticker, "cutoff_date": CUTOFF_DATE}
        },
        {
            "$group": {
                "_id": "$category",
                "count": {"$sum": 1}
            }
        },
        {
            "$sort": {"count": -1}
        },
        {
            "$limit": 1
        }
    ])   # get the most common category
    most_common_category = list(most_common_category)
    if not most_common_category:
        logger.info(f"No most common category found for ticker: {ticker}")
        return
    most_common_category_data = most_common_category[0]
    most_common_category = most_common_category_data["_id"]
    # get the most common category count
    most_common_category_count = most_common_category_data["count"]
    # find %ge of most common category
    most_common_category_percentage = (most_common_category_count / total_category_count) * 100

    # set all flags to false for ticker
    insights_collection.update_many(
        {"ticker": ticker},
        {"$set": {"flag": False}}
    )
    if most_common_category_percentage > 33:
        logger.info(f"Most common category for {ticker} is {most_common_category} with percentage {most_common_category_percentage}")
        insights_collection.update_one(
            {"ticker": ticker, "category": most_common_category},
            {"$set": {"flag": True}}
        )