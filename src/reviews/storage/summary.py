from datetime import datetime
from src.core.logging import get_logger, configure_logging
from pymongo.collection import Collection

logger = get_logger(__name__)
configure_logging()

def create_sales_review_summary(CUTOFF_DATE: datetime, END_DATE: datetime, insights_collection: Collection, summary_collection: Collection):
    # Define the query to filter insights within the given date range
    query = {
        "cutoff_date": {"$gte": CUTOFF_DATE},
        "end_date": {"$lte": END_DATE}
    }

    # Build the aggregation pipeline
    pipeline = [
        {"$match": query},
        {"$project": {
            "ticker": 1,
            "category": 1,
            "summary": 1,
            "cutoff_date": 1,
            "end_date": 1,
        }},
        {"$sort": {"end_date": -1}},
        {"$group": {
            "_id": {
                "ticker": "$ticker",
                "end_date": "$end_date",
                "cutoff_date": "$cutoff_date"
            },
            "reviews": {"$push": {
                "id": "$_id",
                "category": "$category",
                "summary": "$summary",
                "ticker": "$ticker",
                "cutoff_date": "$cutoff_date",
                "end_date": "$end_date",
            }},
        }},
        {"$project": {
            "_id": 0,
            "ticker": "$_id.ticker",
            "end_date": "$_id.end_date",
            "cutoff_date": "$_id.cutoff_date",
            "reviews": 1,
        }},
        {"$sort": {"end_date": -1}}
    ]

    # Execute the aggregation from the "sales_insights" collection in db_salesinsights2
    aggregated_results = list(insights_collection.aggregate(pipeline))

    # Write the aggregated documents into the "sales_summary" collection
    if aggregated_results:
        for doc in aggregated_results:
            filter_criteria = {"ticker": doc["ticker"], "end_date": doc["end_date"]}
            summary_collection.update_one(filter_criteria, {"$set": doc}, upsert=True)
        logger.info(f"Inserted {len(aggregated_results)} summary documents into 'sales_summary'.")
    else:
        logger.info("No aggregated documents to insert.")


def create_employee_review_summary(CUTOFF_DATE: datetime, END_DATE: datetime, insights_collection: Collection, summary_collection: Collection):
    # Define the query to filter insights within the given date range
    query = {
        "cutoff_date": {"$gte": CUTOFF_DATE},
        "end_date": {"$lte": END_DATE}
    }

    # Build the aggregation pipeline
    pipeline = [
        {"$match": query},
        {"$project": {
            "ticker": 1,
            "category": 1,
            "summary": 1,
            "cutoff_date": 1,
            "end_date": 1,
        }},
        {"$sort": {"end_date": -1}},
        {"$group": {
            "_id": {
                "ticker": "$ticker",
                "end_date": "$end_date",
                "cutoff_date": "$cutoff_date"
            },
            "reviews": {"$push": {
                "id": "$_id",
                "category": "$category",
                "summary": "$summary",
                "ticker": "$ticker",
                "cutoff_date": "$cutoff_date",
                "end_date": "$end_date",
            }},
        }},
        {"$project": {
            "_id": 0,
            "ticker": "$_id.ticker",
            "end_date": "$_id.end_date",
            "cutoff_date": "$_id.cutoff_date",
            "reviews": 1,
        }},
        {"$sort": {"end_date": -1}}
    ]   # Execute the aggregation from the "blind_insights" collection in dbblindinsights2
    aggregated_results = list(insights_collection.aggregate(pipeline))

    # Write the aggregated documents into the "blind_summary" collection
    if aggregated_results:
        for doc in aggregated_results:
            filter_criteria = {"ticker": doc["ticker"], "end_date": doc["end_date"]}
            summary_collection.update_one(filter_criteria, {"$set": doc}, upsert=True)
        logger.info(f"Inserted {len(aggregated_results)} summary documents into 'blind_summary'.")
    else:
        logger.info("No aggregated documents to insert.")

def create_products_review_summary(CUTOFF_DATE: datetime, END_DATE: datetime, insights_collection: Collection, summary_collection: Collection):
    # Define the query to filter insights within the given date range
    query = {
        "cutoff_date": {"$gte": CUTOFF_DATE},
        "end_date": {"$lte": END_DATE}
    }

    # Build the aggregation pipeline
    pipeline = [
        {"$match": query},
        {"$project": {
            "ticker": 1,
            "product": 1,
            "summary": 1,
            "cutoff_date": 1,
            "end_date": 1,
        }},
        {"$sort": {"end_date": -1}},
        {"$group": {
            "_id": {
                "ticker": "$ticker",
                "end_date": "$end_date",
                "cutoff_date": "$cutoff_date"
            },
            "reviews": {"$push": {
                "id": "$_id",
                "product": "$product",
                "summary": "$summary",
                "ticker": "$ticker",
                "cutoff_date": "$cutoff_date",
                "end_date": "$end_date",
            }},
        }},
        {"$project": {
            "_id": 0,
            "ticker": "$_id.ticker",
            "end_date": "$_id.end_date",
            "cutoff_date": "$_id.cutoff_date",
            "reviews": 1,
        }},
        {"$sort": {"end_date": -1}}
    ]

    # Execute the aggregation from the "tp_insights" collection in dbproductinsights2
    aggregated_results = list(insights_collection.aggregate(pipeline))

    # Write the aggregated documents into the "tp_summary" collection
    if aggregated_results:
        for doc in aggregated_results:
            filter_criteria = {"ticker": doc["ticker"], "end_date": doc["end_date"]}
            summary_collection.update_one(filter_criteria, {"$set": doc}, upsert=True)
        logger.info(f"Inserted {len(aggregated_results)} summary documents into 'tp_summary'.")
    else:
        logger.info("No aggregated documents to insert.")
