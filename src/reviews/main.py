from pymongo.collection import Collection
from src.reviews.pipelines import sales_pipeline, employee_pipeline, products_pipeline
from src.reviews.storage.summary import create_sales_review_summary, create_employee_review_summary, create_products_review_summary
from src.core.constants import REPVUE_PROCESSING_WINDOW, BLIND_PROCESSING_WINDOW, TRUSTPILOT_PROCESSING_WINDOW
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory

connection = DatabaseFactory().get_mongo_connection()
sales_ticker_collection: Collection = connection.get_blackberry_sales_collection("ticker_identifier")
sales_insights_collection: Collection = connection.get_sales_collection("sales_insights")
sales_summary_collection: Collection = connection.get_sales_collection("sales_summary")

employee_ticker_collection = connection.get_blackberry_employee_collection("blind_companies")
employee_insights_collection: Collection = connection.get_employee_insight_collection("blind_insights")
employee_summary_collection: Collection = connection.get_employee_insight_collection("blind_summary")

products_ticker_collection = connection.get_blackberry_product_collection("trustpilot_companies")
products_insights_collection: Collection = connection.get_product_insight_collection("tp_insights")
products_summary_collection: Collection = connection.get_product_insight_collection("tp_summary")

def process_reviews():
    end_date = datetime.today()
    repvue_cutoff_date: datetime = datetime.today() - timedelta(days=REPVUE_PROCESSING_WINDOW)
    with sales_ticker_collection.find({}).batch_size(10) as docs:
        for doc in docs:
            ticker = doc["ticker"]
            slug = doc["slug"]
            sales_pipeline.process_one_ticker(ticker, slug, repvue_cutoff_date, end_date)
    create_sales_review_summary(repvue_cutoff_date, end_date, sales_insights_collection, sales_summary_collection)

    blind_cutoff_date: datetime = datetime.today() - timedelta(days=BLIND_PROCESSING_WINDOW)
    with employee_ticker_collection.find({}).batch_size(10) as docs:
        for doc in docs:
            ticker = doc["ticker"]
            employee_pipeline.process_one_ticker(ticker, blind_cutoff_date, end_date)
    create_employee_review_summary(blind_cutoff_date, end_date, employee_insights_collection, employee_summary_collection)

    trustpilot_cutoff_date: datetime = datetime.today() - timedelta(days=TRUSTPILOT_PROCESSING_WINDOW)
    with products_ticker_collection.find({}).batch_size(10) as docs:
        for doc in docs:
            ticker = doc["ticker"]
            products_pipeline.process_one_ticker(ticker, trustpilot_cutoff_date, end_date)
    create_products_review_summary(trustpilot_cutoff_date, end_date, products_insights_collection, products_summary_collection)

def process_sales_reviews_historical(start_date: datetime):
    today = datetime.now()
    current_start = start_date
    window_count = 1

    print("Fetching sales ticker documents...")
    sales_tickers = list(sales_ticker_collection.find())
    print(f"Found {len(sales_tickers)} sales tickers")
    print(f"Generating windows from {start_date.strftime('%Y-%m-%d')} to {today.strftime('%Y-%m-%d')}")
    print("=" * 60)

    while current_start <= today:
        current_end = current_start + timedelta(days=10)
        if current_end > today:
            current_end = today

        print(f"Window {window_count:2d}: start_date = {current_start.strftime('%Y-%m-%d')}, end_date = {current_end.strftime('%Y-%m-%d')}")

        for doc in sales_tickers:
            ticker = doc["ticker"]
            slug = doc["slug"]
            sales_pipeline.process_one_ticker(ticker, slug, current_start, current_end)
        create_sales_review_summary(current_start, current_end, sales_insights_collection, sales_summary_collection)

        current_start = current_start + timedelta(days=3)
        window_count += 1

def process_employee_reviews_historical(start_date: datetime):
    today = datetime.now()
    current_start = start_date
    window_count = 1

    print("Fetching employee ticker documents...")
    employee_tickers = list(employee_ticker_collection.find())
    print(f"Found {len(employee_tickers)} employee tickers")
    print(f"Generating windows from {start_date.strftime('%Y-%m-%d')} to {today.strftime('%Y-%m-%d')}")
    print("=" * 60)

    while current_start <= today:
        current_end = current_start + timedelta(days=10)
        if current_end > today:
            current_end = today

        print(f"Window {window_count:2d}: start_date = {current_start.strftime('%Y-%m-%d')}, end_date = {current_end.strftime('%Y-%m-%d')}")

        for doc in employee_tickers:
            ticker = doc["ticker"]
            employee_pipeline.process_one_ticker(ticker, current_start, current_end)
        create_employee_review_summary(current_start, current_end, employee_insights_collection, employee_summary_collection)

        current_start = current_start + timedelta(days=3)
        window_count += 1

def process_products_reviews_historical(start_date: datetime):
    today = datetime.now()
    current_start = start_date
    window_count = 1

    print("Fetching product ticker documents...")
    products_tickers = list(products_ticker_collection.find())
    print(f"Found {len(products_tickers)} product tickers")
    print(f"Generating windows from {start_date.strftime('%Y-%m-%d')} to {today.strftime('%Y-%m-%d')}")
    print("=" * 60)

    while current_start <= today:
        current_end = current_start + timedelta(days=10)
        if current_end > today:
            current_end = today

        print(f"Window {window_count:2d}: start_date = {current_start.strftime('%Y-%m-%d')}, end_date = {current_end.strftime('%Y-%m-%d')}")

        for doc in products_tickers:
            ticker = doc["ticker"]
            products_pipeline.process_one_ticker(ticker, current_start, current_end)
        create_products_review_summary(current_start, current_end, products_insights_collection, products_summary_collection)

        current_start = current_start + timedelta(days=3)
        window_count += 1
