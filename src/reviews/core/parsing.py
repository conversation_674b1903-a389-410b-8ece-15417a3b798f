from typing import List, Any
import re
import json
from pydantic import TypeAdapter, ValidationError
from src.core.logging import get_logger, configure_logging

logger = get_logger(__name__)
configure_logging()

def parse_categorization_results(ai_response: str, OutputSchema: Any) -> List[Any]:
    """
    Extracts and validates JSON from AI response with error handling
    """
    match = re.search(r'(\{\s*\"review_id.*\}|\[.*\])', ai_response, re.DOTALL)
    if not match:
        logger.warning(f"No valid JSON found in OpenAI response - Skipping.")
        return []
    json_str = match.group(1).strip()
    try:
        review_list = json.loads(match.group(1))
    except json.JSONDecodeError as e:
        logger.warning(f"Error parsing JSON categories : {e}")
        return []

    if isinstance(review_list, dict):
        review_list = [review_list]

    adapter = TypeAdapter(List[OutputSchema])
    try:
        validated_reviews = adapter.validate_python(review_list)
    except ValidationError as e:
        logger.warning(f"Validation error: {e}")
        return []
    except Exception as e:
        logger.warning(f"Unknown error in JSON validation: {e}")
        return []

    return validated_reviews
