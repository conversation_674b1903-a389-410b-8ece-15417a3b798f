from src.core.logging import get_logger, configure_logging

logger = get_logger(__name__)
configure_logging()

def preprocess_reviews(review_articles: list,  field_name: str = "content", id_prefix: str = "") -> str:
    """
    Formats raw reviews into standardized text strings with review IDs
    """
    reviews_articles_str = ""
    for review_article in review_articles:
        try:
            assert len(review_article[field_name].split()) > 2
            reviews_articles_str += f"review_id: {id_prefix}{review_article['_id']} "
            reviews_articles_str += f"review_{field_name}: {review_article[field_name]} "
        except KeyError as e:
            logger.error(f"KeyError in {review_article}: {e}")
            continue
        except AssertionError:
            logger.warning(f"Short review_{field_name}: {id_prefix}{review_article['_id']} - '{review_article[field_name]}'")
            continue
    return reviews_articles_str
