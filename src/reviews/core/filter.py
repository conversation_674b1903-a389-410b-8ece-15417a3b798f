from datetime import datetime, timedelta
from typing import Optional, Literal
from pymongo.collection import Collection

MAX_REVIEWS = 20
CYCLE = 3

def filter_reviews(main_field: str, main_value: str, date_field: str, date_format: Literal["str","datetime"], cutoff_date: datetime, end_date: datetime, expression: Optional[dict], collection: Collection) -> list:
    start_date = cutoff_date if date_format == "datetime" else cutoff_date.strftime("%Y-%m-%d")
    mid_date = end_date - timedelta(days=CYCLE)
    mid_date = mid_date if date_format == "datetime" else mid_date.strftime("%Y-%m-%d")
    last_date = end_date if date_format == "datetime" else end_date.strftime("%Y-%m-%d")

    new_reviews_query = {
        main_field: main_value,
        date_field: {
            "$gt": mid_date,
            "$lte": last_date
        }
    }
    if expression:
        new_reviews_query["$expr"] = expression

    new_reviews = collection.find(new_reviews_query)
    new_reviews_count = collection.count_documents(new_reviews_query)
    if new_reviews_count >= MAX_REVIEWS:
        review_list = list(new_reviews)
    else:
        old_reviews_query = {
            main_field: main_value,
            date_field: {
                "$gt": start_date,
                "$lte": mid_date
            }
        }
        if expression:
            old_reviews_query["$expr"] = expression
        old_reviews = collection.find(old_reviews_query).sort(date_field, -1).limit(MAX_REVIEWS - new_reviews_count)
        review_list = list(new_reviews) + list(old_reviews)
    return review_list
