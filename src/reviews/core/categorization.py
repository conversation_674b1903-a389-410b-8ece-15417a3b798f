from src.reviews.core.parsing import parse_categorization_results
from src.services.llm.openai_service import OpenAIService
from typing import List, Any
from pymongo.collection import Collection
from src.core.logging import get_logger, configure_logging

logger = get_logger(__name__)
configure_logging()

openai_service = OpenAIService()

def categorize_reviews(prompt_template: str, OutputSchema: Any, **kwargs) -> List[Any]:
    """
    Extracts and validates JSON from AI response with error handling
    """
    if not prompt_template:
        logger.error("Prompt not found")
        return []
    prompt = prompt_template.format_map(kwargs)
    response = openai_service.get_completion_without_limits(prompt=prompt, temperature=0)
    review_list = parse_categorization_results(response, OutputSchema)
    return review_list
