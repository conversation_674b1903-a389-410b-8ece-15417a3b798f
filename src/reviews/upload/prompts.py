from src.database.factory import DatabaseFactory
from pymongo.collection import Collection

connection = DatabaseFactory().get_mongo_connection()
sales_prompts_collection: Collection = connection.get_sales_collection("prompts")
employee_prompts_collection: Collection = connection.get_employee_insight_collection("prompts")
products_prompts_collection: Collection = connection.get_product_insight_collection("prompts")

SALES_REVIEWS_CATEGORIZATION_PROMPT = """You are a financial analyst. The section between the <reviews start> and <reviews end> tags contains reviews from sales employees about the products of {slug}.
Your task is to categorize the reviews into different categories.
Output should be in JSON format,with fields "review_id", "category" and "review_text" with review_text under 100 words.
Ignore text that is not related to the categories.
Ignore text that is not about the company {slug}
The categories are:
{categories}

The reviews are
<reviews  start>
{reviews_articles_str}
<reviews end>

Sample output: (json format)
'review_id': _id, 'category': 'hiring trends', 'review_text': 'The company is hiring for a new product manager.'
"""

SALES_SUMMARY_PROMPT = """You are a financial analyst summarizing insights from sales comments about {category}.
Summarize the following text into a 50-word insight. Use exact quotes if possible within quotation marks.
For each quote also include the corresponding review_id in brackets.
Output example:
"The company is hiring for a new product manager. <sup>[review_id: 12345]</sup>"

Text: {review_text}
"""

EMPLOYEE_REVIEWS_CATEGORIZATION_PROMPT = """You are a financial analyst. The section between the <reviews start> and <reviews end> tags contains reviews from sales employees about the products of {ticker}.
Your task is to categorize the reviews into different categories.
Output should be in JSON format,with fields "review_id", "category" and "review_text" with review_text under 100 words.
Ignore text that is not related to the categories.
Ignore text that is not about the company {ticker}
The categories are:
{categories}

The reviews are
<reviews  start>
{reviews_articles_str}
< reviews end>

Sample output: (json format)
'review_id': 12345, 'category': 'hiring trends', 'review_text': 'The company is hiring for a new product manager.'
"""

EMPLOYEE_SUMMARY_PROMPT = """You are a financial analyst summarizing insights from employee comments about {category}.
Summarize the following text into a 50-word insight. Use exact quotes if possible within quotation marks.
For each quote also include the corresponding review_id in brackets.
Output example:
"The company is hiring for a new product manager. <sup>[review_id: 12345]</sup>"

Text: {review_text}
"""

PRODUCTS_REVIEWS_CATEGORIZATION_PROMPT = """You are a financial analyst. The section between the <reviews start> and <reviews end> tags contains reviews from customers
about the products of {ticker}. Your task is to categorize the reviews into different products.
Output should be in JSON format,with fields "review_id", "product" and "review_text" with review_text under 100 words.
Ignore text that is not about the company {ticker}

The reviews are
<reviews  start>
{reviews_articles_str}
< reviews end>

Sample output: (json format)
'review_id': _id, 'product': 'sales cloud', 'review_text': 'The company is hiring for a new product manager in salescloud'
"""

PRODUCTS_SUMMARY_PROMPT = """You are a financial analyst summarizing insights from sales comments about {category}.
Summarize the following text into a 50-word insight. Use exact quotes if possible within quotation marks.
For each quote also include the corresponding review_id in brackets.
Output example:
" Customers seem to 'aggressively adopt' sales cloud. <sup>[review_id: 12345]</sup>"

Text: {review_text}
"""

def upload_prompts():
    sales_prompts_collection.update_one(
        {"prompt_name": "sales_reviews_categorization_prompt"},
        {"$set": {"prompt": SALES_REVIEWS_CATEGORIZATION_PROMPT, "version": "1.0"}},
        upsert=True
    )
    sales_prompts_collection.update_one(
        {"prompt_name": "sales_summary_prompt"},
        {"$set": {"prompt": SALES_SUMMARY_PROMPT, "version": "1.0"}},
        upsert=True
    )
    employee_prompts_collection.update_one(
        {"prompt_name": "employee_reviews_categorization_prompt"},
        {"$set": {"prompt": EMPLOYEE_REVIEWS_CATEGORIZATION_PROMPT, "version": "1.0"}},
        upsert=True
    )
    employee_prompts_collection.update_one(
        {"prompt_name": "employee_summary_prompt"},
        {"$set": {"prompt": EMPLOYEE_SUMMARY_PROMPT, "version": "1.0"}},
        upsert=True
    )
    products_prompts_collection.update_one(
        {"prompt_name": "products_reviews_categorization_prompt"},
        {"$set": {"prompt": PRODUCTS_REVIEWS_CATEGORIZATION_PROMPT, "version": "1.0"}},
        upsert=True
    )
    products_prompts_collection.update_one(
        {"prompt_name": "products_summary_prompt"},
        {"$set": {"prompt": PRODUCTS_SUMMARY_PROMPT, "version": "1.0"}},
        upsert=True
    )
