from src.database.factory import DatabaseFactory
from pymongo.collection import Collection

connection = DatabaseFactory().get_mongo_connection()
sales_categories_collection: Collection = connection.get_sales_collection("categories")
employee_categories_collection: Collection = connection.get_employee_insight_collection("categories")
products_categories_collection: Collection = connection.get_product_insight_collection("categories")

sales_categories = {
    "hiring trends",
    "quota and commissions",
    "revenue growth",
    "products",
    "lay offs",
    "competition to the company's products",
    "major customer wins",
    "major customer losses",
    "general positive comments about company",
    "general negative comments about company",
    "upcoming business changes",
    "general business description"
}

employee_categories = {
    "hiring trends",
    "quota and commissions",
    "revenue growth",
    "products",
    "lay offs",
    "competition to the company's products",
    "major customer wins",
    "major customer losses",
    "general positive comments about company",
    "general negative comments about company",
    "upcoming business changes",
    "general business description"
}

def upload_categories():
    sales_categories_collection.update_one(
        {"name": "sales_categories"},
        {"$set": {"categories": list(sales_categories)}},
        upsert=True
    )
    employee_categories_collection.update_one(
        {"name": "employee_categories"},
        {"$set": {"categories": list(employee_categories)}},
        upsert=True
    )