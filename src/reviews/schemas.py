from pydantic import BaseModel, field_validator

class SalesReviewCategory(BaseModel):
    review_id: str
    category: str
    review_text: str

    # @field_validator("review_id", mode="before")
    # def coerce_review_id(cls, v):
    #     return str(v)

class EmployeeReviewCategory(BaseModel):
    review_id: str
    category: str
    review_text: str

class ProductReviewCategory(BaseModel):
    review_id: str
    product: str
    review_text: str