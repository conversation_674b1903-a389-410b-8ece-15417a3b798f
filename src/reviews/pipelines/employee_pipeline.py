from src.reviews.core.preprocessing import preprocess_reviews
from src.reviews.core.categorization import categorize_reviews
from src.reviews.storage.chunks import employee_store_review_chunks
from src.reviews.storage.insights import employee_update_insights_collection
from src.reviews.core.filter import filter_reviews
from src.reviews.schemas import EmployeeReviewCategory
from src.core.logging import get_logger, configure_logging
from pymongo.collection import Collection
from datetime import datetime
from typing import List
from src.database.factory import DatabaseFactory
from bson import ObjectId

logger = get_logger(__name__)
configure_logging()

connection = DatabaseFactory().get_mongo_connection()

ticker_collection: Collection = connection.get_blackberry_employee_collection("blind_companies")
articles_collection: Collection = connection.get_blackberry_employee_collection("articles")
comments_collection: Collection = connection.get_blackberry_employee_collection("comments")

insights_collection: Collection = connection.get_employee_insight_collection("blind_insights")
review_chunks_collection: Collection = connection.get_employee_insight_collection("review_chunks")
summary_collection: Collection = connection.get_employee_insight_collection("blind_summary")
prompts_collection: Collection = connection.get_employee_insight_collection("prompts")
categories_collection: Collection = connection.get_employee_insight_collection("categories")

def process_one_ticker(ticker: str, CUTOFF_DATE: datetime, END_DATE: datetime) -> None:
    logger.info(f"Processing ticker: {ticker}")
    expression = {"$eq": ["$author_company", "$company_slug"]}

    comments = filter_reviews("company_ticker", ticker, "date", "datetime", CUTOFF_DATE, END_DATE, expression, comments_collection)
    articles = filter_reviews("company_ticker", ticker, "date", "datetime", CUTOFF_DATE, END_DATE, expression, articles_collection)
    if len(comments) == 0 and len(articles) == 0:
        logger.info(f"No reviews found for ticker: {ticker}")
        return
    
    latest_insight = insights_collection.find_one({"ticker": ticker}, sort=[("cutoff_date", -1)])
    if latest_insight:
        processed_review_ids = latest_insight.get("review_ids", [])
        processed_review_ids = [ObjectId(rid) for rid in processed_review_ids]
        comments = [article for article in comments if article["_id"] not in processed_review_ids]
        articles = [article for article in articles if article["_id"] not in processed_review_ids]
        logger.info(f"Filtered out {len(processed_review_ids)} already processed reviews. {len(comments) + len(articles)} reviews remaining.")

    review_article_string1 = preprocess_reviews(comments, field_name="content", id_prefix="a_")
    review_article_string2 = preprocess_reviews(articles, field_name="description", id_prefix="b_")
    reviews_articles_string = review_article_string1 + " " + review_article_string2

    prompt_doc = prompts_collection.find_one({"prompt_name": "employee_reviews_categorization_prompt", "version": "1.0"})
    if not prompt_doc:
        logger.error("Prompt not found")
        return

    categories_doc = categories_collection.find_one({"name": "employee_categories"})
    if not categories_doc:
        logger.error("Categories not found")
        return

    review_list: List[EmployeeReviewCategory] = categorize_reviews(prompt_doc["prompt"], EmployeeReviewCategory, ticker=ticker, categories=categories_doc["categories"], reviews_articles_str=reviews_articles_string)

    for review in review_list:
        employee_store_review_chunks(ticker, comments_collection, articles_collection, review_chunks_collection, review, CUTOFF_DATE, END_DATE)

    categories_for_ticker = review_chunks_collection.distinct("category", {"ticker": ticker})
    for category in categories_for_ticker:
        review_count = review_chunks_collection.count_documents({"ticker": ticker, "category": category})
        reviews = review_chunks_collection.find({"ticker": ticker, "category": category, "cutoff_date": CUTOFF_DATE})
        logger.info(f"Number of reviews for category '{category}': {review_count}")
        review_text = " ".join([f"[review_id: {review['review_id_modified']}] {review['review_text']}" for review in reviews])
        if not review_text:
            logger.warning(f"No review text found for category: {category}")
            continue
        employee_update_insights_collection(ticker, category, review_count, review_text, insights_collection, CUTOFF_DATE, END_DATE)
