from src.reviews.core.preprocessing import preprocess_reviews
from src.reviews.core.categorization import categorize_reviews
from src.reviews.storage.chunks import sales_store_review_chunks
from src.reviews.storage.insights import sales_update_insights_collection
from src.reviews.storage.aggregation import sales_aggregate_insights
from src.reviews.core.filter import filter_reviews
from src.reviews.schemas import SalesReviewCategory
from src.core.logging import get_logger, configure_logging
from pymongo.collection import Collection
from datetime import datetime, timedelta
from bson import ObjectId
from src.database.factory import DatabaseFactory

logger = get_logger(__name__)
configure_logging()

connection = DatabaseFactory().get_mongo_connection()

reviews_collection: Collection = connection.get_blackberry_sales_collection("reviews")

insights_collection: Collection = connection.get_sales_collection("sales_insights")
review_chunks_collection: Collection = connection.get_sales_collection("review_chunks")
summary_collection: Collection = connection.get_sales_collection("sales_summary")
prompts_collection: Collection = connection.get_sales_collection("prompts")
categories_collection: Collection = connection.get_sales_collection("categories")

def process_one_ticker(ticker: str, slug: str, cutoff_date: datetime, end_date: datetime) -> None:
    ticker_search_insight_query = {
        "ticker": ticker,
        "cutoff_date": {
            "$gte": cutoff_date,
            "$lt": cutoff_date + timedelta(days=1)
        }
    }
    if insights_collection.find_one(ticker_search_insight_query):
        logger.info(f"Ticker {ticker} already exists in insights collection. Skipping...")
        return
    logger.info(f"Processing ticker {ticker}")

    review_articles = filter_reviews("slug", slug, "created_at", "str", cutoff_date, end_date, None, reviews_collection)
    if len(review_articles) == 0:
        logger.info(f"No reviews found for ticker: {ticker}")
        return
    
    latest_insight = insights_collection.find_one({"ticker": ticker}, sort=[("cutoff_date", -1)])
    if latest_insight:
        processed_review_ids = latest_insight.get("review_ids", [])
        processed_review_ids = [ObjectId(rid) for rid in processed_review_ids]
        review_articles = [article for article in review_articles if article["_id"] not in processed_review_ids]
        logger.info(f"Filtered out {len(processed_review_ids)} already processed reviews. {len(review_articles)} reviews remaining.")

    reviews_articles_string = preprocess_reviews(review_articles, field_name="text")

    prompt_doc = prompts_collection.find_one({"prompt_name": "sales_reviews_categorization_prompt", "version": "1.0"})
    if not prompt_doc:
        logger.error("Prompt not found")
        return

    categories_doc = categories_collection.find_one({"name": "sales_categories"})
    if not categories_doc:
        logger.error("Categories not found")
        return

    review_list = categorize_reviews(prompt_doc["prompt"], SalesReviewCategory, reviews_articles_str=reviews_articles_string, slug=slug, categories=categories_doc["categories"])
    for review in review_list:
        sales_store_review_chunks(ticker, slug, review, reviews_collection, review_chunks_collection, cutoff_date, end_date)

    categories_for_ticker = review_chunks_collection.distinct("category", {"ticker": ticker})
    for category in categories_for_ticker:
        review_count = review_chunks_collection.count_documents({"ticker": ticker, "category": category})
        if review_count == 0:
            logger.info(f"No reviews found for category {category}. Skipping...")
            continue
        logger.info(f"Number of reviews for category '{category}': {review_count}")
        sales_update_insights_collection(ticker, slug, category, review_count, review_chunks_collection, insights_collection, cutoff_date, end_date)

    sales_aggregate_insights(ticker, slug, insights_collection, cutoff_date)
