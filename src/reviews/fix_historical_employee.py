from pymongo import DESCENDING
from src.database.factory import DatabaseFactory

connection = DatabaseFactory.get_mongo_connection()

insight_collection = connection.get_employee_insight_collection("blind_insights")
summary_collection = connection.get_employee_insight_collection("blind_summary")
ticker_collection = connection.get_blackberry_employee_collection("blind_companies")


def clean_insights(ticker: str):
    previous_review_id_set = set()
    for doc in list(
        insight_collection.find({"ticker": ticker}).sort("end_date", DESCENDING)
    ):
        current_review_ids = tuple(doc.get("review_ids_modified", []))
        if not current_review_ids:
            print(f"No review_ids_modified for end_date {doc['end_date']}, skipping.")
            continue
        if current_review_ids in previous_review_id_set:
            print(
                f"Duplicate found for end_date {doc['end_date']}: {current_review_ids}"
            )
            insight_collection.delete_one({"_id": doc["_id"]})
        else:
            previous_review_id_set.add(current_review_ids)


def clean_summary(ticker: str):
    for doc in list(
        summary_collection.find({"ticker": ticker}).sort("end_date", DESCENDING)
    ):
        reviews = list(doc.get("reviews", []))
        # print(
        #     f"Pre-clean reviews for end_date {doc['end_date']}: {[r['id'] for r in reviews]}"
        # )
        for review in reviews:
            if not insight_collection.find_one({"_id": review.get("id")}):
                print(
                    f"Orphaned review found in summary for end_date {doc['end_date']}: {review['id']}"
                )
                reviews.remove(review)

        # print(
        #     f"Post-clean reviews for end_date {doc['end_date']}: {[r['id'] for r in reviews]}"
        # )

        if len(reviews) == 0:
            print(f"Deleting summary for end_date {doc['end_date']}")
            summary_collection.delete_one({"_id": doc["_id"]})

        elif len(reviews) < len(doc.get("reviews", [])):
            print(f"Updating summary for end_date {doc['end_date']}")
            summary_collection.update_one(
                {"_id": doc["_id"]}, {"$set": {"reviews": reviews}}
            )


def fix_historical_employee():
    all_tickers = list(ticker_collection.distinct("ticker"))
    for ticker in all_tickers:
        print(f"Processing ticker: {ticker}" + "=" * 40 + "\n")
        clean_insights(ticker)
        clean_summary(ticker)
        print("" + "=" * 40 + "\n")