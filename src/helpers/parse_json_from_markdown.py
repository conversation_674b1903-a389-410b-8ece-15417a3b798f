import re
import json
from src.core.logging import get_logger

logger = get_logger(__name__)

def parse_json_from_markdown(input_str):
    """
    Extract and parse JSON from an LLM or Markdown-style string output.

    - Detects ```json ... ``` or generic ``` ... ``` fenced blocks
    - Falls back to parsing the entire string if no fence is found
    - Strips whitespace, BOM, and trailing commas before parsing
    - Returns None if no valid JSON can be parsed
    """
    print("input_str: ", input_str)
    # Normalize weird whitespace or BOM characters
    input_str = input_str.strip().replace("\ufeff", "")

    # Regex: captures code fences with optional json tag
    fence_re = re.compile(r'```(?:json)?\s*([\s\S]*?)```', re.IGNORECASE)
    match = fence_re.search(input_str)
    json_text = match.group(1) if match else input_str

    # Clean up common JSON formatting issues from LLMs
    json_text = json_text.strip()
    json_text = re.sub(r',\s*([}\]])', r'\1', json_text)  # remove trailing commas

    try:
        return json.loads(json_text)
    except json.JSONDecodeError as e:
        logger.warning(f"Failed to parse JSON normally: {e}. Attempting fallback cleanup.")

        # --- Fallback 1: Try to extract the first valid JSON object/array ---
        json_fallback = re.search(r'(\{.*\}|\[.*\])', json_text, re.DOTALL)
        if json_fallback:
            try:
                return json.loads(json_fallback.group(1))
            except Exception as inner_e:
                logger.error(f"Fallback JSON parsing also failed: {inner_e}")

        # --- Fallback 2: Return None or raise depending on your use case ---
        return None
