from pymongo import <PERSON><PERSON><PERSON><PERSON>
from src.core.constants import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>RRY_SALES_DB_NAME,
    B<PERSON>CKBERRY_EMPLOYEE_DB_NAME,
    BLACKBERRY_PRODUCT_DB_NAME,
    BLACKBERRY_FILINGS_DB_NAME,
    MONGO_URI,
    MONGO_BLACKBERRY_URI,
    MONGO_DB_NAME,
    MONGO_STOCK_DB_NAME,
    MONGO_EMAIL_DB_NAME,
    PODCAST_DB_NAME,
    SALES_INSIGHT_DB_NAME,
    PRODUCT_INSIGHT_DB_NAME,
    EMPLOYEE_INSIGHT_DB_NAME,
    FILINGS_DB_NAME,
    FEED_DB_NAME,
    SLIDES_DB_NAME,
)


def create_clients_from_env():
    """Create clients using environment variables"""  
    blackberry_client = MongoClient(MONGO_BLACKBERRY_URI)
    mongo_client = MongoClient(MONGO_URI)
    return blackberry_client, mongo_client


def find_and_replace_ticker(ticker, replacement_ticker):
    blackberry_client, mongo_client = create_clients_from_env()

    # Hardcoded DBs for each client
    blackberry_dbs = [
        BLACKBERRY_SALES_DB_NAME,
        BLACKBERRY_EMPLOYEE_DB_NAME,
        BLACKBERRY_PRODUCT_DB_NAME,
        BLACKBERRY_FILINGS_DB_NAME,
    ]

    mongo_dbs = [
        MONGO_DB_NAME,
        # MONGO_STOCK_DB_NAME,
        MONGO_EMAIL_DB_NAME,
        PODCAST_DB_NAME,
        SALES_INSIGHT_DB_NAME,
        PRODUCT_INSIGHT_DB_NAME,
        EMPLOYEE_INSIGHT_DB_NAME,
        FILINGS_DB_NAME,
        FEED_DB_NAME,
        SLIDES_DB_NAME,
    ]

    # Blackberry DBs
    print("\n=== Blackberry Client DBs ===")
    for db_name in blackberry_dbs:
        db = blackberry_client[db_name]
        collections = db.list_collection_names()
        print(f"\nDB: {db_name}")
        if collections:
            for coll in collections:
                if coll.startswith("system."):
                    continue
                count = db[coll].count_documents({"ticker": ticker})
                if count > 0:
                    result = db[coll].update_many(
                        {"ticker": ticker}, {"$set": {"ticker": replacement_ticker}}
                    )
                    print(f" - {coll}: {count} docs → updated {result.modified_count}")
                else:
                    print(f" - {coll}: 0 matching docs")
        else:
            print(" (no collections)")

    # Mongo DBs
    print("\n=== Mongo Client DBs ===")
    for db_name in mongo_dbs:
        db = mongo_client[db_name]
        collections = db.list_collection_names()
        print(f"\nDB: {db_name}")
        if collections:
            for coll in collections:
                if coll.startswith("system."):
                    continue
                count = db[coll].count_documents({"ticker": ticker})
                if count > 0:
                    result = db[coll].update_many(
                        {"ticker": ticker}, {"$set": {"ticker": replacement_ticker}}
                    )
                    print(f" - {coll}: {count} docs → updated {result.modified_count}")
                else:
                    print(f" - {coll}: 0 matching docs")
        else:
            print(" (no collections)")


if __name__ == "__main__":
    ticker = "ZI"
    replacement_ticker = "GTM"
    find_and_replace_ticker(ticker=ticker, replacement_ticker=replacement_ticker)
