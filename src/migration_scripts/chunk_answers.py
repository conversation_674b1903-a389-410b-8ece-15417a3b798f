from datetime import datetime
from bson import ObjectId
from src.database.factory import DatabaseFactory
from src.services.text_splitter import Text<PERSON><PERSON>litter


def get_answer_chunks(
    answer: str,
    answer_speaker: str,
    qna_id: ObjectId,
    ticker: str,
    date: str,
    event_id: ObjectId,
    event_name: str
):
    splitter = TextSplitter()
    chunks = splitter.split(answer)
    answer_chunks = []
    for i in range(len(chunks)):
        answer_chunks.append(
            {
                "qnaId": qna_id,
                "chunk_id": f"{str(qna_id)}_{i}",
                "chunk": chunks[i],
                "date": date,
                "ticker": ticker,
                "answer_speaker": answer_speaker,
                "event": event_name,
                "event_id": event_id,
                "updated_at": datetime.now(),
            }
        )
    return answer_chunks


def process_answers():
    connection = DatabaseFactory().get_mongo_connection()
    embeddings_collection = connection.get_collection("embeddings")
    print("Starting to Process embeddings")
    ctr = 1
    with embeddings_collection.find({"answer_chunks": {"$exists": False}}).batch_size(100) as embeddings_to_process:
        for embedding in embeddings_to_process:
            ctr += 1
            print(f"Started to process embedding #{ctr}")
            embedding_id = embedding["_id"]
            answer = embedding["answer"]
            answer_speaker = embedding["answer_speaker"]
            qnaId = embedding["qnaId"]
            ticker = embedding["ticker"]
            date = embedding["date"]
            event_id = embedding["event_id"]
            event_name = embedding["event_name"]
            answer_chunks = get_answer_chunks(answer=answer, answer_speaker=answer_speaker, qna_id=qnaId, ticker=ticker, date=date, event_id=event_id, event_name=event_name)
            embeddings_collection.update_one(
                {"_id": embedding_id},
                {"$set": {"answer_chunks": answer_chunks}},
            )
            print(f"Completed to process embedding #{ctr}")


if __name__ == "__main__":
    process_answers()
