import os
import snowflake.connector
import pandas as pd
from dotenv import load_dotenv
from src.database.factory import DatabaseFactory

load_dotenv()


SNOW_USER = os.getenv("SNOW_USER")
SNOW_PASSWORD = os.getenv("SNOW_PASSWORD")
SNOW_ACCOUNT = os.getenv("SNOW_ACCOUNT")
SNOW_WAREHOUSE = os.getenv("SNOW_WAREHOUSE")
SNOW_DATABASE = os.getenv("SNOW_DATABASE")
SNOW_SCHEMA = os.getenv("SNOW_SCHEMA")


def get_companyid_from_snow(ticker):
    query = """SELECT
        c.companyName,
        c.companyId,
        ti.tickerSymbol,
        e.exchangeSymbol,
        st.companyStatusTypeName,
        ts.tradingItemStatusName
    FROM
        ciqCompany c
    JOIN
        ciqSecurity s ON c.companyId = s.companyId
    JOIN
        ciqTradingItem ti ON ti.securityId = s.securityId
    JOIN
        ciqExchange e ON e.exchangeId = ti.exchangeId
    JOIN
        ciqCompanyStatusType st ON st.companyStatusTypeId = c.companyStatusTypeId
    JOIN
        ciqTradingItemStatus ts ON ts.tradingItemStatusId = ti.tradingItemStatusId
    WHERE
        (e.exchangeSymbol LIKE 'NasdaqG%' OR e.exchangeSymbol = 'NYSE')
        AND ti.tickerSymbol = '{ticker}'
        AND s.primaryFlag = 1
        AND ti.tradingItemStatusId = 15
        AND s.securityEndDate IS NULL"""
    connection = snowflake.connector.connect(
        user=SNOW_USER,
        password=SNOW_PASSWORD,
        account=SNOW_ACCOUNT,
        warehouse=SNOW_WAREHOUSE,
        database=SNOW_DATABASE,
        schema=SNOW_SCHEMA,
        client_session_keep_alive=True,
        insecure_mode=True,
    )
    query = query.format(ticker=ticker)
    cursor = connection.cursor()
    output = cursor.execute(query)
    output = cursor.fetchone()
    return output[1]


def validate_company_ids():
    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    company_list = list(companies_collection.find({}).limit(3))

    result_json = []
    for i, company in enumerate(company_list):
        ticker = company["ticker"]
        company_id = company["company_id"]

        snow_company_id = get_companyid_from_snow(ticker=ticker)
        result = {
            "ticker": ticker,
            "company_id": company_id,
            "snow_company_id": snow_company_id
        }
        if company_id != snow_company_id:
            print(f"Company id is wrong for {ticker}")
            result["verdict"] = "Incorrect"
        else:
            result["verdict"] = "Correct"
        result_json.append(result)

        print(f"Completed: {i}")
    df = pd.DataFrame(result_json)
    df.to_csv('company_results.csv', index=False)


if __name__ == "__main__":
    validate_company_ids()
