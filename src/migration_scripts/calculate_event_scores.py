import numpy as np
from datetime import date, datetime, timedelta

from src.core.constants import LOOKBACK_DAYS
from src.database.factory import DatabaseFactory


def calculate_mean_sd(sector, date_start, date_end):
    """
    Returns
    -------
    mean_uptick_score, std_dev_uptick_score,
    mean_downtick_score, std_dev_downtick_score,
    uptick_threshold, downtick_threshold, downtick_threshold2, uptick_threshold2
    (unchanged order / count)
    """
    connection = DatabaseFactory().get_mongo_connection()
    coll = connection.get_collection("public_investor_events_outputs")

    def safe(stat_list, field, fallback):
        """Return stat_list[0][field] unless it’s missing or None."""
        return stat_list[0].get(field) if stat_list and stat_list[0].get(field) is not None else fallback

    # ---------- Downtick stats ----------
    downtick_stats = list(coll.aggregate([
        {"$match": {"event_type": "non_earnings", "sector": sector, "date": {"$gte": date_start, "$lte": date_end}, "output_type": {"$nin": ["SUMMARY", "MDA"]}}},
        {"$group": {
            "_id": None,
            "mean": {"$avg": "$downtick_score"},
            "sd": {"$stdDevSamp": "$downtick_score"}
        }}
    ]))
    mean_downtick_score = safe(downtick_stats, "mean", -2.5)
    std_dev_downtick_score = safe(downtick_stats, "sd", 4.0)

    # ---------- Difference stats (uptick – downtick) ----------
    net_stats = list(coll.aggregate([
        {"$match": {"event_type": "non_earnings", "sector": sector, "date": {"$gte": date_start, "$lte": date_end}, "output_type": {"$nin": ["SUMMARY", "MDA"]}}},
        {"$project": {"sum": {"$add": ["$uptick_score", "$downtick_score"]}}},
        {"$group": {
            "_id": None,
            "mean": {"$avg": "$sum"},
            "sd": {"$stdDevSamp": "$sum"}
        }}
    ]))
    mean_net = safe(net_stats, "mean", 3.8)
    std_dev_net = safe(net_stats, "sd", 5.1)

    # ---------- Thresholds (unchanged logic) ----------
    net_threshold = mean_net + 1 * std_dev_net

    downtick_threshold = mean_downtick_score - 1 * std_dev_downtick_score

    # ---------- Logging ----------
    print(f"Mean diff (up‑down): {mean_net:.4f}")
    print(f"SD diff: {std_dev_net:.4f}")
    print(f"Downtick threshold: {downtick_threshold:.4f}")

    return net_threshold, downtick_threshold, mean_downtick_score, std_dev_net, std_dev_downtick_score, mean_net


def calculate_scores(calculated_sentiment, net_score, downtick_score, mean_downtick_score, std_net_score, std_downtick_score, mean_net):
    # mean_uptick_minus_downtick_score = 0  # Need to be calculated on rolling 180 days
    # std_uptick_minus_downtick_score = 0  # Need to be calculated on rolling 180 days

    if std_downtick_score != 0:
        downtick_normal = (downtick_score - mean_downtick_score) / std_downtick_score
    else:
        downtick_normal = -1

    if std_net_score != 0:
        net_normal = ((net_score - mean_net)) / (std_net_score)
    else:
        net_normal = 1
    epsilon = 0.05  # Low epsilon to reduce clipping at -0.4 and +0.4
    upper_bound = 0.32  # This results in range of score being <0.4 when neutral
    lower_bound = -0.24  # This results in range of score being <-0.4 when neutral

    if calculated_sentiment == 'negative':
        final_score = -np.log(1 - downtick_normal) / np.log(2)
    elif calculated_sentiment == 'positive':
        final_score = np.log(1 + net_normal) / np.log(2)
    else:
        final_score = np.log(1 + min(upper_bound, max(lower_bound, epsilon * net_normal))) / np.log(2)

    return final_score


def calculate_sentiment(net, down, down_th, net_th):
    if down < down_th:
        sentiment = "negative"
    elif net > net_th:
        sentiment = "positive"
    else:
        sentiment = "neutral"
    return sentiment


def process_event_sentiment_scores(start_date: date, end_date: date, lookback: int = LOOKBACK_DAYS):
    connection = DatabaseFactory().get_mongo_connection()
    coll_events = connection.get_collection("public_investor_events_outputs")

    # ── cache of sector thresholds keyed by (sector, window_end) ──────
    # threshold_cache: dict[tuple[str, date], tuple[float, float]] = {}

    # def thresholds(sector: str, window_end: date) -> tuple[float, float]:
    #     """
    #     Return (net_threshold, downtick_threshold) for the sector,
    #     using non‑earnings events that ended on window_end (inclusive)
    #     and started LOOKBACK_DAYS before.
    #     """
    #     window_start = window_end - timedelta(days=lookback)
    #     key = (sector, window_start, window_end)
    #     if key not in threshold_cache:
    #         net_th, down_th, mean_downtick_score, std_dev_net, std_dev_downtick_score, mean_net = calculate_mean_sd(
    #             sector,
    #             datetime.combine(window_start, datetime.min.time()),
    #             datetime.combine(window_end, datetime.min.time()) - timedelta(seconds=1)
    #         )
    #         threshold_cache[key] = (net_th, down_th, mean_downtick_score, std_dev_net, std_dev_downtick_score, mean_net)
    #     return threshold_cache[key]

    def thresholds(sector: str, window_end: date) -> tuple[float, float]:
        """
        Return (net_threshold, downtick_threshold) for the sector,
        using non‑earnings events that ended on window_end (inclusive)
        and started LOOKBACK_DAYS before.
        """
        window_start = window_end - timedelta(days=lookback)
        net_th, down_th, mean_downtick_score, std_dev_net, std_dev_downtick_score, mean_net = calculate_mean_sd(
            sector,
            datetime.combine(window_start, datetime.min.time()),
            datetime.combine(window_end, datetime.min.time()) - timedelta(seconds=1)
        )
        return net_th, down_th, mean_downtick_score, std_dev_net, std_dev_downtick_score, mean_net
    # ── pull non‑earnings events inside back‑test window ──────────────
    non_cur = coll_events.find({
        "event_type": "non_earnings",
        # "date": {
        #     "$gte": datetime.combine(start_date, datetime.min.time()),
        #     "$lte": datetime.combine(end_date, datetime.max.time())
        # }
    }, projection={
        "_id": 1,
        "sentiment": 1,
        "ticker": 1,
        "sector": 1,
        "uptick_score": 1,
        "downtick_score": 1,
        "date": 1,
    }).sort("date", 1)

    ctr = 0
    for evt in non_cur:
        sector = evt.get("sector") or "UNKNOWN"
        evt_dt = evt["date"].date() if isinstance(evt["date"], datetime) else evt["date"]

        # ── thresholds based on preceding LOOKBACK_DAYS ───────────────

        up = evt.get("uptick_score")
        down = evt.get("downtick_score")
        if up is None or down is None:
            continue
        net_th, down_th, mean_downtick_score, std_dev_net, std_dev_downtick_score, mean_net = thresholds(sector, evt_dt - timedelta(days=1))
        net = up + down
        sentiment = calculate_sentiment(net=net, down=down, down_th=down_th, net_th=net_th)
        event_score = calculate_scores(calculated_sentiment=sentiment, net_score=net, downtick_score=down, mean_downtick_score=mean_downtick_score, std_net_score=std_dev_net, std_downtick_score=std_dev_downtick_score, mean_net=mean_net)
        sentiment_old = evt.get("sentiment")
        if evt.get("event_type") == "earnings":
            sentiment = sentiment_old
        event_score = round(event_score, 2)
        obj = {
            'sentiment': sentiment,
            'score': round(event_score, 2),
            'net_score': round(net, 2),
            "net_th": round(net_th, 2),
            "down_th": round(down_th, 2),
            'downtick_score': down,
            'mean_downtick_score': round(mean_downtick_score, 2),
            'std_net_score': round(std_dev_net, 2),
            'std_downtick_score': round(std_dev_downtick_score, 2),
            'mean_net': round(mean_net, 2),
        }
        coll_events.update_one(
            {"_id": evt["_id"]},
            {"$set": obj}
        )
        ctr += 1
        print(f"Counter: {ctr}")


if __name__ == "__main__":
    process_event_sentiment_scores(date(2024, 1, 1), date(2025, 5, 25), lookback=180)
