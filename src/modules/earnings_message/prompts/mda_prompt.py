from src.modules.earnings_message.schemas.structured_outputs import ExtractedMetrics
from typing import Any, get_args

DYNAMIC_PROMPT_TEMPLATE = """You are an expert financial analyst AI assistant. Your primary function is to meticulously analyze Management Discussion and Analysis (MD&A) sections from corporate earnings call transcripts.

Your task is to extract specific financial metrics and forward-looking guidance from the provided MD&A text. You must return this information STRICTLY in the JSON format specified below.

**Key Information to Extract:**

{key_information}

**Output Format (JSON):**

Please structure your output precisely as follows. If a specific piece of information is not explicitly mentioned or cannot be reliably determined from the provided text, use `null` as the value for that key. Do not add any explanations or conversational text outside of the JSON object.
{{
    {output_format}
}}

**Instructions for Extraction:**

- Focus SOLELY on the information present in the MD&A text provided. Do not infer information from external knowledge.
- For revenue, extract the numerical value and convert it to a precise number (e.g., convert $1.2B to 1200000000, €500M to 500000000).
- Extract the currency code (USD, EUR, INR, RMB, etc.) separately and include it in the "currency" field.
- For all percentage values, remove the % symbol and provide only the numerical value (e.g., 45.2 instead of "45.2%").
- For revenue and margins, prioritize figures explicitly stated for the "reported quarter" or "current quarter" being discussed.
- For guidance, look for forward-looking statements about the "next quarter," "upcoming quarter," or specific future period.
- If guidance is provided as a qualitative statement (e.g., "low single-digits") that cannot be converted to a precise number, keep it as a string.
- Always give out a single number if available even by inference. For example, "low to mid-single digits" can be assumed as 4% and "45.5 to 46.5" can be assumed as 46%.
- If guidance is provided as a qualitative statement (e.g., "low single-digits") that cannot be converted to a precise number, keep it as a string.

**Current Quarter**
{current_quarter}

**MD&A Text to Analyze:**

--- BEGIN MD&A TEXT ---
{mda}
--- END MD&A TEXT ---

Now, please process the MD&A text provided above and return the JSON output
"""

def get_type_annotation(type_: Any) -> str:
    tuple_ = get_args(type_)
    if tuple_[0] == str:
        return "string | null"
    elif tuple_[0] == int:
        return "number | null"
    elif tuple_[0] == float:
        return "number | null"
    else:
        return "any | null"

def get_mda_extraction_prompt(mda: str, current_quarter: str, extraction_schema: ExtractedMetrics):
    empty_fields = [field for field, value in extraction_schema.model_dump().items() if value is None]
    info = []
    output = []
    for i, field in enumerate(empty_fields, start=1):
        info.append(f"{i}. **{field}**: {ExtractedMetrics.model_fields[field].description}")
        output.append(f'"{field}": {get_type_annotation(ExtractedMetrics.model_fields[field].annotation)}')

    key_information = "\n".join(info)
    output_format = ",\n    ".join(output)
    prompt = DYNAMIC_PROMPT_TEMPLATE.format(key_information=key_information, output_format=output_format, mda=mda, current_quarter=current_quarter)
    return prompt

def get_historical_mda_prompt(mda: str, current_quarter: str):
    prompt = DYNAMIC_PROMPT_TEMPLATE.format(
        key_information="""1. **revenue_yoy_growth_percentage**: The year-over-year (YoY) growth percentage for revenue.
        2. **revenue_qoq_growth_percentage**: The quarter-over-quarter (QoQ) growth percentage for revenue.""",
        output_format='"revenue_yoy_growth_percentage": number | null,\n     "revenue_qoq_growth_percentage": number | null',
        mda=mda,
        current_quarter=current_quarter
    )
    return prompt