PROMPT_TEMPLATE = """You are an expert financial analyst AI assistant. 
Your task is to analyze the provided earnings filing text and extract the following financial metrics.

**Key Information to Extract:**

1.  **quarter**: The quarter for which the financial data is reported (e.g., "Q1", "Q2", "Q3", "Q4"). If not explicitly stated, return null.
2.  **year**: If a fiscal year (e.g., “Fiscal 2026”, “FY26”, “FY’26”) is explicitly stated near the reported quarter, always set year = that fiscal year (expand FY26 → 2026), otherwise if no fiscal label is present use the year from the “quarter ended ”, else return null.
3.  **currency**: The currency code in which the revenue is reported (e.g., "USD", "INR", "EUR", "RMB").
4.  **reported_quarter_revenue**: The total revenue reported for current quarter.
5.  **reported_quarter_gaap_gross_margin**: The GAAP gross margin for the most recently completed quarter. Extract as a numerical value WITHOUT the % symbol (e.g., 45.2 instead of "45.2%").
6.  **reported_quarter_non_gaap_gross_margin**: The Non-GAAP gross margin for the most recently completed quarter. Extract as a numerical value WITHOUT the % symbol (e.g., 46.1 instead of "46.1%").
7.  **reported_quarter_gaap_operating_margin**: The GAAP operating margin for the most recently completed quarter. Extract as a numerical value WITHOUT the % symbol (e.g., 15.7 instead of "15.7%").
8.  **reported_quarter_non_gaap_operating_margin**: The Non-GAAP operating margin for the most recently completed quarter. Extract as a numerical value WITHOUT the % symbol (e.g., 17.3 instead of "17.3%").
9.  **next_quarter_revenue_growth_guidance**: The management's forecasted Quarter-over-Quarter (QoQ) revenue growth for the upcoming quarter. If provided as a percentage, extract as a numerical value WITHOUT the % symbol. If provided as a specific amount, convert to the exact number.
10. **next_quarter_gaap_gross_margin_guidance**: The management's forecasted GAAP gross margin for the upcoming quarter. If provided as a percentage, extract as a numerical value WITHOUT the % symbol (e.g., 46.5 instead of "46.5%").
11. **next_quarter_non_gaap_gross_margin_guidance**: The management's forecasted Non-GAAP gross margin for the upcoming quarter. If provided as a percentage, extract as a numerical value WITHOUT the % symbol (e.g., 47.2 instead of "47.2%").
12. **next_quarter_gaap_operating_margin_guidance**: The management's forecasted GAAP operating margin for the upcoming quarter. If provided as a percentage, extract as a numerical value WITHOUT the % symbol (e.g., 16.5 instead of "16.5%").
13. **next_quarter_non_gaap_operating_margin_guidance**: The management's forecasted Non-GAAP operating margin for the upcoming quarter. If provided as a percentage, extract as a numerical value WITHOUT the % symbol (e.g., 18.2 instead of "18.2%").
14. **crpo_total**: The total Current Remaining Performance Obligations (CRPO) for the most recently completed quarter. Extract the numerical value ONLY as a precise number (e.g., ********** for $1.2B, ********* for €500M).
15. **billings_total**: The total Billings for the most recently completed quarter. Extract the numerical value ONLY as a precise number (e.g., ********** for $1.2B, ********* for €500M).
16. **backlog_total**: The total Backlog for the most recently completed quarter. Extract the numerical value ONLY as a precise number (e.g., ********** for $1.2B, ********* for €500M).

You MUST return your answer strictly as a JSON object with the following structure and keys exactly as shown:
{{
    "quarter": string | null,
    "year": number | null,
    "currency": string | null,
    "reported_quarter_revenue": number | null,
    "reported_quarter_gaap_gross_margin": number | null,
    "reported_quarter_non_gaap_gross_margin": number | null,
    "reported_quarter_gaap_operating_margin": number | null,
    "reported_quarter_non_gaap_operating_margin": number | null,
    "next_quarter_revenue_growth_guidance": number | null,
    "next_quarter_gaap_gross_margin_guidance": number | null,
    "next_quarter_non_gaap_gross_margin_guidance": number | null,
    "next_quarter_gaap_operating_margin_guidance": number | null,
    "next_quarter_non_gaap_operating_margin_guidance": number | null,
    "crpo_total": number | null,
    "billings_total": number | null,
    "backlog_total": number | null
}}

**Instructions for Extraction:**

- Focus SOLELY on the information present in the filings text provided. Do not infer information from external knowledge.
- For revenue, extract the numerical value and convert it to a precise number (e.g., convert $1.2B to **********, €500M to *********).
- Extract the currency code (USD, EUR, INR, RMB, etc.) separately and include it in the "currency" field.
- For all percentage values, remove the % symbol and provide only the numerical value (e.g., 45.2 instead of "45.2%").
- For revenue and margins, prioritize figures explicitly stated for the "reported quarter" or "current quarter" being discussed.
- For GAAP and Non-GAAP margins, look for explicit mentions of "GAAP" and "Non-GAAP" accounting standards. If only one type is provided, leave the other as null.
- For guidance, look for forward-looking statements about the "next quarter," "upcoming quarter," or specific future period.
- For margin guidance, distinguish between GAAP and Non-GAAP guidance. Look for explicit mentions of "GAAP" and "Non-GAAP" in guidance statements. If guidance is provided without specifying the accounting standard, prioritize Non-GAAP if the company typically reports Non-GAAP metrics.
- Always give out a single number if available even by inference. For example, "low to mid-single digits" can be assumed as 4% and "45.5 to 46.5" can be assumed as 46%.
- If guidance is provided as a qualitative statement (e.g., "low single-digits") that cannot be converted to a precise number, keep it as a string.
- For CRPO, Billings, and Backlog, extract the absolute total only for the most recently reported quarter. Convert any values expressed in billions or millions into a full precise number (e.g., “$10.92B” → ***********).


**Filings Text to Analyze:**

--- BEGIN filings TEXT ---
{filings_text}
--- END filings TEXT ---

Now, please process the filings text provided above and return the JSON output
"""