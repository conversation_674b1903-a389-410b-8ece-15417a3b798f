from src.services.llm.openai_service import OpenAIService
from src.modules.earnings_message.schemas.structured_outputs import ExtractedMetrics
from src.modules.earnings_message.prompts.mda_prompt import get_mda_extraction_prompt, get_historical_mda_prompt
import re, json
import pandas as pd
from src.core.logging import get_logger, configure_logging

logger = get_logger(__name__)
configure_logging()

openai = OpenAIService()

def extract_mda_information(mda_text: str, current_period: pd.Period, eight_k_data: ExtractedMetrics) -> dict:
    earnings_analysis_prompt = get_mda_extraction_prompt(
        mda=mda_text, current_quarter=f"Q{current_period.quarter}-{current_period.year}", extraction_schema=eight_k_data
    )

    raw_response = openai.get_completion_without_limits(prompt=earnings_analysis_prompt)
    # Try to find JSON inside triple backticks first
    match = re.search(r"```(?:json)?\s*(.*?)\s*```", raw_response, re.DOTALL | re.IGNORECASE)
    if match:
        json_str = match.group(1)
    else:
        # Fallback: extract first JSON object in the text
        match = re.search(r"\{[\s\S]*\}", raw_response)
        if match:
            json_str = match.group(0)
        else:
            json_str = ""
            logger.error(f"No JSON found in model response: {raw_response[:200]}...")
    
    try:
        out = json.loads(json_str)
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON for MDAAnalysis: {e}\nExtracted: {json_str}")
        out = {}
    
    return out

def extract_historical_mda_information(mda_text: str, current_period: pd.Period) -> dict:
    earnings_analysis_prompt = get_historical_mda_prompt(
        mda=mda_text, current_quarter=f"Q{current_period.quarter}-{current_period.year}"
    )

    raw_response = openai.get_completion_without_limits(prompt=earnings_analysis_prompt)
    # Try to find JSON inside triple backticks first
    match = re.search(r"```(?:json)?\s*(.*?)\s*```", raw_response, re.DOTALL | re.IGNORECASE)
    if match:
        json_str = match.group(1)
    else:
        # Fallback: extract first JSON object in the text
        match = re.search(r"\{[\s\S]*\}", raw_response)
        if match:
            json_str = match.group(0)
        else:
            json_str = ""
            logger.error(f"No JSON found in model response: {raw_response[:200]}...")
    
    try:
        out = json.loads(json_str)
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON for Historical MDA Analysis: {e}\nExtracted: {json_str}")
        out = {}
    
    return out