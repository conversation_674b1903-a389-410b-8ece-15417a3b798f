from src.services.llm.openai_service import OpenAIService
from src.modules.earnings_message.schemas.structured_outputs import ExtractedMetrics
from src.modules.earnings_message.prompts.eightk_prompt import PROMPT_TEMPLATE
from pydantic import ValidationError
from src.core.logging import get_logger, configure_logging
import tiktoken
import re

logger = get_logger(__name__)
configure_logging()

openai = OpenAIService()

def get_encoding(model: str):
    try:
        return tiktoken.encoding_for_model(model)
    except Exception:
        return tiktoken.get_encoding("cl100k_base")

def count_tokens(text: str, model: str) -> int:
    enc = get_encoding(model)
    return len(enc.encode(text))

def extract_information(text: str) -> ExtractedMetrics:
    model = "gpt-4o"
    enc = get_encoding(model)
    ids = enc.encode(text)
    max_tokens = 128_000 - 1_600
    if (original_token_length := count_tokens(text, model)) > max_tokens:
        first_part = enc.decode(ids[:6000])
        last_part = enc.decode(ids[-(max_tokens-6000):])
        truncated_text = first_part + "\n\n[... content truncated for length ...]\n\n" + last_part
    else:
        truncated_text = text
    
    logger.info(f"Original token length: {original_token_length}, truncated token length: {count_tokens(truncated_text, model)}")
    
    raw_response = openai.get_completion_without_limits(
        prompt=PROMPT_TEMPLATE.format(filings_text=truncated_text)
    )

    # Try to find JSON inside triple backticks first
    match = re.search(r"```(?:json)?\s*(.*?)\s*```", raw_response, re.DOTALL | re.IGNORECASE)
    if match:
        json_str = match.group(1)
    else:
        # Fallback: extract first JSON object in the text
        match = re.search(r"\{[\s\S]*\}", raw_response)
        if match:
            json_str = match.group(0)
        else:
            json_str = ""
            logger.error(f"No JSON found in model response: {raw_response[:200]}...")

    try:
        out = ExtractedMetrics.model_validate_json(json_str)
    except ValidationError as e:
        logger.error(f"Invalid JSON for FinancialData: {e}\nExtracted: {json_str}")
        out = ExtractedMetrics(
            quarter=None, 
            year=None, 
            currency=None, 
            reported_quarter_revenue=None, 
            reported_quarter_gaap_gross_margin=None, 
            reported_quarter_non_gaap_gross_margin=None, 
            reported_quarter_gaap_operating_margin=None, 
            reported_quarter_non_gaap_operating_margin=None, 
            next_quarter_revenue_growth_guidance=None, 
            next_quarter_gaap_gross_margin_guidance=None, 
            next_quarter_non_gaap_gross_margin_guidance=None, 
            next_quarter_gaap_operating_margin_guidance=None, 
            next_quarter_non_gaap_operating_margin_guidance=None, 
            crpo_total=None, 
            billings_total=None, 
            backlog_total=None
            )

    return out