import re
from sec_api import RenderApi
from src.core.constants import SEC_API_KEY
from html_to_markdown import convert_to_markdown
from src.core.logging import get_logger, configure_logging

logger = get_logger(__name__)
configure_logging()

renderApi = RenderApi(api_key=SEC_API_KEY)

def sanitize_unicode(text: str) -> str:
    """
    Remove or replace invalid Unicode characters that can cause encoding issues.
    This specifically handles surrogate characters that are invalid in UTF-8.
    """
    if not text:
        return text

    # Remove surrogate characters (U+D800 to U+DFFF)
    # These are used in UTF-16 but are invalid in UTF-8
    sanitized = ""
    for char in text:
        code_point = ord(char)
        # Skip surrogate characters
        if 0xD800 <= code_point <= 0xDFFF:
            continue
        # Replace other problematic characters with a space
        elif code_point > 0x10FFFF:
            sanitized += " "
        else:
            sanitized += char

    return sanitized

def extract_text_from_url(url: str) -> str|None:
    try:
        html_content = renderApi.get_file(url)
    except Exception as e:
        logger.error(f"Error fetching HTML content from {url}: {e}")
        return None
    
    try:
        if html_content and isinstance(html_content, str):
            # Sanitize the HTML content before converting to markdown
            html_content = sanitize_unicode(html_content)
            markdown = convert_to_markdown(html_content)
            # Sanitize the markdown output as well for extra safety
            markdown = sanitize_unicode(markdown)
            return markdown
    except Exception as e:
        logger.error(f"Error converting HTML to markdown for {url}: {e}")

    return None