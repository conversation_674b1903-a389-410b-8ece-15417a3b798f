from typing import TypedDict, Optional

class RevenueFigures(TypedDict):
    reported_quarter_revenue: Optional[float]
    current_yoy_growth: Optional[float]
    previous_yoy_growth: Optional[float]
    current_qoq_growth: Optional[float]
    previous_qoq_growth: Optional[float]
    q_minus_1_yoy_growth: Optional[float]
    y_minus_1_qoq_growth: Optional[float]


class GrossMarginFigures(TypedDict):
    reported_quarter_gross_margin: Optional[float]
    yoy_margin_change: Optional[float]
    qoq_margin_change: Optional[float]


class OperatingMarginFigures(TypedDict):
    reported_quarter_operating_margin: Optional[float]
    yoy_margin_change: Optional[float]
    qoq_margin_change: Optional[float]


class GuidanceFigures(TypedDict):
    revenue_growth_guidance: Optional[float]  # e.g., 20% YoY
    revenue_growth_vs_prev_yoy: Optional[float]  # e.g., -1.5% deceleration vs prior YoY
    revenue_growth_vs_prev_qoq: Optional[float]  # e.g., +2% acceleration vs prior QoQ

    gross_margin_guidance: Optional[float]  # e.g., 55.0%
    gross_margin_vs_prev_yoy_bps: Optional[float]
    gross_margin_vs_prev_qoq_bps: Optional[float]  # e.g., -20 bps decline vs prior QoQ

    operating_margin_guidance: Optional[float]  # e.g., 30.5%
    operating_margin_vs_prev_yoy_bps: Optional[float]
    operating_margin_vs_prev_qoq_bps: Optional[float]


class CRPOBillingsFigures(TypedDict):
    current_crpo: Optional[float]
    current_billings: Optional[float]
    current_backlog: Optional[float]
    crpo_qoq_growth: Optional[float]
    billings_qoq_growth: Optional[float]
    backlog_qoq_growth: Optional[float]
    crpo_yoy_growth: Optional[float]
    billings_yoy_growth: Optional[float]
    backlog_yoy_growth: Optional[float]
    previous_crpo_qoq_growth: Optional[float]
    previous_billings_qoq_growth: Optional[float]
    previous_backlog_qoq_growth: Optional[float]
    previous_crpo_yoy_growth: Optional[float]
    previous_billings_yoy_growth: Optional[float]
    previous_backlog_yoy_growth: Optional[float]