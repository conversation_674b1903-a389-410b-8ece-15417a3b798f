from typing import TypedDict, Optional
from typing_extensions import NotRequired
from bson import ObjectId
from datetime import datetime

class EightKFilingsData(TypedDict):
    """The structure to store data in mongodb 8-K filings collection"""

    _id: NotRequired[ObjectId]
    event_id: Optional[ObjectId]
    ticker: str
    quarter: str
    year: int
    currency: str
    revenue: Optional[float]
    gaap_gross_margin: Optional[float]
    non_gaap_gross_margin: Optional[float]
    gaap_operating_margin: Optional[float]
    non_gaap_operating_margin: Optional[float]
    revenue_growth_guidance: Optional[float]
    gaap_gross_margin_guidance: Optional[float]
    non_gaap_gross_margin_guidance: Optional[float]
    gaap_operating_margin_guidance: Optional[float]
    non_gaap_operating_margin_guidance: Optional[float]
    crpo_total: Optional[float]
    billings_total: Optional[float]
    backlog_total: Optional[float]
    filed_at: datetime
    cik: str
    accession_number: str
    filing_type: str
    document_url: str
    updated_at: datetime


class EarningsAnalysisData(TypedDict):
    """The structure to store data in mongodb earnings analysis collection"""

    _id: NotRequired[ObjectId]
    event_id: Optional[ObjectId]
    ticker: str
    quarter: str
    year: int
    currency: str
    analysis: str
    date: datetime
    created_at: datetime
    reported_quarter_revenue: Optional[float]
    quarterly_revenue_growth_percent: Optional[float]
    yearly_revenue_growth_percent: Optional[float]
    reported_quarter_gross_margin: Optional[float]
    reported_quarter_operating_margin: Optional[float]
    next_quarter_revenue_growth_guidance: Optional[float]
    next_quarter_operating_margin_guidance: Optional[float]
    next_quarter_gross_margin_guidance: Optional[float]
    mark_for_deletion: bool