from typing import Optional
from dataclasses import dataclass
from src.modules.earnings_message.utilities.calculations import calculate_growth, calculate_bps_change
from src.modules.earnings_message.schemas.mongodb import EightKFilingsData

@dataclass
class QuarterlyData:
    ticker: str
    quarter: str
    year: int
    currency: Optional[str] = None

    revenue: Optional[float] = None
    revenue_qoq_growth: Optional[float] = None
    revenue_yoy_growth: Optional[float] = None

    gaap_gross_margin: Optional[float] = None
    gaap_gross_margin_qoq_change: Optional[float] = None
    gaap_gross_margin_yoy_change: Optional[float] = None

    non_gaap_gross_margin: Optional[float] = None
    non_gaap_gross_margin_qoq_change: Optional[float] = None
    non_gaap_gross_margin_yoy_change: Optional[float] = None

    gaap_operating_margin: Optional[float] = None
    gaap_operating_margin_qoq_change: Optional[float] = None
    gaap_operating_margin_yoy_change: Optional[float] = None

    non_gaap_operating_margin: Optional[float] = None
    non_gaap_operating_margin_qoq_change: Optional[float] = None
    non_gaap_operating_margin_yoy_change: Optional[float] = None

    revenue_growth_guidance: Optional[float] = None
    gaap_gross_margin_guidance: Optional[float] = None
    non_gaap_gross_margin_guidance: Optional[float] = None
    gaap_operating_margin_guidance: Optional[float] = None
    non_gaap_operating_margin_guidance: Optional[float] = None

    crpo_total: Optional[float] = None
    crpo_qoq_growth: Optional[float] = None
    crpo_yoy_growth: Optional[float] = None
    billings_total: Optional[float] = None
    billings_qoq_growth: Optional[float] = None
    billings_yoy_growth: Optional[float] = None
    backlog_total: Optional[float] = None
    backlog_qoq_growth: Optional[float] = None
    backlog_yoy_growth: Optional[float] = None

    def update_current_data(self, current_data: EightKFilingsData) -> None:
        self.currency = current_data.get("currency")
        self.revenue = current_data.get("revenue")
        self.gaap_gross_margin = current_data.get("gaap_gross_margin")
        self.non_gaap_gross_margin = current_data.get("non_gaap_gross_margin")
        self.gaap_operating_margin = current_data.get("gaap_operating_margin")
        self.non_gaap_operating_margin = current_data.get("non_gaap_operating_margin")
        self.revenue_growth_guidance = current_data.get("revenue_growth_guidance")
        self.gaap_gross_margin_guidance = current_data.get("gaap_gross_margin_guidance")
        self.non_gaap_gross_margin_guidance = current_data.get(
            "non_gaap_gross_margin_guidance"
        )
        self.gaap_operating_margin_guidance = current_data.get(
            "gaap_operating_margin_guidance"
        )
        self.non_gaap_operating_margin_guidance = current_data.get(
            "non_gaap_operating_margin_guidance"
        )
        self.crpo_total = current_data.get("crpo_total")
        self.billings_total = current_data.get("billings_total")
        self.backlog_total = current_data.get("backlog_total")

    def calculate_growth_and_change(
        self,
        current_data: EightKFilingsData,
        q_minus_1_data: "QuarterlyData",
        y_minus_1_data: "QuarterlyData",
    ) -> None:
        self.revenue_qoq_growth = calculate_growth(
            q_minus_1_data.revenue, current_data.get("revenue")
        )
        self.revenue_yoy_growth = calculate_growth(
            y_minus_1_data.revenue, current_data.get("revenue")
        )
        self.gaap_gross_margin_qoq_change = calculate_bps_change(
            q_minus_1_data.gaap_gross_margin, current_data.get("gaap_gross_margin")
        )
        self.gaap_gross_margin_yoy_change = calculate_bps_change(
            y_minus_1_data.gaap_gross_margin, current_data.get("gaap_gross_margin")
        )
        self.non_gaap_gross_margin_qoq_change = calculate_bps_change(
            q_minus_1_data.non_gaap_gross_margin,
            current_data.get("non_gaap_gross_margin"),
        )
        self.non_gaap_gross_margin_yoy_change = calculate_bps_change(
            y_minus_1_data.non_gaap_gross_margin,
            current_data.get("non_gaap_gross_margin"),
        )
        self.gaap_operating_margin_qoq_change = calculate_bps_change(
            q_minus_1_data.gaap_operating_margin,
            current_data.get("gaap_operating_margin"),
        )
        self.gaap_operating_margin_yoy_change = calculate_bps_change(
            y_minus_1_data.gaap_operating_margin,
            current_data.get("gaap_operating_margin"),
        )
        self.non_gaap_operating_margin_qoq_change = calculate_bps_change(
            q_minus_1_data.non_gaap_operating_margin,
            current_data.get("non_gaap_operating_margin"),
        )
        self.non_gaap_operating_margin_yoy_change = calculate_bps_change(
            y_minus_1_data.non_gaap_operating_margin,
            current_data.get("non_gaap_operating_margin"),
        )
        self.crpo_qoq_growth = calculate_growth(
            q_minus_1_data.crpo_total, current_data.get("crpo_total")
        )
        self.crpo_yoy_growth = calculate_growth(
            y_minus_1_data.crpo_total, current_data.get("crpo_total")
        )
        self.billings_qoq_growth = calculate_growth(
            q_minus_1_data.billings_total, current_data.get("billings_total")
        )
        self.billings_yoy_growth = calculate_growth(
            y_minus_1_data.billings_total, current_data.get("billings_total")
        )
        self.backlog_qoq_growth = calculate_growth(
            q_minus_1_data.backlog_total, current_data.get("backlog_total")
        )
        self.backlog_yoy_growth = calculate_growth(
            y_minus_1_data.backlog_total, current_data.get("backlog_total")
        )