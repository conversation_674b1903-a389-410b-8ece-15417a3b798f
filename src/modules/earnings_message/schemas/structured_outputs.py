from pydantic import BaseModel, Field
from typing import Optional

class ExtractedMetrics(BaseModel):
    quarter: Optional[str] = Field(
        description='The quarter for which the financial data is reported (e.g., "Q1", "Q2", "Q3", "Q4"). If not explicitly stated, return null.'
    )
    year: Optional[int] = Field(
        description="If a fiscal year (e.g., “Fiscal 2026”, “FY26”, “FY’26”) is explicitly stated near the reported quarter, always set year = that fiscal year (expand FY26 → 2026), otherwise if no fiscal label is present use the year from the “quarter ended ”, else return null."
    )
    currency: Optional[str] = Field(
        description='The currency code in which the revenue is reported (e.g., "USD", "INR", "EUR", "RMB").'
    )
    reported_quarter_revenue: Optional[float] = Field(
        description="The total revenue reported for the current quarter."
    )
    reported_quarter_gaap_gross_margin: Optional[float] = Field(
        description='The GAAP gross margin for the most recently completed quarter. Extract as a numerical value WITHOUT the % symbol (e.g., 45.2 instead of "45.2%").'
    )
    reported_quarter_non_gaap_gross_margin: Optional[float] = Field(
        description='The Non-GAAP gross margin for the most recently completed quarter. Extract as a numerical value WITHOUT the % symbol (e.g., 46.1 instead of "46.1%").'
    )
    reported_quarter_gaap_operating_margin: Optional[float] = Field(
        description='The GAAP operating margin for the most recently completed quarter. Extract as a numerical value WITHOUT the % symbol (e.g., 15.7 instead of "15.7%").'
    )
    reported_quarter_non_gaap_operating_margin: Optional[float] = Field(
        description='The Non-GAAP operating margin for the most recently completed quarter. Extract as a numerical value WITHOUT the % symbol (e.g., 17.3 instead of "17.3%").'
    )
    next_quarter_revenue_growth_guidance: Optional[float] = Field(
        description='The management’s forecasted revenue growth for the upcoming quarter. If a range is provided (e.g., "12% to 14%"), calculate and return the midpoint (e.g., 13). If only one value is provided, return that value. Always return as a numerical value WITHOUT the % symbol.'
    )
    next_quarter_gaap_gross_margin_guidance: Optional[float] = Field(
        description='The management’s forecasted GAAP gross margin for the upcoming quarter. If a range is provided (e.g., "46% to 48%"), calculate and return the midpoint (e.g., 47). Always return as a numerical value WITHOUT the % symbol.'
    )
    next_quarter_non_gaap_gross_margin_guidance: Optional[float] = Field(
        description='The management’s forecasted Non-GAAP gross margin for the upcoming quarter. If a range is provided (e.g., "47% to 49%"), calculate and return the midpoint (e.g., 48). Always return as a numerical value WITHOUT the % symbol.'
    )
    next_quarter_gaap_operating_margin_guidance: Optional[float] = Field(
        description='The management’s forecasted GAAP operating margin for the upcoming quarter. If a range is provided (e.g., "15% to 17%"), calculate and return the midpoint (e.g., 16). Always return as a numerical value WITHOUT the % symbol.'
    )
    next_quarter_non_gaap_operating_margin_guidance: Optional[float] = Field(
        description='The management’s forecasted Non-GAAP operating margin for the upcoming quarter. If a range is provided (e.g., "18% to 20%"), calculate and return the midpoint (e.g., 19). Always return as a numerical value WITHOUT the % symbol.'
    )
    crpo_total: Optional[float] = Field(
        description="The total Current Remaining Performance Obligations (CRPO) for the most recently completed quarter. Extract the numerical value ONLY as a precise number (e.g., 1200000000 for $1.2B, 500000000 for €500M)."
    )
    billings_total: Optional[float] = Field(
        description="The total Billings for the most recently completed quarter. Extract the numerical value ONLY as a precise number (e.g., 1200000000 for $1.2B, 500000000 for €500M)."
    )
    backlog_total: Optional[float] = Field(
        description="The total Backlog for the most recently completed quarter. Extract the numerical value ONLY as a precise number (e.g., 1200000000 for $1.2B, 500000000 for €500M)."
    )