import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../..")))

from src.database.factory import DatabaseFactory
from datetime import datetime, timedelta
from src.modules.earnings_message.processors.event_processor import process_single_earnings_event
from src.core.logging import get_logger, configure_logging
from src.core.constants import LOG_LEVEL

logger = get_logger(__name__)
configure_logging(log_level=LOG_LEVEL)
connection = DatabaseFactory.get_mongo_connection()
events_collection = connection.get_collection("public_investor_events_outputs")
earnings_analysis_collection = connection.get_collection("earnings_analysis")

def correct_earnings_messages(cutoff_date: datetime) -> None:
    with events_collection.find({
        "event_type": "earnings",
        "earnings_analysis": {"$exists": True},
        "date": {"$lte": cutoff_date}
    }).sort("date", -1).batch_size(100) as events:
        for event in events:
            upsert_doc = process_single_earnings_event(
                event, datetime.now() - timedelta(days=30), datetime.now()
            )

            if upsert_doc:
                earnings_analysis_collection.update_one(
                    {"event_id": event["event_id"]},
                    {"$set": upsert_doc},
                    upsert=True
                )

                events_collection.update_one(
                    {"_id": event["_id"]},
                    {"$set": {"earnings_analysis": upsert_doc["analysis"]}}
                )
                logger.info(f"Updated event {event['_id']}")
            
            else:
                logger.info(f"Failed to process event {event['_id']}")


if __name__ == "__main__":
    # 18 Sept 2025
    cutoff = datetime(2025, 9, 18)
    correct_earnings_messages(cutoff)