from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from datetime import datetime, timedelta, timezone
from src.core.logging import get_logger, configure_logging
from src.modules.earnings_message.processors.event_processor import process_single_earnings_event
from src.core.constants import LOG_LEVEL
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from pymongo import UpdateOne

logger = get_logger(__name__)
configure_logging(log_level=LOG_LEVEL)

openai_service = OpenAIService()
connection = DatabaseFactory().get_mongo_connection()
qna_collection = connection.get_collection("qnas")
quant_data_collection = connection.get_stock_collection("quant_data")
public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
earnings_analysis_collection = connection.get_collection("earnings_analysis")
eight_k_collection = connection.get_filings_collection("eight_k_filings")

def create_earnings_message(cutoff_date, lookback_days=3, max_workers=5):

    if isinstance(cutoff_date, str):
        end_date = datetime.strptime(cutoff_date, "%Y-%m-%d").replace(
            tzinfo=timezone.utc
        )
    else:
        end_date = (
            cutoff_date
            if cutoff_date.tzinfo
            else cutoff_date.replace(tzinfo=timezone.utc)
        )

    begin_date = end_date - timedelta(days=lookback_days)

    logger.info(
        f"Creating earnings analysis for events between {begin_date} and {end_date}."
    )

    with public_investor_events_outputs_collection.find({
        'event_type': 'earnings',
        'date': {'$gte': begin_date, '$lt': end_date}
    }, no_cursor_timeout=True).batch_size(100) as earnings_events:

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_event = {
                executor.submit(
                    process_single_earnings_event, event, begin_date, end_date
                ): event
                for event in earnings_events
            }
            batch_updates = []
            for future in as_completed(future_to_event):
                event = future_to_event[future]
                try:
                    batch_result = future.result()
                    if batch_result:
                        batch_updates.append(
                            UpdateOne(
                                {"event_id": event["_id"]},
                                {"$set": batch_result},
                                upsert=True,
                            )
                        )

                except Exception as e:
                    logger.error(f"Error processing event {event['_id']}: {e}")
            
            if batch_updates:
                earnings_analysis_collection.bulk_write(batch_updates)
                logger.info(f"Inserted/Updated {len(batch_updates)} earnings analysis records.")
    return
