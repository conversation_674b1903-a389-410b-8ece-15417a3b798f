import pandas as pd
from src.modules.earnings_message.core.fetch_eightk import fetch_eightk_filings
from src.modules.earnings_message.schemas.storage import QuarterlyData
from src.modules.earnings_message.schemas.mongodb import EightKFilingsData
from src.database.factory import DatabaseFactory
from datetime import datetime, timedelta

connection = DatabaseFactory.get_mongo_connection()
eight_k_collection = connection.get_filings_collection("eight_k_filings")


def fetch_historical_data(data: EightKFilingsData, event_date: datetime) -> tuple[QuarterlyData, QuarterlyData, QuarterlyData]:
    """
    Fetch and process historical quarterly financial data for comparison with current earnings.

    Args:
        data: Current quarter's 8-K filing data containing ticker, year, and quarter information.
        event_date: The date of the current earnings event, used to calculate filing date windows.

    Returns:
        A tuple containing:
        - current_data: QuarterlyData object for the current quarter with calculated growth metrics
        - q_minus_1_data: QuarterlyData object for the previous quarter (Q-1)
        - y_minus_1_data: QuarterlyData object for the same quarter last year (Y-1)

    Note:
        Fetches data for Q-1, Q-2, Q-5, Y-1, and Y-2 periods to calculate quarter-over-quarter
        and year-over-year growth metrics. Uses both MongoDB storage and SEC 8-K filings API.
    """
    current_period = pd.Period(f"{data['year']}{data['quarter']}") # Q2 2025
    q_minus_1 = current_period - 1 # Q1 2025
    q_minus_2 = current_period - 2 # Q4 2024
    y_minus_1 = current_period - 4 # Q2 2024
    q_minus_5 = current_period - 5 # Q1 2024
    y_minus_2 = current_period - 8 # Q2 2023

    q_minus_1_data = QuarterlyData(ticker=data["ticker"], quarter=f"Q{q_minus_1.quarter}", year=q_minus_1.year)
    q_minus_2_data = QuarterlyData(ticker=data["ticker"], quarter=f"Q{q_minus_2.quarter}", year=q_minus_2.year)
    y_minus_1_data = QuarterlyData(ticker=data["ticker"], quarter=f"Q{y_minus_1.quarter}", year=y_minus_1.year)
    q_minus_5_data = QuarterlyData(ticker=data["ticker"], quarter=f"Q{q_minus_5.quarter}", year=q_minus_5.year)
    y_minus_2_data = QuarterlyData(ticker=data["ticker"], quarter=f"Q{y_minus_2.quarter}", year=y_minus_2.year)

    q_minus_1_quant_doc = eight_k_collection.find_one({"fiscalperiod": f"{q_minus_1.year}-Q{q_minus_1.quarter}"})
    q_minus_2_quant_doc = eight_k_collection.find_one({"fiscalperiod": f"{q_minus_2.year}-Q{q_minus_2.quarter}"})
    y_minus_1_quant_doc = eight_k_collection.find_one({"fiscalperiod": f"{y_minus_1.year}-Q{y_minus_1.quarter}"})
    q_minus_5_quant_doc = eight_k_collection.find_one({"fiscalperiod": f"{q_minus_5.year}-Q{q_minus_5.quarter}"})
    y_minus_2_quant_doc = eight_k_collection.find_one({"fiscalperiod": f"{y_minus_2.year}-Q{y_minus_2.quarter}"})

    # Fetch 8-K filings within a week window around the expected filing date 
    q_minus_1_doc = fetch_eightk_filings(data["ticker"], q_minus_1, event_date - timedelta(days=95), event_date - timedelta(days=85))
    q_minus_2_doc = fetch_eightk_filings(data["ticker"], q_minus_2, event_date - timedelta(days=185), event_date - timedelta(days=175))
    y_minus_1_doc = fetch_eightk_filings(data["ticker"], y_minus_1, event_date - timedelta(days=370), event_date - timedelta(days=360))
    y_minus_2_doc = fetch_eightk_filings(data["ticker"], y_minus_2, event_date - timedelta(days=735), event_date - timedelta(days=725))
    q_minus_5_doc = fetch_eightk_filings(data["ticker"], q_minus_5, event_date - timedelta(days=460), event_date - timedelta(days=450))

    if q_minus_5_doc:
        q_minus_5_data.update_current_data(q_minus_5_doc)
        if q_minus_5_quant_doc:
            q_minus_5_data.revenue = q_minus_5_quant_doc.get("revenue")

    if q_minus_2_doc:
        q_minus_2_data.update_current_data(q_minus_2_doc)
        if q_minus_2_quant_doc:
            q_minus_2_data.revenue = q_minus_2_quant_doc.get("revenue")

    if q_minus_1_doc:
        q_minus_1_data.update_current_data(q_minus_1_doc)
        q_minus_1_data.calculate_growth_and_change(q_minus_1_doc, q_minus_2_data, q_minus_5_data)
        if q_minus_1_quant_doc:
            q_minus_1_data.revenue = q_minus_1_quant_doc.get("revenue")
    
    if y_minus_2_doc:
        y_minus_2_data.update_current_data(y_minus_2_doc)
        if y_minus_2_quant_doc:
            y_minus_2_data.revenue = y_minus_2_quant_doc.get("revenue")

    if y_minus_1_doc:
        y_minus_1_data.update_current_data(y_minus_1_doc)
        y_minus_1_data.calculate_growth_and_change(y_minus_1_doc, q_minus_5_data, y_minus_2_data)
        if y_minus_1_quant_doc:
            y_minus_1_data.revenue = y_minus_1_quant_doc.get("revenue")

    current_data = QuarterlyData(ticker=data["ticker"], quarter=data["quarter"], year=data["year"])
    current_data.update_current_data(data)
    current_data.calculate_growth_and_change(data, q_minus_1_data, y_minus_1_data)
    
    return current_data, q_minus_1_data, y_minus_1_data