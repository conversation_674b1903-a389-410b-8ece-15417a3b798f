from src.modules.earnings_message.schemas.mongodb import EightKFilingsData
from src.modules.earnings_message.schemas.structured_outputs import ExtractedMetrics
from src.modules.earnings_message.extractors.mda_extractor import extract_mda_information
from src.database.factory import DatabaseFactory
import pandas as pd
from src.core.constants import LOG_LEVEL
from src.core.logging import get_logger, configure_logging

logger = get_logger(__name__)
configure_logging(log_level=LOG_LEVEL)

connection = DatabaseFactory.get_mongo_connection()
earning_analysis_collection = connection.get_collection("earnings_analysis")
quant_collection = connection.get_stock_collection("quant_data")
qna_collection = connection.get_collection("qnas")

def enrich_eightk_data_with_mda(event_id: str, ticker: str, current_period: pd.Period, eight_k_data: EightKFilingsData) -> EightKFilingsData:
    """
    Enrich 8-K filing data with Management Discussion and Analysis (MDA) information.

    Retrieves MDA text from the QnA collection for a given earnings event and uses it
    to extract additional financial metrics that may be missing from the 8-K data.
    Only updates fields in the 8-K data that are currently None.

    Args:
        event_id: The unique identifier for the earnings event.
        ticker: The stock ticker symbol for the company.
        current_period: The reporting period (quarter/year) as a pandas Period object.
        eight_k_data: The existing 8-K filing data dictionary to be enriched.

    Returns:
        EightKFilingsData: The enriched 8-K data dictionary with additional metrics
        extracted from MDA. Returns original data if no MDA is found.
    """
    mda = []
    qnas = qna_collection.find(
        {"event_id": event_id, "section": "MDA"}, no_cursor_timeout=True
    )
    for qna in qnas:
        if qna["section"] == "MDA":
            mda.append(qna["answer"])

    if len(mda) == 0:
        logger.warning(f"No MDA found for {ticker} Q{current_period.quarter}-{current_period.year}")
        return eight_k_data

    mda_text = " ".join(mda)

    dummy_response = ExtractedMetrics(
        quarter=eight_k_data.get("quarter"),
        year=eight_k_data.get("year"),
        currency=eight_k_data.get("currency"),
        reported_quarter_revenue=eight_k_data.get("revenue"),
        reported_quarter_gaap_gross_margin=eight_k_data.get("gaap_gross_margin"),
        reported_quarter_non_gaap_gross_margin=eight_k_data.get("non_gaap_gross_margin"),
        reported_quarter_gaap_operating_margin=eight_k_data.get("gaap_operating_margin"),
        reported_quarter_non_gaap_operating_margin=eight_k_data.get("non_gaap_operating_margin"),
        next_quarter_revenue_growth_guidance=eight_k_data.get("revenue_growth_guidance"),
        next_quarter_gaap_gross_margin_guidance=eight_k_data.get("gaap_gross_margin_guidance"),
        next_quarter_non_gaap_gross_margin_guidance=eight_k_data.get("non_gaap_gross_margin_guidance"),
        next_quarter_gaap_operating_margin_guidance=eight_k_data.get("gaap_operating_margin_guidance"),
        next_quarter_non_gaap_operating_margin_guidance=eight_k_data.get("non_gaap_operating_margin_guidance"),
        crpo_total=eight_k_data.get("crpo_total"),
        billings_total=eight_k_data.get("billings_total"),
        backlog_total=eight_k_data.get("backlog_total")
    )

    extra_numbers = extract_mda_information(mda_text, current_period, dummy_response)
    
    # Map ExtractionResponse fields to EightKFilingsData fields
    field_mapping = {
        'reported_quarter_revenue': 'revenue',
        'reported_quarter_gaap_gross_margin': 'gaap_gross_margin',
        'reported_quarter_non_gaap_gross_margin': 'non_gaap_gross_margin',
        'reported_quarter_gaap_operating_margin': 'gaap_operating_margin',
        'reported_quarter_non_gaap_operating_margin': 'non_gaap_operating_margin',
        'next_quarter_revenue_growth_guidance': 'revenue_growth_guidance',
        'next_quarter_gaap_gross_margin_guidance': 'gaap_gross_margin_guidance',
        'next_quarter_non_gaap_gross_margin_guidance': 'non_gaap_gross_margin_guidance',
        'next_quarter_gaap_operating_margin_guidance': 'gaap_operating_margin_guidance',
        'next_quarter_non_gaap_operating_margin_guidance': 'non_gaap_operating_margin_guidance',
        'crpo_total': 'crpo_total',
        'billings_total': 'billings_total',
        'backlog_total': 'backlog_total'
    }
    
    # Update eight_k_data with mapped values only if they don't already exist
    for extraction_field, eight_k_field in field_mapping.items():
        if extraction_field in extra_numbers and extra_numbers[extraction_field] is not None:
            if eight_k_data.get(eight_k_field) is None:
                try:
                    eight_k_data[eight_k_field] = float(extra_numbers[extraction_field])
                except ValueError:
                    logger.warning(f"Could not convert {extra_numbers[extraction_field]} to float for {eight_k_field}")
    
    return eight_k_data