from datetime import datetime
from src.core.logging import get_logger, configure_logging
from src.database.factory import DatabaseFactory
from src.modules.earnings_message.core.fetch_eightk import fetch_eightk_filings
from src.modules.earnings_message.utilities.calculations import extract_quarter_and_year
from src.modules.earnings_message.schemas.mongodb import EightKFilingsData
from src.modules.earnings_message.processors.historical_data import (
    fetch_historical_data,
)
from src.modules.earnings_message.core.metrics import compute_financial_metrics
from src.modules.earnings_message.schemas.mongodb import EarningsAnalysisData
from src.modules.earnings_message.extractors.mda_extractor import extract_historical_mda_information
from src.modules.earnings_message.processors.mda_processor import (
    enrich_eightk_data_with_mda,
)
from src.modules.earnings_message.message_generation.generate_full_message import (
    generate_full_message,
)

logger = get_logger(__name__)
configure_logging()

connection = DatabaseFactory.get_mongo_connection()
eight_k_collection = connection.get_filings_collection("eight_k_filings")
earnings_analysis_collection = connection.get_collection("earnings_analysis")


def process_single_earnings_event(
    event: dict, start_date: datetime, end_date: datetime
) -> EarningsAnalysisData | None:
    """Process a single earnings event from the public_investor_events_outputs collection.

    Args:
        event (dict): Event document containing earnings information with fields like event_id, event_type, date, ticker, and title.
        start_date (datetime): Start date for fetching eightk filings data.
        end_date (datetime): End date for fetching eightk filings data.

    Returns:
        earnings_analysis_data (EarningsAnalysisData | None): Earnings analysis document to be upserted into the earnings_analysis collection.
    """

    event_id = event.get("event_id")
    if not event_id:
        logger.warning(f"Event ID not found for event: {event}")
        return

    if event.get("event_type") != "earnings":
        logger.info(f"Event with ID {event_id} is not an earnings event.")
        return

    event_date = event.get("date")
    if not event_date:
        logger.warning(f"Event date not found for event with ID {event_id}.")
        return

    ticker = event.get("ticker")
    if not ticker:
        logger.warning(f"Ticker not found for event with ID {event_id}.")
        return

    event_title = event.get("title")
    if not event_title:
        logger.warning(f"Event title not found for event with ID {event_id}.")
        return

    current_period = extract_quarter_and_year(event_title)

    if current_period is None:
        logger.warning(
            f"Could not extract quarter and year from event title: {event_title}"
        )
        return

    # Getting numbers from 8k filings data
    eight_k_doc = fetch_eightk_filings(ticker, current_period, start_date, end_date)

    if eight_k_doc:
        eight_k_data = EightKFilingsData(**eight_k_doc)
    else:
        logger.info(
            f"No 8k data found for {ticker} Q{current_period.quarter}-{current_period.year}"
        )
        eight_k_data = EightKFilingsData(
            event_id=event_id,
            ticker=ticker,
            quarter=f"Q{current_period.quarter}",
            year=current_period.year,
            currency="USD",
            revenue=None,
            gaap_gross_margin=None,
            non_gaap_gross_margin=None,
            gaap_operating_margin=None,
            non_gaap_operating_margin=None,
            revenue_growth_guidance=None,
            gaap_gross_margin_guidance=None,
            non_gaap_gross_margin_guidance=None,
            gaap_operating_margin_guidance=None,
            non_gaap_operating_margin_guidance=None,
            crpo_total=None,
            billings_total=None,
            backlog_total=None,
            filed_at=datetime.now(),
            cik="",
            accession_number="",
            filing_type="",
            document_url="",
            updated_at=datetime.now(),
        )

    combined_data = enrich_eightk_data_with_mda(
        event_id, ticker, current_period, eight_k_data
    )

    current_data, previous_quarter_data, previous_year_data = fetch_historical_data(
        combined_data, event_date
    )

    (
        revenue_figures,
        gross_margin_figures,
        operating_margin_figures,
        guidance_figures,
        crpo_billings_figures,
    ) = compute_financial_metrics(
        current_data, previous_quarter_data, previous_year_data
    )

    # Getting more data from MDA using LLM
    # if revenue_figures["current_yoy_growth"] is None or revenue_figures["current_qoq_growth"] is None:
    #     mda_extracted_figures = extract_historical_mda_information(
    #         ticker, current_period
    #     )
    #     if revenue_figures["current_yoy_growth"] is None:
    #         revenue_figures["current_yoy_growth"] = (
    #             mda_extracted_figures.get("current_yoy_growth")
    #         )
    #     if revenue_figures["current_qoq_growth"] is None:
    #         revenue_figures["current_qoq_growth"] = (
    #             mda_extracted_figures.get("current_qoq_growth")
    #         )


    full_message = generate_full_message(
        current_period,
        revenue_figures,
        gross_margin_figures,
        operating_margin_figures,
        guidance_figures,
        crpo_billings_figures,
    )

    if full_message == "":
        logger.warning(
            f"No analysis generated for {ticker} {combined_data['quarter']}-{combined_data['year']}"
        )
        return

    upsert_doc: EarningsAnalysisData = {
        "event_id": event_id,
        "ticker": ticker,
        "quarter": combined_data["quarter"],
        "year": combined_data["year"],
        "currency": combined_data["currency"],
        "analysis": full_message,
        "date": event_date,
        "created_at": datetime.now(),
        "reported_quarter_revenue": revenue_figures["reported_quarter_revenue"],
        "quarterly_revenue_growth_percent": revenue_figures["current_qoq_growth"],
        "yearly_revenue_growth_percent": revenue_figures["current_yoy_growth"],
        "reported_quarter_gross_margin": gross_margin_figures[
            "reported_quarter_gross_margin"
        ],
        "reported_quarter_operating_margin": operating_margin_figures[
            "reported_quarter_operating_margin"
        ],
        "next_quarter_revenue_growth_guidance": guidance_figures[
            "revenue_growth_guidance"
        ],
        "next_quarter_operating_margin_guidance": guidance_figures[
            "operating_margin_guidance"
        ],
        "next_quarter_gross_margin_guidance": guidance_figures["gross_margin_guidance"],
        "mark_for_deletion": False,
    }

    return upsert_doc
