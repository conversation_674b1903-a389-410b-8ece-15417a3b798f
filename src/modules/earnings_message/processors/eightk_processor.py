from typing import Optional
from src.modules.earnings_message.schemas.mongodb import EightKFilingsData
from src.modules.earnings_message.extractors.document_reader import extract_text_from_url
from src.modules.earnings_message.extractors.eightk_extractor import extract_information
from src.database.factory import DatabaseFactory
from datetime import datetime
from src.core.constants import LOG_LEVEL
from src.core.logging import get_logger, configure_logging
import time

logger = get_logger(__name__)
configure_logging(log_level=LOG_LEVEL)

connection = DatabaseFactory.get_mongo_connection()
earning_analysis_collection = connection.get_collection("earnings_analysis")
quant_collection = connection.get_stock_collection("quant_data")
qna_collection = connection.get_collection("qnas")

def process_eightk_filing(ticker: str, filing: dict) -> Optional[EightKFilingsData]:
    if not filing.get("filedAt") or not filing.get("cik") or not filing.get("accessionNo"):
        logger.warning(f"Missing required fields in filing {filing.get('accessionNo')}")
        return None

    # Extract EX-99.1 document URL efficiently
    ex_99_1_url = None
    document_format_files = filing.get("documentFormatFiles", [])
    for doc in document_format_files:
        if doc.get("type", "").startswith("EX-99"):
            ex_99_1_url = doc.get("documentUrl")
            break  # Stop once we find the first EX-99.1
    
    if not ex_99_1_url:
        logger.warning(f"No EX-99.1 document found for filing {filing.get('accessionNo')}")
        return None

    filings_text = extract_text_from_url(ex_99_1_url)
    if not filings_text:
        logger.warning(f"Failed to extract text from EX-99.1 for filing {filing.get('accessionNo')}")
        return None
    filings_data = extract_information(filings_text)
    time.sleep(10) # remove this later

    if not filings_data.quarter or not filings_data.year or not filings_data.currency:
        logger.warning(f"Missing required fields in extracted data for filing {filing.get('accessionNo')}")
        return None

    # if not filings_data.reported_quarter_revenue:
    #     quant = quant_collection.find_one({"ticker": ticker, "fiscalperiod": f"{filings_data.year}-{filings_data.quarter}"})
    #     if quant:
    #         filings_data.reported_quarter_revenue = quant.get("revenue")

    event = earning_analysis_collection.find_one(
        {"ticker": ticker, "quarter": filings_data.quarter, "year": filings_data.year}
    )

    # Process and store each filing
    return EightKFilingsData(
        event_id = event.get("event_id") if event else None,
        ticker = ticker,
        quarter = filings_data.quarter,
        year = filings_data.year,
        currency = filings_data.currency,
        revenue = filings_data.reported_quarter_revenue,
        gaap_gross_margin = filings_data.reported_quarter_gaap_gross_margin,
        non_gaap_gross_margin = filings_data.reported_quarter_non_gaap_gross_margin,
        gaap_operating_margin = filings_data.reported_quarter_gaap_operating_margin,
        non_gaap_operating_margin = filings_data.reported_quarter_non_gaap_operating_margin,
        revenue_growth_guidance = filings_data.next_quarter_revenue_growth_guidance,
        gaap_gross_margin_guidance = filings_data.next_quarter_gaap_gross_margin_guidance,
        non_gaap_gross_margin_guidance = filings_data.next_quarter_non_gaap_gross_margin_guidance,
        gaap_operating_margin_guidance = filings_data.next_quarter_gaap_operating_margin_guidance,
        non_gaap_operating_margin_guidance = filings_data.next_quarter_non_gaap_operating_margin_guidance,
        crpo_total = filings_data.crpo_total,
        billings_total = filings_data.billings_total,
        backlog_total = filings_data.backlog_total,
        filed_at = filing["filedAt"],
        cik = filing["cik"],
        accession_number = filing["accessionNo"],
        filing_type = "EX-99.1",
        document_url = ex_99_1_url,
        updated_at = datetime.now()
    )