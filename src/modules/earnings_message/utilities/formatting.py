def format_number(value: float, decimals: int = 2) -> str:
    """Smart number formatting for financial reports."""
    if abs(value) < 0.1 and value != 0:
        abs_val = abs(value)
        if abs_val >= 0.01:
            return f"{value:.2f}"
        elif abs_val >= 0.001:
            return f"{value:.3f}"
        elif abs_val >= 0.0001:
            return f"{value:.4f}"
        else:
            return f"{value:.{decimals}f}"  # fallback
    return f"{value:.1f}"

def format_change(value: float, is_percent: bool = True) -> str:
    """Format change values with appropriate verb (increased/decreased)."""
    verb = "increased" if value >= 0 else "decreased"
    unit = "%" if is_percent else " basis points (bps)"
    return f"{verb} {format_number(abs(value))}{unit}"


def format_change_value(value: float, is_percent: bool = True) -> str:
    """Format just the change value with sign."""
    sign = "+" if value >= 0 else "-"
    unit = "%" if is_percent else " bps"
    return f"{sign}{format_number(abs(value), decimals=0)}{unit}"