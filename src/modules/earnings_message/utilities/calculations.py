from typing import Optional
import pandas as pd

def calculate_growth(previous_value: Optional[float], current_value: Optional[float]) -> Optional[float]:
    if previous_value is None or current_value is None:
        return None
    if previous_value == 0:
        return None
    growth = ((current_value - previous_value) / previous_value) * 100
    return growth if growth != 0 else None

def calculate_bps_change(previous_value: Optional[float], current_value: Optional[float]) -> Optional[float]:
    if previous_value is None or current_value is None:
        return None
    bps_change =  (current_value - previous_value) * 100
    return bps_change if bps_change != 0 else None

def calculate_change(previous_value: Optional[float], current_value: Optional[float]) -> Optional[float]:
    if previous_value is None or current_value is None:
        return None
    change = current_value - previous_value
    return change if change != 0 else None

def extract_quarter_and_year(event_title) -> pd.Period|None:
    quarter = None
    year = None
    if "Q1" in event_title:
        quarter = "Q1"
    elif "Q2" in event_title:
        quarter = "Q2"
    elif "Q3" in event_title:
        quarter = "Q3"
    elif "Q4" in event_title:
        quarter = "Q4"
    if "2029" in event_title:
        year = 2029
    elif "2028" in event_title:
        year = 2028
    elif "2027" in event_title:
        year = 2027
    elif "2026" in event_title:
        year = 2026
    elif "2025" in event_title:
        year = 2025
    elif "2024" in event_title:
        year = 2024
    elif "2023" in event_title:
        year = 2023
    
    if quarter and year:
        return pd.Period(f"{year}{quarter}")
    return None