import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../..")))

from pymongo import DESCENDING
from src.database.factory import DatabaseFactory
from datetime import datetime
import re
from bson import ObjectId

connection = DatabaseFactory.get_mongo_connection()

earnings_analysis_collection = connection.get_collection("earnings_analysis")
public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")

events = list(public_investor_events_outputs_collection.find({
    'event_type': 'earnings', 'earnings_analysis': {'$exists': False}
    }).sort('date', DESCENDING))

for event in events:
    earnings_analysis_docs = earnings_analysis_collection.find({'event_id': event['event_id']})
    for earnings_analysis_doc in earnings_analysis_docs:
        try:
            analysis = earnings_analysis_doc['analysis']
            summary = event['summary']
            summary_bullet = event['summary_bullet']
            cleaned_summary = summary.replace(analysis.strip(), "").strip()
            pattern = rf"-\s*{re.escape(analysis.strip())}(\s*\n\n)?"
            cleaned_summary_bullet = re.sub(pattern, "", summary_bullet).strip()
            public_investor_events_outputs_collection.update_one({
                'event_id': event['event_id']}, 
                {'$set': {'summary': cleaned_summary, 'summary_bullet': cleaned_summary_bullet, 'earnings_analysis': analysis}
            })
            
        except Exception as e:
            print(f"Error for event_id: {event['event_id']}")
            print(e)