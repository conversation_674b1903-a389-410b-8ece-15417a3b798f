from src.core.logging import get_logger, configure_logging
from src.core.constants import LOG_LEVEL
from src.database.factory import DatabaseFactory
from src.modules.earnings_message.processors.eightk_processor import process_eightk_filing

logger = get_logger(__name__)
configure_logging(log_level=LOG_LEVEL)

connection = DatabaseFactory.get_mongo_connection()
eight_k_collection = connection.get_filings_collection("eight_k_filings")
earning_analysis_collection = connection.get_collection("earnings_analysis")
quant_collection = connection.get_stock_collection("quant_data")

def store_eightk_data(eight_k_filings: list, ticker: str) -> None:
    for filing in eight_k_filings:
        eight_k_data = process_eightk_filing(ticker, filing)
        if not eight_k_data:
            logger.warning(f"Could not process filing for {ticker}: {filing}")
            continue

        # Update to avoid duplicates
        try:
            eight_k_collection.update_one(
                {"ticker": ticker, "quarter": eight_k_data["quarter"], "year": eight_k_data["year"]},
                {"$set": eight_k_data},
                upsert=True
            )
        except Exception as e:
            logger.error(f"Error storing 8k data for {ticker}: {e}")