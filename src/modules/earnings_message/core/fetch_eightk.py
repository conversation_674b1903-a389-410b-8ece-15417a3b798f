from src.database.factory import DatabaseFactory
from src.services.sec_api import search_8k_filings
from src.modules.earnings_message.core.store_eightk import store_eightk_data
from src.modules.earnings_message.utilities.calculations import extract_quarter_and_year
from src.modules.earnings_message.schemas.mongodb import EightKFilingsData
import pandas as pd
from datetime import datetime
from src.core.logging import get_logger, configure_logging

logger = get_logger(__name__)
configure_logging()

connection = DatabaseFactory.get_mongo_connection()
eight_k_collection = connection.get_filings_collection("eight_k_filings")

def fetch_eightk_filings(ticker: str, period: pd.Period, start_date: datetime, end_date: datetime) -> EightKFilingsData | None:
    """
    Fetch 8-K filings data for a specific ticker and period.

    Retrieves 8-K filings data from the database for the given ticker and period.
    If the data doesn't exist, it will fetch the filings from the SEC API and store them.

    Args:
        ticker (str): The stock ticker symbol.
        period (pd.Period): The period object containing quarter and year information.
        start_date (datetime): The start date for filing search.
        end_date (datetime): The end date for filing search.

    Returns:
        eight_k_data ( EightKFilingsData | None): The 8-K filings document if found, None otherwise.
    """
    # Getting numbers from 8k filings data
    eight_k_doc = eight_k_collection.find_one({
            "ticker": ticker,
            "quarter": f"Q{period.quarter}",
            "year": period.year,
    })

    # If 8k collection doesn't exist, create it
    if not eight_k_doc:
        logger.info(f"Creating 8k collection for {ticker} Q{period.quarter}-{period.year}")
        filings = search_8k_filings(ticker, start_date, end_date)
        store_eightk_data(filings, ticker)
    
        # Checking if 8k doc exists after creation
        eight_k_doc = eight_k_collection.find_one({
                "ticker": ticker,
                "quarter": f"Q{period.quarter}",
                "year": period.year,
        })

    return eight_k_doc