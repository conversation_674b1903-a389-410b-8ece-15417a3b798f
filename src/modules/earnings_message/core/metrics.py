from src.modules.earnings_message.schemas.figures import RevenueFigures, GrossMarginFigures, OperatingMarginFigures, GuidanceFigures, CRPOBillingsFigures
from src.modules.earnings_message.schemas.storage import QuarterlyData
from src.modules.earnings_message.utilities.calculations import calculate_change, calculate_bps_change
from typing import Optional, <PERSON><PERSON>

def compute_financial_metrics(
    current_data: QuarterlyData,
    previous_quarter_data: QuarterlyData,
    previous_year_data: QuarterlyData,
) -> Tuple[RevenueFigures, GrossMarginFigures, OperatingMarginFigures, GuidanceFigures, CRPOBillingsFigures]:
    """
    Compute comprehensive financial metrics for earnings analysis.

    Args:
        current_data: QuarterlyData object containing the current quarter's financial data.
        previous_quarter_data: QuarterlyData object containing the previous quarter's (Q-1) financial data.
        previous_year_data: QuarterlyData object containing the same quarter from previous year's (Y-1) financial data.

    Returns:
        A tuple containing:
        - RevenueFigures: Revenue metrics including YoY/QoQ growth rates
        - GrossMarginFigures: Gross margin metrics with period-over-period changes
        - OperatingMarginFigures: Operating margin metrics with period-over-period changes
        - GuidanceFigures: Forward-looking guidance metrics with comparisons
        - CRPOBillingsFigures: CRPO, billings, and backlog metrics with growth rates

    Note:
        Prioritizes Non-GAAP metrics when available, falling back to GAAP metrics.
        Calculates basis point changes for margin comparisons and percentage changes for growth metrics.
    """
    
    revenue = RevenueFigures(
        reported_quarter_revenue=current_data.revenue,
        current_yoy_growth=current_data.revenue_yoy_growth,
        previous_yoy_growth=previous_year_data.revenue_yoy_growth,
        current_qoq_growth=current_data.revenue_qoq_growth,
        previous_qoq_growth=previous_quarter_data.revenue_qoq_growth,
        y_minus_1_qoq_growth=previous_year_data.revenue_qoq_growth,
        q_minus_1_yoy_growth=previous_quarter_data.revenue_yoy_growth,
    )

    # If Non-GAAP gross margin is available use that across the board, otherwise use GAAP gross margin
    if current_data.non_gaap_gross_margin is not None:
        current_gross_margin = current_data.non_gaap_gross_margin
        current_gross_margin_qoq_change = current_data.non_gaap_gross_margin_qoq_change
        current_gross_margin_yoy_change = current_data.non_gaap_gross_margin_yoy_change
        current_gross_margin_guidance = current_data.non_gaap_gross_margin_guidance
        gross_margin_vs_prev_yoy_bps = calculate_bps_change(previous_year_data.non_gaap_gross_margin, current_gross_margin_guidance)
        gross_margin_vs_prev_qoq_bps = calculate_bps_change(previous_quarter_data.non_gaap_gross_margin, current_gross_margin_guidance)

    else:
        current_gross_margin = current_data.gaap_gross_margin
        current_gross_margin_qoq_change = current_data.gaap_gross_margin_qoq_change
        current_gross_margin_yoy_change = current_data.gaap_gross_margin_yoy_change
        current_gross_margin_guidance = current_data.gaap_gross_margin_guidance
        gross_margin_vs_prev_yoy_bps = calculate_bps_change(previous_year_data.gaap_gross_margin, current_gross_margin_guidance)
        gross_margin_vs_prev_qoq_bps = calculate_bps_change(previous_quarter_data.gaap_gross_margin, current_gross_margin_guidance)


    gross_margin = GrossMarginFigures(
        reported_quarter_gross_margin=current_gross_margin,
        yoy_margin_change=current_gross_margin_yoy_change,
        qoq_margin_change=current_gross_margin_qoq_change
    )

    # If Non-GAAP operating margin is available use that across the board, otherwise use GAAP operating margin
    if current_data.non_gaap_operating_margin is not None:
        current_operating_margin = current_data.non_gaap_operating_margin
        current_operating_margin_qoq_change = current_data.non_gaap_operating_margin_qoq_change
        current_operating_margin_yoy_change = current_data.non_gaap_operating_margin_yoy_change
        current_operating_margin_guidance = current_data.non_gaap_operating_margin_guidance
        operating_margin_vs_prev_yoy_bps = calculate_bps_change(previous_year_data.non_gaap_operating_margin, current_operating_margin_guidance)
        operating_margin_vs_prev_qoq_bps = calculate_bps_change(previous_quarter_data.non_gaap_operating_margin, current_operating_margin_guidance)

    else:
        current_operating_margin = current_data.gaap_operating_margin
        current_operating_margin_qoq_change = current_data.gaap_operating_margin_qoq_change
        current_operating_margin_yoy_change = current_data.gaap_operating_margin_yoy_change
        current_operating_margin_guidance = current_data.gaap_operating_margin_guidance
        operating_margin_vs_prev_yoy_bps = calculate_bps_change(previous_year_data.gaap_operating_margin, current_operating_margin_guidance)
        operating_margin_vs_prev_qoq_bps = calculate_bps_change(previous_quarter_data.gaap_operating_margin, current_operating_margin_guidance)

    operating_margin = OperatingMarginFigures(
        reported_quarter_operating_margin=current_operating_margin,
        yoy_margin_change=current_operating_margin_yoy_change,
        qoq_margin_change= current_operating_margin_qoq_change
    )

    guidance = GuidanceFigures(
        revenue_growth_guidance=current_data.revenue_growth_guidance,
        revenue_growth_vs_prev_yoy=calculate_change(previous_year_data.revenue_yoy_growth, current_data.revenue_growth_guidance),
        revenue_growth_vs_prev_qoq=calculate_change(previous_quarter_data.revenue_yoy_growth, current_data.revenue_growth_guidance),
        gross_margin_guidance=current_gross_margin_guidance,
        gross_margin_vs_prev_yoy_bps=gross_margin_vs_prev_yoy_bps,
        gross_margin_vs_prev_qoq_bps=gross_margin_vs_prev_qoq_bps,
        operating_margin_guidance=current_operating_margin_guidance,
        operating_margin_vs_prev_yoy_bps=operating_margin_vs_prev_yoy_bps,
        operating_margin_vs_prev_qoq_bps=operating_margin_vs_prev_qoq_bps,
    )

    crpo_billings = CRPOBillingsFigures(
        current_crpo=current_data.crpo_total,
        current_billings=current_data.billings_total,
        current_backlog=current_data.backlog_total,
        crpo_qoq_growth=current_data.crpo_qoq_growth,
        billings_qoq_growth=current_data.billings_qoq_growth,
        backlog_qoq_growth=current_data.backlog_qoq_growth,
        crpo_yoy_growth=current_data.crpo_yoy_growth,
        billings_yoy_growth=current_data.billings_yoy_growth,
        backlog_yoy_growth=current_data.backlog_yoy_growth,
        previous_crpo_qoq_growth=previous_quarter_data.crpo_qoq_growth,
        previous_billings_qoq_growth=previous_quarter_data.billings_qoq_growth,
        previous_backlog_qoq_growth=previous_quarter_data.backlog_qoq_growth,
        previous_crpo_yoy_growth=previous_year_data.crpo_yoy_growth,
        previous_billings_yoy_growth=previous_year_data.billings_yoy_growth,
        previous_backlog_yoy_growth=previous_year_data.backlog_yoy_growth,
    )

    return revenue, gross_margin, operating_margin, guidance, crpo_billings