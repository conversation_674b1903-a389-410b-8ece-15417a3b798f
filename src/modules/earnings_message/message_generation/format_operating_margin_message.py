from src.modules.earnings_message.schemas.figures import OperatingMarginFigures
from src.modules.earnings_message.utilities.formatting import format_change_value, format_number

def generate_operating_margin_analysis(
    current_period: str, previous_quarter: str, previous_year: str, data: OperatingMarginFigures
) -> str | None:
    if not data["reported_quarter_operating_margin"]:
        return None

    parts = [f"Operating margins stood at {format_number(data['reported_quarter_operating_margin'])}% in {current_period}"]
    
    changes = []
    if data["yoy_margin_change"] is not None:
        changes.append(f"a {format_change_value(data['yoy_margin_change'], False)} change compared to {previous_year}")
    
    if data["qoq_margin_change"] is not None:
        conjunction = "and" if changes else "a"
        changes.append(f"{conjunction} {format_change_value(data['qoq_margin_change'], False)} change versus {previous_quarter}")
    
    if changes:
        parts.append(", ".join(changes))
    
    return ", ".join(parts) + "."