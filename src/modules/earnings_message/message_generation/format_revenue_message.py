from src.modules.earnings_message.schemas.figures import RevenueFigures
from src.modules.earnings_message.utilities.formatting import format_change, format_number

def generate_revenue_analysis(
    current_period: str, previous_quarter: str, previous_year: str, data: RevenueFigures
) -> str | None:

    components = []
    
    # YoY statement
    if data["current_yoy_growth"] is not None:
        yoy_parts = [f"In {current_period}, revenue {format_change(data['current_yoy_growth'])} year-over-year (YoY)"]
        if data["previous_yoy_growth"] is not None:
            yoy_parts.append(f"compared to {format_number(data['previous_yoy_growth'])}% growth in {previous_year}")
        if data["q_minus_1_yoy_growth"] is not None:
            yoy_parts.append(f"and {format_number(data['q_minus_1_yoy_growth'])}% in {previous_quarter}")
        components.append(", ".join(yoy_parts))

    # QoQ statement
    if data["current_qoq_growth"] is not None:
        qoq_parts = [f"Quarter-over-quarter (QoQ), revenue grew {format_number(data['current_qoq_growth'])}%"]
        if data["previous_qoq_growth"] is not None:
            qoq_parts.append(f"versus {format_number(data['previous_qoq_growth'])}% in {previous_quarter}")
        if data["y_minus_1_qoq_growth"] is not None:
            qoq_parts.append(f"and {format_number(data['y_minus_1_qoq_growth'])}% in {previous_year}")
        components.append(", ".join(qoq_parts))

    if len(components) == 0:
        return None
    
    return ". ".join(components) + "."