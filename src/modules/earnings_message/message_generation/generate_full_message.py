import pandas as pd
from src.modules.earnings_message.schemas.figures import (
    RevenueFigures,
    GrossMarginFigures,
    OperatingMarginFigures,
    GuidanceFigures,
    CRPOBillingsFigures,
)
from src.modules.earnings_message.schemas.mongodb import EightKFilingsData

from src.modules.earnings_message.message_generation.format_revenue_message import (
    generate_revenue_analysis,
)
from src.modules.earnings_message.message_generation.format_gross_margin_message import (
    generate_gross_margin_analysis,
)
from src.modules.earnings_message.message_generation.format_operating_margin_message import (
    generate_operating_margin_analysis,
)
from src.modules.earnings_message.message_generation.format_guidance_message import (
    generate_guidance_analysis,
)
from src.modules.earnings_message.message_generation.format_crpo_billings_message import (
    generate_crpo_billings_analysis,
)


def generate_full_message(
    current_period: pd.Period,
    revenue_figures: RevenueFigures,
    gross_margin_figures: GrossMarginFigures,
    operating_margin_figures: OperatingMarginFigures,
    guidance_figures: GuidanceFigures,
    crpo_billings_figures: CRPOBillingsFigures,
) -> str:
    """
    Generate a comprehensive earnings analysis message from financial metrics.

    Args:
        current_period: pandas Period object representing the current reporting quarter.
        revenue_figures: Revenue metrics including growth rates and comparisons.
        gross_margin_figures: Gross margin data with period-over-period changes.
        operating_margin_figures: Operating margin data with period-over-period changes.
        guidance_figures: Forward-looking guidance metrics and comparisons.
        crpo_billings_figures: CRPO, billings, and backlog metrics with growth rates.

    Returns:
        A formatted string containing the complete earnings analysis message with sections for:
        Revenue, Gross Margin, Operating Margin, Guidance, and CRPO/Billings.
        Each section is prefixed with bold markdown formatting and separated by spaces.
        Empty sections are omitted from the final message.
    """
    current_quarter = f"Q{current_period.quarter}-{current_period.year}"
    previous_year = f"Q{(current_period - 4).quarter}-{(current_period - 4).year}"
    previous_quarter = f"Q{(current_period - 1).quarter}-{(current_period - 1).year}"
    next_quarter = f"Q{(current_period + 1).quarter}-{(current_period + 1).year}"

    full_message = ""
    revenue_analysis = generate_revenue_analysis(
        current_quarter, previous_quarter, previous_year, revenue_figures
    )
    if revenue_analysis:
        full_message += "**Revenue**: " + revenue_analysis + " "
    gross_margin_analysis = generate_gross_margin_analysis(
        current_quarter, previous_quarter, previous_year, gross_margin_figures
    )
    if gross_margin_analysis:
        full_message += "**Gross Margin**: " + gross_margin_analysis + " "
    operating_margin_analysis = generate_operating_margin_analysis(
        current_quarter, previous_quarter, previous_year, operating_margin_figures
    )
    if operating_margin_analysis:
        full_message += "**Operating Margin**: " + operating_margin_analysis + " "
    guidance_analysis = generate_guidance_analysis(
        current_quarter, previous_quarter, next_quarter, guidance_figures
    )
    if guidance_analysis:
        full_message += "**Guidance**: " + guidance_analysis + " "
    crpo_billings_analysis = generate_crpo_billings_analysis(
        current_quarter, previous_quarter, crpo_billings_figures
    )
    if crpo_billings_analysis:
        full_message += "**CRPO/Billings**: " + crpo_billings_analysis + " "

    return full_message
