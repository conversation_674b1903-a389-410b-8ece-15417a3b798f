from src.modules.earnings_message.schemas.figures import GuidanceFigures
from src.modules.earnings_message.utilities.formatting import format_change_value, format_number

def generate_guidance_analysis(
    current_period: str,        # e.g., "Q2-2025" (reported)
    previous_quarter: str,      # e.g., "Q1-2025"
    next_quarter: str,          # e.g., "Q3-2025" (guided)
    data: GuidanceFigures
) -> str | None:
    parts: list[str] = []

    # ---- Revenue guidance ----
    if data.get("revenue_growth_guidance") is not None:
        rev_g = data["revenue_growth_guidance"]
        rev_sentence = [f"For {next_quarter}, revenue is expected to grow {format_number(rev_g)}% QoQ"]

        # Prefer the delta vs prior quarter's YoY, else vs prior year's YoY if available
        delta_pp = None
        delta_base_label = None
        if data.get("revenue_growth_vs_prev_qoq") is not None:
            delta_pp = data["revenue_growth_vs_prev_qoq"]
            delta_base_label = f"{previous_quarter}’s QoQ growth"
        elif data.get("revenue_growth_vs_prev_yoy") is not None:
            delta_pp = data["revenue_growth_vs_prev_yoy"]
            delta_base_label = "the same quarter last year"

        if delta_pp is not None and delta_base_label is not None:
            direction = "acceleration" if delta_pp > 0 else "deceleration" if delta_pp < 0 else "no change"
            if direction == "no change":
                rev_sentence.append(f", implying no change from {delta_base_label}")
            else:
                # use percentage points (pp) wording for growth delta
                rev_sentence.append(
                    f", implying a {format_number(abs(delta_pp))} pp {direction} from {delta_base_label}"
                )

        parts.append("".join(rev_sentence) + ".")

    # ---- Gross margin guidance ----
    if data.get("gross_margin_guidance") is not None:
        gm_g = data["gross_margin_guidance"]
        gm_sentence = [f"Gross margins for {next_quarter} are guided at {format_number(gm_g)}%"]

        gm_qoq_bps = data.get("gross_margin_vs_prev_qoq_bps")
        gm_yoy_bps = data.get("gross_margin_vs_prev_yoy_bps")

        changes = []
        if gm_qoq_bps is not None:
            changes.append(f"{format_change_value(gm_qoq_bps, is_percent=False)} versus {previous_quarter}")
        if gm_yoy_bps is not None:
            # We don't have the label for previous year period in params; phrase generically
            conj = "and " if changes else ""
            changes.append(f"{conj}{format_change_value(gm_yoy_bps, is_percent=False)} versus the same quarter last year")

        if changes:
            gm_sentence.append(", " + " ".join(changes))

        parts.append("".join(gm_sentence) + ".")

    # ---- Operating margin guidance ----
    if data.get("operating_margin_guidance") is not None:
        op_g = data["operating_margin_guidance"]
        op_sentence = [f"Operating margins for {next_quarter} are guided at {format_number(op_g)}%"]

        op_qoq_bps = data.get("operating_margin_vs_prev_qoq_bps")
        op_yoy_bps = data.get("operating_margin_vs_prev_yoy_bps")

        changes = []
        if op_qoq_bps is not None:
            changes.append(f"{format_change_value(op_qoq_bps, is_percent=False)} versus {previous_quarter}")
        if op_yoy_bps is not None:
            conj = "and " if changes else ""
            changes.append(f"{conj}{format_change_value(op_yoy_bps, is_percent=False)} versus the same quarter last year")

        if changes:
            op_sentence.append(", " + " ".join(changes))

        parts.append("".join(op_sentence) + ".")

    if not parts:
        return None

    return " ".join(parts)