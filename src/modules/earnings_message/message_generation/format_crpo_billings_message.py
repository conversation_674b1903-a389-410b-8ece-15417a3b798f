from src.modules.earnings_message.schemas.figures import CRPOBillingsFigures
from src.modules.earnings_message.utilities.formatting import format_change, format_number

def generate_crpo_billings_analysis(current_period: str, previous_quarter: str, data: CRPOBillingsFigures) -> str | None:
    parts = []
    
    # CRPO analysis
    current_crpo = data.get("current_crpo")
    if current_crpo is not None:
        crpo_components = []
        crpo_value = current_crpo / 1_000_000_000
        crpo_components.append(f"cRPO totaled ${format_number(crpo_value, 1)}B")
        
        growth_statements = []
        # YoY growth
        crpo_yoy = data.get("crpo_yoy_growth")
        if crpo_yoy is not None:
            yoy_text = f"{format_change(crpo_yoy)} YoY"
            prev_crpo_yoy = data.get("previous_crpo_yoy_growth")
            if prev_crpo_yoy is not None:
                trend = "accelerating" if abs(crpo_yoy) > abs(prev_crpo_yoy) else "decelerating"
                yoy_text += f" ({trend} from {format_number(abs(prev_crpo_yoy))}%)"
            growth_statements.append(yoy_text)
        
        # QoQ growth
        crpo_qoq = data.get("crpo_qoq_growth")
        if crpo_qoq is not None:
            qoq_text = f"{format_change(crpo_qoq)} QoQ"
            prev_crpo_qoq = data.get("previous_crpo_qoq_growth")
            if prev_crpo_qoq is not None:
                trend = "accelerating" if abs(crpo_qoq) > abs(prev_crpo_qoq) else "decelerating"
                qoq_text += f" ({trend} from {format_number(abs(prev_crpo_qoq))}%)"
            growth_statements.append(qoq_text)
        
        if growth_statements:
            crpo_components.append(", ".join(growth_statements))
        
        parts.append(" ".join(crpo_components))
    
    # Billings analysis
    current_billings = data.get("current_billings")
    if current_billings is not None:
        billings_components = []
        billings_value = current_billings / 1_000_000_000
        billings_components.append(f"Billings reached ${format_number(billings_value, 1)}B")
        
        growth_statements = []
        # YoY growth
        billings_yoy = data.get("billings_yoy_growth")
        if billings_yoy is not None:
            yoy_text = f"growing {format_number(abs(billings_yoy))}% YoY"
            prev_billings_yoy = data.get("previous_billings_yoy_growth")
            if prev_billings_yoy is not None:
                if abs(billings_yoy) > abs(prev_billings_yoy):
                    yoy_text += f" (up from {format_number(abs(prev_billings_yoy))}%)"
                else:
                    yoy_text += f" (down from {format_number(abs(prev_billings_yoy))}%)"
            growth_statements.append(yoy_text)
        
        # QoQ growth
        billings_qoq = data.get("billings_qoq_growth")
        if billings_qoq is not None:
            qoq_text = f"{format_number(abs(billings_qoq))}% QoQ"
            prev_billings_qoq = data.get("previous_billings_qoq_growth")
            if prev_billings_qoq is not None:
                comparison = "vs" 
                qoq_text += f" ({comparison} {format_number(abs(prev_billings_qoq))}% in {previous_quarter})"
            growth_statements.append(qoq_text)
        
        if growth_statements:
            billings_components.append(", ".join(growth_statements))
        
        parts.append(" ".join(billings_components))
    
    # Backlog analysis
    current_backlog = data.get("current_backlog")
    if current_backlog is not None:
        backlog_components = []
        backlog_value = current_backlog / 1_000_000_000
        backlog_components.append(f"Backlog stood at ${format_number(backlog_value, 1)}B")
        
        growth_statements = []
        # YoY growth
        backlog_yoy = data.get("backlog_yoy_growth")
        if backlog_yoy is not None and backlog_yoy != 0:
            yoy_text = f"{format_change(backlog_yoy)} YoY"
            prev_backlog_yoy = data.get("previous_backlog_yoy_growth")
            if prev_backlog_yoy is not None:
                trend_word = "from" if backlog_yoy * prev_backlog_yoy > 0 else "compared to"
                yoy_text += f" ({trend_word} {format_number(abs(prev_backlog_yoy))}% growth)"
            growth_statements.append(yoy_text)
        
        # QoQ growth  
        backlog_qoq = data.get("backlog_qoq_growth")
        if backlog_qoq is not None and backlog_qoq != 0:
            qoq_text = f"{format_change(backlog_qoq)} QoQ"
            prev_backlog_qoq = data.get("previous_backlog_qoq_growth")
            if prev_backlog_qoq is not None:
                trend_word = "from" if backlog_qoq * prev_backlog_qoq > 0 else "versus"
                qoq_text += f" ({trend_word} {format_number(abs(prev_backlog_qoq))}% in {previous_quarter})"
            growth_statements.append(qoq_text)
        
        if growth_statements:
            backlog_components.append(", ".join(growth_statements))
        
        parts.append(" ".join(backlog_components))
    
    if not parts:
        return None
    
    return ". ".join(parts) + "."