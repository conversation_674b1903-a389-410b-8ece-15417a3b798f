from src.modules.earnings_message.schemas.figures import GrossMarginFigures
from src.modules.earnings_message.utilities.formatting import format_change_value, format_number

def generate_gross_margin_analysis(
    current_period: str, previous_quarter: str, previous_year: str, data: GrossMarginFigures
) -> str | None:
    if not data["reported_quarter_gross_margin"]:
        return None

    parts = [f"Gross margins for {current_period} were {format_number(data['reported_quarter_gross_margin'])}%"]
    
    if data["yoy_margin_change"] is not None or data["qoq_margin_change"] is not None:
        parts.append("representing")
        
        changes = []
        if data["yoy_margin_change"] is not None:
            changes.append(f"a {format_change_value(data['yoy_margin_change'], False)} change from {previous_year}")
        
        if data["qoq_margin_change"] is not None:
            conjunction = "and" if changes else ""
            changes.append(f"{conjunction} a {format_change_value(data['qoq_margin_change'], False)} change from {previous_quarter}")
        
        parts.append(" ".join(changes).strip())
    
    return " ".join(parts) + "."