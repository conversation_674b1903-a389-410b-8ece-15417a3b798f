import uuid
import time
import traceback
from datetime import datetime, timedelta

from src.core.logging import get_logger
from src.core.constants import LOOKBACK_DAYS
from src.database.factory import DatabaseFactory

from src.modules.input_collections.qna import process_investor_events_for_qna
from src.modules.input_collections.upload_sp_events_to_s3 import upload_sp_events_to_s3
from src.modules.input_collections.snowflake_to_mongo_transcript_etl import snowflake_create_public_investor_events_collection_pooled

from src.modules.process_collections.create_embeddings import create_embeddings_collection
from src.modules.process_collections.create_entailment_collection import create_entailment_collection
from src.modules.process_collections.create_llm_answer_collection import create_llm_answer_collection
from src.modules.process_collections.create_similar_questions import create_similar_questions_collection
from src.modules.process_collections.create_similarity_collection import create_similarity_collection
from src.modules.process_collections.create_similarity_mda_collection import create_similarity_mda_collection

from src.modules.output_collections.add_llm_rationale_to_entailment import add_LLM_rationale_to_entailment
from src.modules.output_collections.create_trend_collection import create_trend_collection
from src.modules.output_collections.create_insights_collection import create_insights_collection
from src.modules.output_collections.create_category_collection import create_category_collection
from src.modules.output_collections.assign_importance_to_category import assign_importance_to_category
from src.modules.output_collections.create_qna_flags_collection import create_qna_flags_collection
from src.modules.output_collections.create_output_qna_collection import create_output_qna_collection
from src.modules.output_collections.create_event_output_collection import create_event_output_collection
from src.modules.output_collections.create_earnings_anomalies_collection import create_earnings_anomalies_collection
from src.modules.output_collections.create_mda_outputs import create_mda_outputs
from src.modules.output_collections.create_event_summary import create_event_summary
from src.modules.output_collections.calculate_event_score import calculate_event_scores
from src.modules.output_collections.create_event_transcript_summary import create_event_transcript_summary
from src.modules.output_collections.create_insights_mda_collection import create_insights_mda_collection
from src.modules.output_collections.create_trend_mda_collection import create_mda_trend_collection
from src.modules.output_collections.rank_insights import rank_insights
from src.modules.earnings_message.main import create_earnings_message

# Not necessarily part of output collection
from src.modules.output_collections.create_email_collection import create_email_collection
from src.modules.output_collections.create_earnings_reaction_update import create_earnings_reaction_update


logger = get_logger(__name__)


def log_step_time(execution_metrics_collection, run_id, step_name, duration):
    """Log the execution time for a step to MongoDB"""
    execution_metrics_collection.update_one(
        {"run_id": run_id},
        {"$set": {f"steps.{step_name}": duration}},
        upsert=True
    )


def log_step_error(execution_metrics_collection, run_id, error):
    """Log an error for a step to MongoDB"""
    error_info = {
        "error_message": str(error),
        "error_type": type(error).__name__,
        "traceback": traceback.format_exc()
    }
    execution_metrics_collection.update_one(
        {"run_id": run_id},
        {
            "$set": {
                "error": error_info,
                "status": "failed"
            }
        },
        upsert=True
    )

def update_input_collections(cutoff_date, execution_metrics_collection, run_id):
    step_start = time.time()
    num_events, earnings_count, non_earnings_count, events_processed = snowflake_create_public_investor_events_collection_pooled(cutoff_date, LOOKBACK_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "snowflake_create_public_investor_events", duration)
    logger.info(f"create_public_investor_events_collection done in {duration:.2f}s")
    
    step_start = time.time()
    num_qnas = process_investor_events_for_qna(cutoff_date, LOOKBACK_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "process_investor_events_for_qna", duration)
    logger.info(f"create_qnas_collection done in {duration:.2f}s")
    
    step_start = time.time()
    upload_sp_events_to_s3(cutoff_date, LOOKBACK_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "upload_sp_events_to_s3", duration)
    logger.info(f"create_s3_bucket done in {duration:.2f}s")
    
    return num_events, earnings_count, non_earnings_count, events_processed, num_qnas


def update_process_collections(cutoff_date, LOOKBACK_DAYS, execution_metrics_collection, run_id):
    step_start = time.time()
    create_embeddings_collection(cutoff_date, LOOKBACK_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_embeddings_collection", duration)
    logger.info(f"create_embeddings_collection done in {duration:.2f}s")

    MIN_CHUNK_LENGTH = 10
    ANALYSIS_WINDOW_DAYS = 180
    step_start = time.time()
    create_similarity_mda_collection(cutoff_date=cutoff_date, lookback_days=LOOKBACK_DAYS, min_chunk_length=MIN_CHUNK_LENGTH, analysis_window_days=ANALYSIS_WINDOW_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_similarity_mda_collection", duration)
    logger.info(f"Completed create_similarity_mda_collection in {duration:.2f}s")

    ANALYSIS_WINDOW_DAYS = 180
    MIN_SIMILARITY = 0.4
    TOPN = 6
    MIN_QUESTION_LENGTH = 20
    MIN_ANSWER_LENGTH = 20
    step_start = time.time()
    create_similar_questions_collection(ANALYSIS_WINDOW_DAYS, MIN_SIMILARITY, TOPN, MIN_QUESTION_LENGTH, MIN_ANSWER_LENGTH, cutoff_date, LOOKBACK_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_similar_questions_collection", duration)
    logger.info(f"similar Questions collection created in {duration:.2f}s")

    MIN_QUESTION_LENGTH = 10
    MIN_ANSWER_LENGTH = 50
    step_start = time.time()
    create_entailment_collection(cutoff_date, LOOKBACK_DAYS, MIN_QUESTION_LENGTH, MIN_ANSWER_LENGTH)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_entailment_collection", duration)
    logger.info(f"entailments collection created in {duration:.2f}s")

    MIN_QUESTION_LENGTH = 10
    MIN_ANSWER_LENGTH = 50

    step_start = time.time()
    create_category_collection(cutoff_date, LOOKBACK_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_category_collection", duration)
    logger.info(f"create_category_collection done in {duration:.2f}s")

    step_start = time.time()
    create_similarity_collection(cutoff_date, LOOKBACK_DAYS, MIN_QUESTION_LENGTH, MIN_ANSWER_LENGTH)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_similarity_collection", duration)
    logger.info(f"create_similarity_collection done in {duration:.2f}s")

    MIN_QUESTION_LENGTH = 10
    MIN_ANSWER_LENGTH = 100

    step_start = time.time()
    create_llm_answer_collection(cutoff_date, LOOKBACK_DAYS, MIN_QUESTION_LENGTH, MIN_ANSWER_LENGTH)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_llm_answer_collection", duration)
    logger.info(f"LLM answers created in {duration:.2f}s")


def update_output_collections(cutoff_date, LOOKBACK_DAYS, analysis_window, execution_metrics_collection, run_id):
    MAX_WORDS_IN_SUMMARY = 50
    step_start = time.time()
    add_LLM_rationale_to_entailment(cutoff_date, LOOKBACK_DAYS, MAX_WORDS_IN_SUMMARY)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "add_LLM_rationale_to_entailment", duration)
    logger.info(f"LLM rationale added to entailments collection done in {duration:.2f}s")

    MAX_WORDS = 50
    ANALYSIS_WINDOW_DAYS = 180
    step_start = time.time()
    create_mda_trend_collection(cutoff_date=cutoff_date, lookback_days=LOOKBACK_DAYS, max_words=MAX_WORDS, analysis_window_days=ANALYSIS_WINDOW_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_mda_trend_collection", duration)
    logger.info(f"Completed create_mda_trend_collection in {duration:.2f}s")

    MAX_WORDS = 50
    step_start = time.time()
    create_trend_collection(cutoff_date, LOOKBACK_DAYS, MAX_WORDS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_trend_collection", duration)
    logger.info(f"Trend collection created in {duration:.2f}s")

    step_start = time.time()
    create_insights_collection(cutoff_date, LOOKBACK_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_insights_collection", duration)
    logger.info(f"Insights collection created in {duration:.2f}s")

    step_start = time.time()
    create_insights_mda_collection(cutoff_date=cutoff_date, lookback_days=LOOKBACK_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_insights_mda_collection", duration)
    logger.info(f"Completed create_insights_mda_collection in {duration:.2f}s")

    logger.info("Questions categorized and importance assigned")

    step_start = time.time()
    assign_importance_to_category(cutoff_date, LOOKBACK_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "assign_importance_to_category", duration)
    logger.info(f"assign_importance_to_category done in {duration:.2f}s")

    LLM_similarity_threshold = 0.6
    entailment_probability_contradiction_threshold = 0.5
    similar_questions_faiss_threshold = 0.65
    step_start = time.time()
    create_qna_flags_collection(cutoff_date, LOOKBACK_DAYS, LLM_similarity_threshold, entailment_probability_contradiction_threshold, similar_questions_faiss_threshold)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_qna_flags_collection", duration)
    logger.info(f"create_qna_flags_collection done in {duration:.2f}s")

    MIN_ANSWER_LENGTH = 30

    step_start = time.time()
    create_output_qna_collection(cutoff_date, LOOKBACK_DAYS, MIN_ANSWER_LENGTH)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_output_qna_collection", duration)
    logger.info(f"Final QNA collection created in {duration:.2f}s")
    
    step_start = time.time()
    create_mda_outputs(cutoff_date=cutoff_date, lookback_days=LOOKBACK_DAYS, analysis_window_days=ANALYSIS_WINDOW_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_mda_outputs", duration)
    logger.info(f"create_mda_outputs done in {duration:.2f}s")

    step_start = time.time()
    create_event_output_collection(cutoff_date, LOOKBACK_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_event_output_collection", duration)
    logger.info(f"Event output collection created in {duration:.2f}s")

    step_start = time.time()
    create_earnings_anomalies_collection(cutoff_date, LOOKBACK_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_earnings_anomalies_collection", duration)
    logger.info(f"create_earnings_anomalies_collection done in {duration:.2f}s")
    
    step_start = time.time()
    create_earnings_message(cutoff_date, LOOKBACK_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_earnings_message", duration)
    logger.info(f"create_earnings_message done in {duration:.2f}s")
    
    ANALYSIS_WINDOW_DAYS = 180
    step_start = time.time()
    calculate_event_scores(cutoff_date=cutoff_date, lookback_days=LOOKBACK_DAYS, anaysis_window_days=ANALYSIS_WINDOW_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "calculate_event_scores", duration)
    logger.info(f"calculate_event_scores done in {duration:.2f}s")
    
    step_start = time.time()
    create_event_summary(cutoff_date, LOOKBACK_DAYS, ANALYSIS_WINDOW_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_event_summary", duration)
    logger.info(f"Event summaries updated in {duration:.2f}s")
    
    step_start = time.time()
    create_event_transcript_summary(cutoff_date=cutoff_date, lookback_days=LOOKBACK_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "create_event_transcript_summary", duration)
    logger.info(f"Event transcripts updated in {duration:.2f}s")
    
    step_start = time.time()
    rank_insights(cutoff_date=cutoff_date, lookback_days=LOOKBACK_DAYS)
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "rank_insights", duration)
    logger.info(f"Insights ranked in {duration:.2f}s")

    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    step_start = time.time()
    companies_collection.update_many({}, {'$set': {'select_for_processing': 0}})
    duration = time.time() - step_start
    log_step_time(execution_metrics_collection, run_id, "reset_select_for_processing", duration)
    logger.info(f"Set select_for_processing to 0 for all companies in {duration:.2f}s")


def updatecollections():
    try:
        connection = DatabaseFactory().get_mongo_connection()
        public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
        run_id = str(uuid.uuid4())
        execution_metrics = connection.get_collection("execution_metrics")

        # Initialize the execution metrics document
        execution_metrics.insert_one({
            "run_id": run_id,
            "started_at": datetime.now(),
            "steps": {}
        })

        cutoff_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")

        total_start = time.time()
        
        num_events, earnings_count, non_earnings_count, events_processed = update_input_collections(cutoff_date, execution_metrics, run_id)
        
        # Update num_records in the metrics document
        execution_metrics.update_one(
            {"run_id": run_id},
            {
                "$set": {
                    "num_events": num_events,
                    "earnings_count": earnings_count,
                    "non_earnings_count": non_earnings_count,
                    "events": events_processed
                }
            }
        )

        analysis_window = 182
        update_process_collections(cutoff_date, LOOKBACK_DAYS, execution_metrics, run_id)

        update_output_collections(cutoff_date, LOOKBACK_DAYS, analysis_window, execution_metrics, run_id)

        step_start = time.time()
        date_email = (datetime.now() + timedelta(days=-1)).strftime("%Y-%m-%d")
        create_email_collection(date_email)
        duration = time.time() - step_start
        log_step_time(execution_metrics, run_id, "create_email_collection", duration)
        logger.info(f"create_email_collection done in {duration:.2f}s")
        
        step_start = time.time()
        create_earnings_reaction_update(cutoff_date, LOOKBACK_DAYS)
        duration = time.time() - step_start
        log_step_time(execution_metrics, run_id, "create_earnings_reaction_update", duration)
        logger.info(f"create_earnings_reaction_update done in {duration:.2f}s")

        total_duration = time.time() - total_start
        
        two_hours_ago = datetime.now() - timedelta(hours=2)
        processed_documents = public_investor_events_outputs_collection.count_documents({"updated_at": {"$gte": two_hours_ago}})
        
        # Final update with total duration and completion time
        execution_metrics.update_one(
            {"run_id": run_id},
            {
                "$set": {
                    "total_duration": total_duration,
                    "completed_at": datetime.now(),
                }
            }
        )
        
        logger.info(f"Number of records updated in public_investor_events_outputs collection in the last 2 hours: {processed_documents}")
        logger.info(f"Code run completed successfully in {total_duration:.2f}s with run_id: {run_id}")
    except Exception as e:
        log_step_error(execution_metrics, run_id, e)
        logger.error(f"Error in create_qna_flags_collection afters: {str(e)}")
