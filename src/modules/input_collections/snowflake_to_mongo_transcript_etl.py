from typing import List, Dict, Any
from datetime import datetime, timedelta
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.database.mongo.queries import mark_events_for_deletion
from src.database.snowflake.queries import extract_company_by_id, get_latest_company_transcript


logger = get_logger(__name__)


def _collect_company_ids(companies_collection) -> List:
    """
    Collect company IDs from the companies collection, updating any missing IDs.

    Args:
        companies_collection: MongoDB collection containing company data

    Returns:
        List of company IDs (snow_ids)
    """
    snow_ids = []
    companies = companies_collection.find({}, no_cursor_timeout=True)

    for company in companies:
        company_ticker = company["ticker"]
        snow_id = company.get("company_id")

        if snow_id is None:
            snow_id = extract_company_by_id(company_ticker)
            if snow_id is not None:
                companies_collection.update_one(
                    {'ticker': company_ticker},
                    {'$set': {'company_id': int(snow_id)}},
                    upsert=True
                )

        if snow_id is None:
            logger.warning(f"Skipping company {company_ticker} due to missing company_id")
            continue

        snow_ids.append(int(snow_id))

    return snow_ids


def _process_transcript_data(df, companies_collection, public_investor_events_collection, delete_old: int) -> int:
    """
    Process transcript data and update the public_investor_events collection.

    Args:
        df: DataFrame containing transcript data
        companies_collection: MongoDB collection for companies
        public_investor_events_collection: MongoDB collection for public investor events
        delete_old: Flag to determine if old records should be deleted

    Returns:
        Number of records processed
    """
    events_processed = []
    earnings_count = 0
    non_earnings_count = 0
    records_processed = 0

    for index, row in df.iterrows():
        try:
            event_name = row["HEADLINE"]
            snow_id = row['COMPANYID']
            company_name = row['COMPANYNAME']
            transcriptid = row["TRANSCRIPTID"]
            unique_event_id = row["KEYDEVID"]

            company = companies_collection.find_one({"company_id": snow_id})
            if not company:
                logger.warning(f"Company with company_id {snow_id} not found.")
                continue

            company_ticker = company["ticker"]
            company_id = company["_id"]

            if public_investor_events_collection.find_one({"transcript_id": transcriptid}):
                logger.info(f"Event already exists: {transcriptid}")
                continue
            
            old_transcript = None
            if delete_old == 1:
                logger.debug(f"Delete old is set to {delete_old}")
                existing_event = public_investor_events_collection.find_one({"unique_event_id": unique_event_id})
                if existing_event:
                    logger.info(f"Event already exists: {unique_event_id}. Deleting older event docs.")
                    event_id = existing_event["_id"]
                    old_transcript = existing_event["unique_event_id"]
                    mark_events_for_deletion(event_id)

            document = _create_event_document(row, company_name, company_ticker, snow_id, company_id, old_transcript)

            result  = public_investor_events_collection.update_one(
                {"unique_event_id": int(unique_event_id)},
                {"$set": document},
                upsert=True
            )
            if result.upserted_id is not None:
                processed_event_id = result.upserted_id
            else:
                existing_doc = public_investor_events_collection.find_one(
                    {"unique_event_id": int(unique_event_id)},
                    {"_id": 1}
                )
                processed_event_id = existing_doc["_id"]
            events_processed.append({"event_id": processed_event_id, "datetime": document["datetime"]})
            logger.info(f"Upserted event: {event_name}")
            records_processed += 1
            if "Earnings Call" in document.get("title"):
                earnings_count += 1
            else:
                non_earnings_count += 1

            companies_collection.update_one(
                {"ticker": company_ticker},
                {"$set": {"select_for_processing": 1}}
            )

        except Exception as e:
            logger.error(f"Error processing transcript row: {e}", exc_info=True)

    return records_processed, earnings_count, non_earnings_count, events_processed


def _create_event_document(row, company_name, company_ticker, snow_id, company_id, old_transcript) -> Dict[str, Any]:
    """
    Create a document for the public_investor_events collection.

    Args:
        row: DataFrame row containing transcript data
        company_name: Name of the company
        company_ticker: Company ticker symbol
        snow_id: Company ID in Snowflake
        company_id: Company ID in MongoDB

    Returns:
        Document to be inserted into the public_investor_events collection
    """
    event_name = row["HEADLINE"]
    file_date = row["MOSTIMPORTANTDATEUTC"]
    file_time = row["MOSTIMPORTANTDATEUTC"]
    unique_event_id = int(row["KEYDEVID"])
    transcriptid = int(row["TRANSCRIPTID"])

    return {
        "title": event_name,
        "company_name": company_name,
        "ticker": company_ticker,
        "ticker_factset": snow_id,
        "snow_id": snow_id,
        "date": file_date,
        "datetime": file_time,
        "file_name": f"{company_ticker}_{file_date}_{event_name}.xml",
        "factset_location": unique_event_id,
        "xml_location": unique_event_id,
        "company_id": company_id,
        "factset_event_id": transcriptid,
        "transcript_id": transcriptid,
        "unique_event_id": unique_event_id,
        "old_transcript": old_transcript,
        "updated_at": datetime.now(),
        "select_for_qna_processing": 1
    }


def snowflake_create_public_investor_events_collection_pooled(cutoff_date, lookback_days, delete_old=1):
    """
    Create or update the public_investor_events collection by fetching and processing
    company transcript data from Snowflake.

    Args:
        cutoff_date: End date for data retrieval in format 'YYYY-MM-DD'
        lookback_days: Number of days to look back from the cutoff date
        delete_old: Flag to determine if old records should be deleted (1) or kept (0)

    Returns:
        DataFrame containing the fetched company transcript data
    """
    logger.info("Creating public_investor_events collection")
    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    public_investor_events_collection = connection.get_collection("public_investor_events")
    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)
    snow_ids = _collect_company_ids(companies_collection)
    df = get_latest_company_transcript(snow_ids, cutoff_date_start, cutoff_date_end)
    logger.debug("Retrieved transcript data")
    records_processed, earnings_count, non_earnings_count, events_processed = _process_transcript_data(df, companies_collection, public_investor_events_collection, delete_old)
    logger.info(f"Processed {records_processed} transcript records. Earnings: {earnings_count}. Non Earnings: {non_earnings_count}")
    return records_processed, earnings_count, non_earnings_count, events_processed


if __name__ == "__main__":
    snowflake_create_public_investor_events_collection_pooled("2025-03-26", 10)
