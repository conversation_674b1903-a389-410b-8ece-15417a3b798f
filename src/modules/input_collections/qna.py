import threading
import pandas as pd
from pymongo import InsertOne
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.database.snowflake.queries import snowflake_extract_participants, fetch_transcript_data
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def snowflake_to_df(transcriptid):
    df = fetch_transcript_data(transcript_id=transcriptid)
    processed_data = []

    # Iterate through the DataFrame to combine questions and answers
    current_question = None
    current_answer = None
    question_speaker = ''
    answer_speaker = ''
    slno = 1
    temp = None

    for index, row in df.iterrows():
        if row['FIRSTNAME'] is None:
            row['FIRSTNAME']  = ""
        if row['LASTNAME'] is None:
            row['LASTNAME']  = ""
        if row['TITLE'] is None:
            row['TITLE']  = ""
        speaker = f"{row['FIRSTNAME']} {row['LASTNAME']} {row['TITLE']}"
        if speaker.strip() == "":
            speaker = "Unknown"
        component_type = row['TRANSCRIPTCOMPONENTTYPENAME']

        if row['COMPONENTTEXT'] == temp or "[Operator Instructions]" in row["COMPONENTTEXT"]:
            continue
        temp = row['COMPONENTTEXT']
        if component_type == 'Question':
            if current_question is None:
                current_question = row['COMPONENTTEXT']
                question_speaker = speaker
            else:
                # Combine with existing question if multiple in a row
                current_question += ' ' + row['COMPONENTTEXT']

        elif component_type == 'Answer':
            if current_answer is None:
                current_answer = row['COMPONENTTEXT']
                answer_speaker = speaker
            else:
                # Combine with existing answer if multiple in a row
                current_answer += ' ' + row['COMPONENTTEXT']

        elif component_type == 'Presenter Speech':
            # If presenter speech, record it separately as MDA
            processed_data.append({
                'slno': slno,
                'section': 'MDA',
                'question': None,
                'question_speaker': None,
                'answer': row['COMPONENTTEXT'],
                'answer_speaker': speaker
            })
            slno += 1

        # Store and reset when a new question or end of DataFrame is reached
        if component_type == 'Question' and current_question and current_answer:
            processed_data.append({
                'slno': slno,
                'section': 'QNA',
                'question': current_question,
                'question_speaker': question_speaker,
                'answer': current_answer,
                'answer_speaker': answer_speaker
            })
            slno += 1

            # Reset for next Q&A pair
            current_question = row['COMPONENTTEXT']
            question_speaker = speaker
            current_answer = None
            answer_speaker = ''

        elif index == len(df) - 1 and current_question and current_answer:
            processed_data.append({
                'slno': slno,
                'section': 'QNA',
                'question': current_question,
                'question_speaker': question_speaker,
                'answer': current_answer,
                'answer_speaker': answer_speaker
            })
            slno += 1
    processed_df = pd.DataFrame(processed_data)
    return processed_df


def process_single_event(event, cutoff_date_start, cutoff_date_end, deleted_events_set, deleted_events_lock):
    """Process a single event and return batch data for bulk insert"""
    # Create new database connection for this thread
    connection = DatabaseFactory().get_mongo_connection()
    events_collection = connection.get_collection("public_investor_events")
    qna_collection = connection.get_collection("qnas")
    event_id = event["_id"]
    with deleted_events_lock:
        if event_id not in deleted_events_set:
            delete_records_marked_for_deletion(event_id=event_id, coll=qna_collection)
            deleted_events_set.add(event_id)

    events_collection.update_one(
        {"_id": event_id},
        {"$set": {"select_for_qna_processing": 0}}
    )
    company_name = event["company_name"]
    company_ticker = event["ticker"]
    title = event["title"]
    file_name = event["file_name"]
    file_date = event["date"]
    factset_event_id = event["factset_event_id"]
    transcript_id = event["transcript_id"]
    unique_event_id = event["unique_event_id"]

    logger.info(f"Processing event: {file_name}")
    batch = []

    if cutoff_date_start < file_date < cutoff_date_end:
        if qna_collection.find_one({"transcript_id": transcript_id, "unique_event_id": unique_event_id}):
            logger.info(f"Event already exists: {file_name}, {factset_event_id}")
            return batch

        try:
            corp_reps, analysts = snowflake_extract_participants(transcript_id)
        except Exception:
            logger.exception(f"Error extracting participants: {transcript_id}")
            return batch
        # Update event with participants
        events_collection.update_one(
            {"_id": event_id},
            {"$set": {"corp_reps": corp_reps, "analysts": analysts}}
        )
        logger.info(f"Updated event with participants: {transcript_id}")

        # Run time code for prepping the dataframe with just Q&A
        qa_data = pd.DataFrame()
        try:
            qa_data = snowflake_to_df(transcript_id)
        except Exception:
            logger.exception(f"Error extracting Q&A: {transcript_id}")
            return batch

        data_dict = qa_data.to_dict("records")
        current_datetime = datetime.now()

        for record in data_dict:
            document = {
                "company_name": company_name,
                "ticker": company_ticker,
                "event": title,
                "event_id": event_id,
                "date": file_date,
                "file_name": file_name,
                "question_speaker": record["question_speaker"],
                "question": record["question"],
                "answer_speaker": record["answer_speaker"],
                "answer": record["answer"],
                "section": record["section"],
                "factset_event_id": factset_event_id,
                "transcript_id": transcript_id,
                "unique_event_id": unique_event_id,
                "updated_at": current_datetime,
                "slno": record["slno"],
                "select_for_embeddings_processing": 1,
                "is_operator_instruction_removed": 1
            }
            batch.append(InsertOne(document))
        logger.info(f"Processed event: {file_name}")
    return batch


def process_investor_events_for_qna(cutoff_date, lookback_days, max_workers=10) -> int:
    logger.info("Creating qnas collection")
    connection = DatabaseFactory().get_mongo_connection()

    events_collection = connection.get_collection("public_investor_events")
    qna_collection = connection.get_collection("qnas")

    # Thread-safe set for deleted events
    deleted_events_set = set()
    deleted_events_lock = threading.Lock()

    num_qnas = 0

    with events_collection.find({"select_for_qna_processing": 1}, no_cursor_timeout=True).batch_size(5000) as events:
        cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
        cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)
        # Convert cursor to list for parallel processing
        events_list = list(events)
        logger.info(f"Found {len(events_list)} events to process")
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_event = {
                executor.submit(
                    process_single_event,
                    event,
                    cutoff_date_start,
                    cutoff_date_end,
                    deleted_events_set,
                    deleted_events_lock
                ): event for event in events_list
            }

            # Collect results as they complete
            for future in as_completed(future_to_event):
                event = future_to_event[future]
                try:
                    batch = future.result()
                    if batch:  # Only add non-empty batches
                        qna_collection.bulk_write(batch)
                        num_qnas += len(batch)
                        batch = []
                except Exception as exc:
                    logger.exception(f'Event {event.get("file_name", "unknown")} generated an exception: {exc}')

        logger.info("Qnas collection creation completed.")
    
    return num_qnas


if __name__ == "__main__":
    process_investor_events_for_qna('2025-05-14', 7)
