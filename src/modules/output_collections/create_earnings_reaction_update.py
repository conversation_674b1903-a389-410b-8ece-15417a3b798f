from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger
from dotenv import load_dotenv

load_dotenv()

logger = get_logger(__name__)


def find_stock_price_with_fallback(stock_prices_collection, ticker, base_date, days_offsets):
    """
    Find stock price for a ticker, trying multiple date offsets iteratively.

    Args:
        stock_prices_collection: MongoDB collection for stock prices
        ticker: Stock ticker symbol
        base_date: Base event date
        days_offsets: List of day offsets to try (e.g., [-1, -2, -3] for before dates)

    Returns:
        tuple: (stock_price_data, actual_date_used) or (None, None) if not found
    """
    for offset in days_offsets:
        target_date = base_date + timedelta(days=offset)
        stock_price = stock_prices_collection.find_one({"ticker": ticker, "date": target_date})
        if stock_price:
            return stock_price, target_date
    return None, None


def create_earnings_reaction_update(cutoff_date, lookback_days):
    """Find all earnings events and calculate stock price reactions."""
    # Initialize database connections
    connection = DatabaseFactory().get_mongo_connection()
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    stock_prices_collection = connection.get_stock_collection("stock_prices")

    # Handle date conversion
    if isinstance(cutoff_date, str):
        cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    else:
        cutoff_date_end = cutoff_date

    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    # Query earnings events
    events = public_investor_events_outputs_collection.find({
        "event_type": "earnings",
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "$or": [
                {"sentiment": "TBD"},
                {"sentiment": None},
                {"sentiment": {"$exists": False}}
            ]
    }, no_cursor_timeout=True)

    # Define the day offsets to try
    before_offsets = [-1, -2, -3, -4]  # t-1, t-2, t-3, t-4
    after_offsets = [1, 2, 3, 4]      # t+1, t+2, t+3, t+4

    for event in events:
        event_id = event["event_id"]
        ticker = event["ticker"]
        event_date = event["date"]
        event_date = event_date.replace(hour=0, minute=0, second=0, microsecond=0)
        logger.info(f"Calculating earnings reaction for event with ID {event_id} and {ticker} on date {event_date}.")

        # Find stock prices before and after the event
        stock_price_before, date_before_used = find_stock_price_with_fallback(stock_prices_collection, ticker, event_date, before_offsets)

        stock_price_after, date_after_used = find_stock_price_with_fallback(stock_prices_collection, ticker, event_date, after_offsets)

        # Check if we found both required prices
        if not stock_price_before or not stock_price_after:
            logger.warning(f"Missing stock price data for {ticker} on {event_date}. "
                           f"Before: {'Found' if stock_price_before else 'Not found'}, "
                           f"After: {'Found' if stock_price_after else 'Not found'}")

            public_investor_events_outputs_collection.update_one(
                {"event_id": event_id},
                {"$set": {"sentiment": "TBD"}},
                upsert=True
            )
            continue

        # Calculate the earnings reaction
        earnings_reaction = (stock_price_after["closingPrice"] - stock_price_before["closingPrice"]) / stock_price_before["closingPrice"]

        logger.info(f"Earnings reaction for {ticker} on {event_date} is {earnings_reaction:.2%}.")

        # Determine sentiment
        sentiment = "stockUP" if earnings_reaction > 0 else "stockDOWN"

        # Store the earnings reaction and dates used in the database
        public_investor_events_outputs_collection.update_one(
            {"event_id": event_id},
            {
                "$set": {
                    "sentiment": sentiment,
                    "earnings_reaction": earnings_reaction,
                    "price_date_before": date_before_used,
                    "price_date_after": date_after_used
                }
            },
            upsert=True
        )


if __name__ == "__main__":
    cutoff_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    create_earnings_reaction_update(cutoff_date=cutoff_date, lookback_days=5000)
