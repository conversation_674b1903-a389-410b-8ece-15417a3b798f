import json
import pandas as pd
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.core.logging import get_logger
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)

# Thread-safe lock for tracking deleted events
deleted_events_lock = Lock()


def generate_category_prompt(question, answer, classification_list, sector):
    """
    Generate a prompt for classifying a QnA into one of the topics for a specific sector.
    """
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    classification_list_text = '\n'.join(f'- {category}' for category in classification_list)
    categorize_management_answers_prompt = prompt_collection.find_one({"prompt_name": "categorize_management_answers_prompt"})["prompt"]
    prompt = categorize_management_answers_prompt.format(classification_list_text=classification_list_text, question=question, answer=answer, sector=sector)
    return prompt


def process_qna(qna, deleted_events, companies_collection, categories_collection, qna_importance_collection):
    """
    Process a single QnA document: classify it and store results in MongoDB.
    This function is thread-safe and can be executed in parallel.
    """
    try:
        qnaId = qna["_id"]
        event_id = qna["event_id"]
        ticker = qna["ticker"]
        question = qna["question"]
        answer = qna["answer"]
        
        # Thread-safe check and deletion of records
        with deleted_events_lock:
            if event_id not in deleted_events:
                delete_records_marked_for_deletion(event_id=event_id, coll=qna_importance_collection)
                deleted_events.append(event_id)

        # Skip if qnaId exists in qna_importance collection
        if qna_importance_collection.find_one({"qnaId": qnaId}):
            logger.info("category exists for qnaId: %s", qnaId)
            return None

        # Identify sector of company
        company_doc = companies_collection.find_one({"ticker": ticker})
        sector = company_doc["sector"] if company_doc else None
        logger.info("Processing ticker: %s, sector: %s", ticker, sector)

        # Load all categories for the sector
        categories_cursor = categories_collection.find({"sector": sector}, no_cursor_timeout=True)
        sector_topics = pd.DataFrame(list(categories_cursor))
        
        if sector_topics.empty:
            logger.warning("No categories found for sector: %s", sector)
            return None
            
        classification_list = sector_topics["category"]

        # Generate the prompt
        prompt = generate_category_prompt(question, answer, classification_list, sector)

        # Call the classification function (LLM)
        openai_service = OpenAIService()
        generation = openai_service.get_completion(prompt)
        
        if generation:
            json_content = generation
            try:
                category_data = json.loads(json_content)
                qna_category = category_data.get("category", "Uncategorized")
            except json.JSONDecodeError:
                logger.info("Invalid JSON response for qnaId: %s", qnaId)
                qna_category = "Uncategorized"
        else:
            logger.info("No JSON content found for qnaId: %s", qnaId)
            qna_category = "Uncategorized"

        category_importance = "Somewhat Important"
        
        # Store the category back into MongoDB
        document = {
            "qnaId": qnaId,
            "ticker": ticker,
            "sector": sector,
            "question": question,
            "answer": answer,
            "event_id": event_id,
            "category": qna_category,
            "importance": category_importance,
            "raw result": generation,
            "date": qna["date"],
            "is_importance_assigned": False,
            "updated_at": datetime.now()
        }
        qna_importance_collection.insert_one(document)
        logger.info("Ticker: %s, QnA Category: %s, Category Importance: %s", ticker, qna_category, category_importance)
        
        return {"qnaId": qnaId, "category": qna_category}
        
    except Exception as e:
        logger.error("Error processing qnaId %s: %s", qna.get("_id"), str(e))
        return None


def create_category_collection(cutoff_date, lookback_days, max_workers=10):
    """
    Loop through each QnA in parallel, generate a prompt, call the LLM to classify, and store the results in MongoDB.
    
    Args:
        cutoff_date: End date for QnA processing (format: YYYY-MM-DD)
        lookback_days: Number of days to look back from cutoff_date
        max_workers: Maximum number of parallel threads (default: 10)
    """
    connection = DatabaseFactory().get_mongo_connection()
    qna_collection = connection.get_collection("qnas")
    companies_collection = connection.get_collection("companies")
    categories_collection = connection.get_collection("sector_categories")
    qna_importance_collection = connection.get_collection("qna_importance")

    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end}, 
        "ticker": {"$in": tickers}
    }
    
    deleted_events = []
    
    # Fetch all QnAs first to avoid cursor timeout issues with parallel processing
    qnas = list(qna_collection.find(query).batch_size(1000))
    total_qnas = len(qnas)
    logger.info("Found %d QnAs to process", total_qnas)
    
    if not qnas:
        logger.info("No QnAs found for the specified date range.")
        return
    
    # Process QnAs in parallel
    processed_count = 0
    failed_count = 0
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_qna = {
            executor.submit(
                process_qna, 
                qna, 
                deleted_events, 
                companies_collection, 
                categories_collection, 
                qna_importance_collection
            ): qna for qna in qnas
        }
        
        # Process completed tasks
        for future in as_completed(future_to_qna):
            qna = future_to_qna[future]
            try:
                result = future.result()
                if result:
                    processed_count += 1
                    if processed_count % 100 == 0:
                        logger.info("Progress: %d/%d QnAs processed", processed_count, total_qnas)
            except Exception as e:
                failed_count += 1
                logger.error("Exception for qnaId %s: %s", qna.get("_id"), str(e))
    
    logger.info("Processing complete. Total: %d, Processed: %d, Failed: %d", 
                total_qnas, processed_count, failed_count)
    return


# if __name__ == "__main__":
#     create_category_collection("2025-04-07", 1780, max_workers=10)