from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory


def get_step1_candidates(sentiment, outputs):
    """
    In this step we find the insights with highest importance and LLM_trend_final is 1
    if there are none
        - then get the very important ones
    If there are none
        - then get somewhat important ones
    """
    if sentiment == "positive":
        multiplier = 1
    else:
        multiplier = -1

    result_insights = []
    for output in outputs:
        if output["importance"] == "Very Important" and output["LLM_trend_final"] and multiplier * output["LLM_trend_final"] == 1:
            result_insights.append(output)

    if not result_insights:
        for output in outputs:
            if output["importance"] == "Somewhat Important" and output["LLM_trend_final"] and multiplier * output["LLM_trend_final"] == 1:
                result_insights.append(output)

    if not result_insights:
        for output in outputs:
            if output["importance"] == "Very Important":
                result_insights.append(output)

    if not result_insights:
        for output in outputs:
            if output["importance"] == "Somewhat Important":
                result_insights.append(output)
    return result_insights


def get_step3_candidates(outputs):
    """
    Get the item with highest flag count
    """
    connection = DatabaseFactory().get_mongo_connection()
    qna_flags_collection = connection.get_collection("qna_flags")
    relevant_qna_ids = [output["qnaId"] for output in outputs]
    relevant_qna_flags = list(qna_flags_collection.find({"qnaId": {"$in": relevant_qna_ids}}))

    qna_id_to_flag_count = {
        flag["qnaId"]: flag.get("flag_count", 0)
        for flag in relevant_qna_flags
    }
    for output in outputs:
        output["flag_count"] = qna_id_to_flag_count.get(output["qnaId"], 0)

    sorted_outputs = sorted(outputs, key=lambda x: x['flag_count'], reverse=True)

    highest_flag_count = sorted_outputs[0]['flag_count']
    highest_items = [item for item in sorted_outputs if item['flag_count'] == highest_flag_count]
    if len(highest_items) == 1:
        return [sorted_outputs[0]]
    else:
        return highest_items


def _rank_insights(event_id):
    connection = DatabaseFactory().get_mongo_connection()
    events_collection = connection.get_collection("public_investor_events_outputs")
    qna_output_collection = connection.get_collection("outputs")
    event_output = events_collection.find_one({"event_id": event_id})
    qna_output = qna_output_collection.find({
        "event_id": event_id,
        "LLM_insight": {"$ne": None}
    })
    outputs = list(qna_output)
    # event_sentiment = event_output["sentiment"]

    uptick_score = event_output['uptick_score']
    downtick_score = event_output['downtick_score']

    downtick_threshold = event_output.get("down_th")

    net_threshold = event_output.get("net_th")

    score = event_output['score']
    event_sentiment = event_output.get("sentiment")
    if event_sentiment not in ["positive", "negative", "neutral"]:
        if score is not None and isinstance(score, (int, float)) and uptick_score is not None and downtick_score is not None and isinstance(uptick_score, (int, float)) and isinstance(downtick_score, (int, float)):
            event_sentiment = "negative" if downtick_score < downtick_threshold else ('positive' if uptick_score + downtick_score > net_threshold else 'neutral')
        else:
            event_sentiment = "neutral"

    if not outputs:
        return None

    top_candidates = get_step1_candidates(sentiment=event_sentiment, outputs=outputs)
    if len(top_candidates) == 1:
        return top_candidates[0]

    if not top_candidates:
        top_candidates = outputs

    top_candidates = get_step3_candidates(outputs=top_candidates)
    if len(top_candidates) == 1:
        return top_candidates[0]

    sorted_top_candidates = sorted(
        (x for x in top_candidates if x.get('LLM_answer_final_similarity') is not None),
        key=lambda x: x['LLM_answer_final_similarity']
    )
    if not sorted_top_candidates:
        return top_candidates[0]

    return sorted_top_candidates[0]


def rank_insights(cutoff_date, lookback_days):
    connection = DatabaseFactory().get_mongo_connection()
    events_collection = connection.get_collection("public_investor_events_outputs")
    qna_output_collection = connection.get_collection("outputs")
    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)
    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "insights_ranked": {"$exists": False}
    }
    with events_collection.find(query, no_cursor_timeout=True) as events:
        for event in events:
            event_id = event["event_id"]
            top_insight = _rank_insights(event_id=event_id)
            if not top_insight:
                events_collection.update_one(
                    {"_id": event["_id"]},
                    {
                        "$set": {
                            "insights_ranked": False
                        }
                    }
                )
                print("There is no top insight for this event id")
                continue
            top_insight_id = top_insight["_id"]
            qna_output_collection.update_one(
                {"_id": top_insight_id},
                {
                    "$set": {
                        "top_insight": True
                    }
                }
            )

            events_collection.update_one(
                {"_id": event["_id"]},
                {
                    "$set": {
                        "insights_ranked": True
                    }
                }
            )

            print(f"Top Output: {top_insight['LLM_answer_final']}")


if __name__ == "__main__":
    cutoff_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    rank_insights(cutoff_date=cutoff_date, lookback_days=7)
