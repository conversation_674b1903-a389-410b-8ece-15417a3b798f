from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.core.logging import get_logger
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def get_insight_refinement_prompt(trend, trend_rationale):
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    insight_refinement_prompt = prompt_collection.find_one({"prompt_name": "insight_refinement_prompt"})["prompt"]
    prompt = insight_refinement_prompt.format(trend=trend, trend_rationale=trend_rationale)
    return prompt


def process_single_document(llm_mda_trend, cutoff_date_start, cutoff_date_end, deleted_events_set):
    """
    Process a single document to generate insights.
    This function will be executed in parallel.
    """
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    llm_mda_trend_collection = connection.get_collection("LLM_MDA_trend")
    llm_mda_insights_collection = connection.get_collection("LLM_MDA_insights")
    
    qnaId = llm_mda_trend["qnaId"]
    event_id = llm_mda_trend["event_id"]
    event_name = llm_mda_trend["event"]
    date = llm_mda_trend["date"]
    mda_chunk = llm_mda_trend["answer"]
    ticker = llm_mda_trend["ticker"]
    mda_chunk_category = llm_mda_trend["category"]
    mda_chunk_importance = llm_mda_trend["importance"]
    
    # Handle deletion (thread-safe check)
    if event_id not in deleted_events_set:
        delete_records_marked_for_deletion(event_id=event_id, coll=llm_mda_insights_collection)
        deleted_events_set.add(event_id)

    trend = llm_mda_trend["trend"]
    trend_rationale = llm_mda_trend["trend_rationale"]

    # Check if insight already exists
    if llm_mda_insights_collection.find_one({"mda_chunk_id": llm_mda_trend["mda_chunk_id"]}):
        logger.info("LLM insight already exists")
        # Mark as processed
        llm_mda_trend_collection.update_one(
            {"_id": llm_mda_trend["_id"]},
            {"$set": {"select_for_llm_insights_mda_processing": 0}}
        )
        return True

    if date < cutoff_date_start or date > cutoff_date_end:
        # Mark as processed
        llm_mda_trend_collection.update_one(
            {"_id": llm_mda_trend["_id"]},
            {"$set": {"select_for_llm_insights_mda_processing": 0}}
        )
        return True

    # Create the prompt
    insight_refinement_prompt = get_insight_refinement_prompt(trend=trend, trend_rationale=trend_rationale)

    # Ask OpenAI to generate insight
    insight = openai_service.get_completion(insight_refinement_prompt)
    logger.info(insight)

    # Store the insight in MongoDB
    document = {
        "mda_chunk_id": llm_mda_trend["mda_chunk_id"],
        "chunk_id": llm_mda_trend["chunk_id"],
        "qnaId": qnaId,
        "ticker": ticker,
        "event_id": event_id,
        "event": event_name,
        "answer": mda_chunk,
        "rationale": trend_rationale,
        "trend": trend,
        "insight": insight,
        "date": date,
        "category": mda_chunk_category,
        "importance": mda_chunk_importance,
        "updated_at": datetime.now()
    }

    # Update the trend document and insert the insight document
    llm_mda_trend_collection.update_one(
        {"_id": llm_mda_trend["_id"]},
        {"$set": {"select_for_llm_insights_mda_processing": 0}}
    )
    llm_mda_insights_collection.insert_one(document)
    
    return True


def create_insights_mda_collection(cutoff_date, lookback_days, batch_size=100, max_workers=10):
    """
    Loop through each question in LLM_trend and ask OpenAI to generate insights.
    Uses parallel processing with ThreadPoolExecutor for concurrent LLM calls.
    
    Args:
        cutoff_date: End date for processing
        lookback_days: Number of days to look back
        batch_size: Number of documents to fetch at once
        max_workers: Maximum number of parallel workers (default: 5)
    """
    connection = DatabaseFactory().get_mongo_connection()
    llm_mda_trend_collection = connection.get_collection("LLM_MDA_trend")

    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "select_for_llm_insights_mda_processing": 1
    }

    # Fetch all documents first to avoid cursor timeout issues
    logger.info("Fetching documents to process...")
    documents_to_process = list(llm_mda_trend_collection.find(query).batch_size(batch_size))
    total_documents = len(documents_to_process)
    logger.info(f"Found {total_documents} documents to process")

    # Track deleted events (using set for thread-safety with proper handling)
    deleted_events_set = set()
    processed_count = 0

    # Process documents in parallel
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_doc = {
            executor.submit(
                process_single_document, 
                doc, 
                cutoff_date_start, 
                cutoff_date_end, 
                deleted_events_set
            ): doc 
            for doc in documents_to_process
        }

        # Process completed tasks
        for future in as_completed(future_to_doc):
            try:
                result = future.result()
                processed_count += 1
                if processed_count % 10 == 0:  # Log progress every 10 documents
                    logger.info(f"Progress: {processed_count}/{total_documents} documents processed")
            except Exception as e:
                doc = future_to_doc[future]
                logger.error(f"Error processing document {doc.get('_id')}: {str(e)}")
                processed_count += 1

    logger.info(f"Completed processing: {processed_count} documents processed")
    return