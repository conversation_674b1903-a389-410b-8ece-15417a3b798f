import json
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.core.logging import get_logger
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def get_trend_prompt(question, answer, similar_questions_faiss, classification, rationale, max_words):
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    trend_prompt = prompt_collection.find_one({"prompt_name": "trend_prompt"})["prompt"]
    similar_qna_faiss_text = " ".join(
        [
            f"Question: {item['question']} Answer: {item['answer']}"
            for item in similar_questions_faiss[:5]
        ]
    )
    prompt = trend_prompt.format(question=question, answer=answer, similar_qna_faiss=similar_qna_faiss_text, classification=classification, rationale=rationale, max_words=max_words)
    return prompt


def process_single_llm_similarity(llm_similarity_doc, cutoff_date_start, cutoff_date_end, max_words, deleted_events_set, deleted_events_lock):
    """
    Process a single LLM similarity document.
    """
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    similar_question_collection = connection.get_collection("similar_questions")
    llm_trend_collection = connection.get_collection("LLM_trend")
    qnaId = llm_similarity_doc["qnaId"]
    date = llm_similarity_doc["date"]
    event_id = llm_similarity_doc["event_id"]
    question = llm_similarity_doc["question"]
    answer = llm_similarity_doc["answer"]
    classification = llm_similarity_doc["classification"]
    rationale = llm_similarity_doc["rationale"]
    with deleted_events_lock:
        if event_id not in deleted_events_set:
            delete_records_marked_for_deletion(event_id=event_id, coll=llm_trend_collection)
            deleted_events_set.add(event_id)

    if llm_trend_collection.find_one({"qnaId": qnaId}):
        logger.info("LLM trend already exists")
        return

    if classification == "Similar":
        return

    # Get related_qna_faiss from the `similar_questions` collection for this qnaId
    similar_question = similar_question_collection.find_one({"qnaId": qnaId})
    similar_questions_faiss = similar_question.get("related_qna_faiss", []) if similar_question else []

    if date < cutoff_date_start or date > cutoff_date_end:
        return

    # Create the prompt
    trend_prompt = get_trend_prompt(
        question=question,
        answer=answer,
        similar_questions_faiss=similar_questions_faiss,
        classification=classification,
        rationale=rationale,
        max_words=max_words,
    )
    try:
        # Ask OpenAI to differentiate the answer
        trend = openai_service.get_completion(trend_prompt)
        if isinstance(trend, dict):
            data = trend
        else:
            data = json.loads(trend)

        trend_classification = data["trend"]
        logger.info(trend_classification)
        trend_rationale = data["rationale"]
        logger.info(trend_rationale)

        # Store the differentiation back in MongoDB
        document = {
            "qnaId": qnaId,
            "ticker": llm_similarity_doc["ticker"],
            "event_id": event_id,
            "question": question,
            "answer": answer,
            "classification": classification,
            "rationale": rationale,
            "trend": trend_classification,
            "trend_rationale": trend_rationale,
            "date": llm_similarity_doc["date"],
            "updated_at": datetime.now()
        }
        llm_trend_collection.insert_one(document)

    except Exception as e:
        logger.error(f"Error processing qnaId {qnaId}: {str(e)}")


def create_trend_collection(cutoff_date, lookback_days, max_words, max_workers=10):
    """
    Loop through each question in LLM_similarity and ask OpenAI to classify the trend of the answer.
    Now processes documents in parallel for improved performance.
    """
    connection = DatabaseFactory().get_mongo_connection()
    LLM_similarity_collection = connection.get_collection("LLM_similarity")
    companies_collection = connection.get_collection("companies")
    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "ticker": {"$in": tickers}
    }
    llm_similarities = list(LLM_similarity_collection.find(query).batch_size(100))
    if not llm_similarities:
        logger.info("No LLM similarities found for processing.")
        return

    deleted_events_set = set()
    deleted_events_lock = threading.Lock()

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_doc = {
            executor.submit(
                process_single_llm_similarity,
                doc,
                cutoff_date_start,
                cutoff_date_end,
                max_words,
                deleted_events_set,
                deleted_events_lock
            ): doc for doc in llm_similarities
        }

        for future in as_completed(future_to_doc):
            doc = future_to_doc[future]
            try:
                future.result()
            except Exception as e:
                logger.error(f"Error in parallel processing for document {doc.get('qnaId', 'unknown')}: {str(e)}")
