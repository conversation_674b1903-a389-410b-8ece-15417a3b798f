from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger

logger = get_logger(__name__)


def process_qna_batch(qna_batch, sector_categories_data):
    """Process a batch of QnA documents and return update operations"""
    connection = DatabaseFactory().get_mongo_connection()
    qna_importance = connection.get_collection("qna_importance")
    
    results = []
    for qna in qna_batch:
        qnaId = qna["qnaId"]
        logger.info(f"Assigning importance to {qnaId}")
        sector = qna["sector"]
        category = qna["category"]
        if sector == "consumer":
            sector = "consumer_disc"

        # Use cached sector_categories instead of querying database
        sector_category = sector_categories_data.get((sector, category))
        if sector_category:
            category_importance = sector_category["importance"]
        else:
            logger.info(f"Missing 'Category' or 'Importance' columns in sector_topics for sector: {sector}")
            category_importance = "Not Important"

        # Store the category back into MongoDB with upsert
        qna_importance.update_one(
            {"qnaId": qnaId},
            {"$set": {
                "importance": category_importance,
                "sector": sector,
                "is_importance_assigned": True,
                "updated_at": datetime.now()
            }},
            upsert=True
        )
        logger.info(f"Category: {category}, Category Importance: {category_importance}")
        results.append(qnaId)
    
    return results


def assign_importance_to_category(cutoff_date, lookback_days, max_workers=10, batch_size=50):
    logger.info("Executing assign_importance_to_category")
    connection = DatabaseFactory().get_mongo_connection()
    categories_collection = connection.get_collection("sector_categories")
    qna_importance = connection.get_collection("qna_importance")
    companies_collection = connection.get_collection("companies")
    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    # Load all sector_categories once and cache them
    logger.info("Loading sector categories...")
    sector_categories_data = {}
    for category_doc in categories_collection.find():
        key = (category_doc["sector"], category_doc["category"])
        sector_categories_data[key] = category_doc
    logger.info(f"Loaded {len(sector_categories_data)} sector categories")

    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "ticker": {"$in": tickers},
        "is_importance_assigned": False
    }

    # Collect all QnA documents into batches
    logger.info("Collecting QnA documents...")
    qna_batches = []
    current_batch = []
    
    with qna_importance.find(query, no_cursor_timeout=True).batch_size(200) as qna_cursor:
        for qna in qna_cursor:
            current_batch.append(qna)
            if len(current_batch) >= batch_size:
                qna_batches.append(current_batch)
                current_batch = []
        
        # Add remaining documents
        if current_batch:
            qna_batches.append(current_batch)
    
    logger.info(f"Created {len(qna_batches)} batches to process")
    
    if not qna_batches:
        logger.info("No QnA documents to process.")
        return

    # Process batches in parallel
    total_processed = 0
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all batches
        future_to_batch = {
            executor.submit(process_qna_batch, batch, sector_categories_data): i 
            for i, batch in enumerate(qna_batches)
        }
        
        # Process completed batches
        for future in as_completed(future_to_batch):
            batch_idx = future_to_batch[future]
            try:
                results = future.result()
                total_processed += len(results)
                logger.info(f"Completed batch {batch_idx + 1}/{len(qna_batches)}, processed {len(results)} documents")
            except Exception as exc:
                logger.error(f"Batch {batch_idx} generated an exception: {exc}")
    
    logger.info(f"Total documents processed: {total_processed}")


if __name__ == "__main__":
    assign_importance_to_category("2025-06-03", 1780)