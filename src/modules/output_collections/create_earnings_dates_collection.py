from src.database.factory import DatabaseFactory
from src.database.snowflake.queries import get_upcoming_earning_calls
from src.core.logging import get_logger

logger = get_logger(__name__)


def find_next_earnings_date(ticker):
    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    future_earnings_collection = connection.get_collection("future_earnings")
    company = companies_collection.find_one({"ticker": ticker})
    if company:
        company_id = company["company_id"]
    else:
        raise ValueError(f"Company with ticker {ticker} not found")

    df = get_upcoming_earning_calls(company_id=company_id)

    empty_array = []

    for _, row in df.iterrows():
        date = row["MOSTIMPORTANTDATEUTC_EVENT"]  # Adjusted for renamed column
        type = row["EVENT_TYPE"]
        keydevid = row["KEYDEVID_EVENT"]
        announceddate = row["EVENT_ANNOUNCED_DATE"]

        # Store the values in a dictionary and append to the list
        empty_array.append({
            "date": date,
            "type": type,
            "keydevid": keydevid,
            "announceddate": announceddate
        })

    if not empty_array:
        return
    # Insert the earnings data into the future_earnings collection

    for earnings_event in empty_array:
        future_earnings_collection.update_one(
            {
                "ticker": ticker,
                "keydevid": earnings_event["keydevid"]

            },
            {
                "$set": {
                    "earnings_date": earnings_event["date"],
                    "announceddate": earnings_event["announceddate"]
                }
            },
            upsert=True
        )


def create_earnings_dates_collection():
    # Get all tickers from the companies collection
    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    tickers = companies_collection.distinct("ticker")
    for ticker in tickers:
        find_next_earnings_date(ticker)
        logger.info(f"Processed ticker: {ticker}")

    logger.info(f"Created earnings dates collection for tickers: {tickers}")
    return


if __name__ == "__main__":
    create_earnings_dates_collection()