from datetime import datetime, timed<PERSON>ta
from concurrent.futures import ThreadPoolExecutor, as_completed
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.core.logging import get_logger

logger = get_logger(__name__)


def get_generate_summary_prompt(question_speaker, question, answer_speaker, answer, similar_qna_faiss, max_words_in_summary):
    """
    Generate a prompt to ask OpenAI to summarize the reason why the current answer differs from previous answers.
    """
    similar_qna_faiss_text = " ".join([f"Question: {item['question']} Answer: {item['answer']}" for item in similar_qna_faiss])
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    generate_summary_prompt = prompt_collection.find_one({"prompt_name": "generate_summary_prompt"})["prompt"]
    prompt = generate_summary_prompt.format(answer_speaker=answer_speaker, question_speaker=question_speaker, question=question, answer=answer, similar_qna_faiss_text=similar_qna_faiss_text, max_words_in_summary=max_words_in_summary)
    return prompt


def process_single_qna_flag(qna_flag, cutoff_date_start, cutoff_date_end, max_words_in_summary):
    """
    Process a single QNA flag entry.
    Returns tuple: (success: bool, qnaId: str, summary: str or None, error: str or None)
    """
    try:
        qnaId = qna_flag["qnaId"]
        question = qna_flag["question"]
        answer = qna_flag["answer"]
        date = qna_flag["date"]

        # Skip if outside date range
        if date < cutoff_date_start or date > cutoff_date_end:
            return (False, qnaId, None, "Outside date range")

        # Skip if Rationale already exists
        if "Rationale" in qna_flag:
            logger.info(f"Rationale already exists for qnaId {qnaId}")
            return (False, qnaId, None, "Rationale exists")

        # Get database connections (each thread gets its own)
        connection = DatabaseFactory().get_mongo_connection()
        similar_questions_collection = connection.get_collection("similar_questions")
        
        # Get Similar questions
        similar_questions = similar_questions_collection.find_one({"qnaId": qnaId})
        
        if not similar_questions:
            return (False, qnaId, None, "No similar questions found")

        question_speaker = similar_questions.get("question_speaker", "")
        answer_speaker = similar_questions.get("answer_speaker", "")
        similar_qna_faiss = similar_questions.get("related_qna_faiss", [])
        
        # Create the prompt
        prompt = get_generate_summary_prompt(question_speaker, question, answer_speaker, answer, similar_qna_faiss, max_words_in_summary)
        
        # Ask OpenAI to summarize the differences (each thread gets its own service)
        openai_service = OpenAIService()
        summary = openai_service.get_completion(prompt)
        
        logger.info(f"LLM rationale for qnaId {qnaId} generated")
        
        return (True, qnaId, summary, None)
        
    except Exception as e:
        logger.error(f"Error processing qnaId {qna_flag.get('qnaId', 'unknown')}: {str(e)}")
        return (False, qna_flag.get('qnaId', 'unknown'), None, str(e))


def add_LLM_rationale_to_entailment(cutoff_date, lookback_days, max_words_in_summary, max_workers=10):
    """
    Loop through entailments collections and add an explanation of difference if entailment : contradiction.
    Processes entries in parallel using ThreadPoolExecutor.
    
    Args:
        cutoff_date: End date in format "YYYY-MM-DD"
        lookback_days: Number of days to look back from cutoff_date
        max_words_in_summary: Maximum words in the generated summary
        max_workers: Maximum number of parallel workers (default: 5)
    """
    connection = DatabaseFactory().get_mongo_connection()
    qna_flags_collection = connection.get_collection("entailments")

    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    # Fetch all matching documents
    qna_flags_cursor = qna_flags_collection.find({
        "entailment": "contradiction",
        "Rationale": {"$exists": False}
    }, no_cursor_timeout=True)

    # Convert cursor to list to allow parallel processing
    qna_flags_list = list(qna_flags_cursor)
    qna_flags_cursor.close()
    
    logger.info(f"Found {len(qna_flags_list)} QNA flags to process")

    # Process entries in parallel
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_qna = {
            executor.submit(process_single_qna_flag, qna_flag, cutoff_date_start, cutoff_date_end, max_words_in_summary): qna_flag
            for qna_flag in qna_flags_list
        }
        
        # Process completed tasks
        success_count = 0
        skip_count = 0
        error_count = 0
        
        for future in as_completed(future_to_qna):
            success, qnaId, summary, error = future.result()
            
            if success:
                # Update the database with the summary
                qna_flags_collection.update_one(
                    {"qnaId": qnaId},
                    {"$set": {"Rationale": summary, "updated_at": datetime.now()}}
                )
                success_count += 1
            elif error:
                if error in ["Outside date range", "Rationale exists", "No similar questions found"]:
                    skip_count += 1
                else:
                    error_count += 1
        
        logger.info(f"Processing complete. Success: {success_count}, Skipped: {skip_count}, Errors: {error_count}")