"""
Create transcript summaries for the events which do not have MDA insights or QNA insights
"""
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService


def get_company_details(ticker):
    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    company = companies_collection.find_one({"ticker": ticker})
    company_name = company["company_name"]
    company_details_collection = connection.get_collection("company_details")
    company_details = company_details_collection.find_one({"ticker": ticker})
    company_summary = None
    if company_details:
        company_summary = company_details.get("company_summary")
    return company_summary, company_name


def get_summarization_prompt_with_bullets(ticker, qa_pairs, mda_text, event_type):
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    prompt: str = prompt_collection.find_one({"prompt_name": "event_transcript_summary_bullets", "version": 1})["prompt"]
    company_details, company_name = get_company_details(ticker=ticker)
    company_summary = ""
    if company_details:
        company_summary = f"""A brief about the company:
        {company_details}"""
    summarization_prompt_with_bullets = prompt.format(company_name=company_name, ticker=ticker, company_summary=company_summary, qa_pairs=qa_pairs, mda_text=mda_text, event_type=event_type)
    return summarization_prompt_with_bullets


def get_summarization_prompt(ticker, qa_pairs, mda_text, event_type):
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    prompt: str = prompt_collection.find_one({"prompt_name": "event_transcript_summary", "version": 1})["prompt"]
    company_details, company_name = get_company_details(ticker=ticker)
    company_summary = ""
    if company_details:
        company_summary = f"""A brief about the company:
        {company_details}"""
    summarization_prompt = prompt.format(company_name=company_name, ticker=ticker, company_summary=company_summary, qa_pairs=qa_pairs, mda_text=mda_text, event_type=event_type)
    return summarization_prompt


def create_event_transcript_summary(cutoff_date, lookback_days):
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    earnings_analysis_collection = connection.get_collection("earnings_analysis")
    events_output_collection = connection.get_collection("public_investor_events_outputs")
    qna_collection = connection.get_collection("qnas")
    mda_chunk_categories_collection = connection.get_collection("mda_chunk_categories")
    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "output_type": "SUMMARY",
        "summary": {"$exists": False}
    }

    with events_output_collection.find(query, no_cursor_timeout=True) as events_cursor:
        for event in events_cursor:
            event_id = event["event_id"]
            date = event["date"]
            if date < cutoff_date_start or date > cutoff_date_end:
                continue
            ticker = event.get("ticker")

            qnas = list(qna_collection.find({"event_id": event_id}))
            mdas = list(mda_chunk_categories_collection.find({"event_id": event_id}))

            qa_pairs = ""
            for qna in qnas:
                question = qna["question"]
                question_speaker = qna["question_speaker"]
                answer = qna["answer"]
                answer_speaker = qna["answer_speaker"]
                qa_pairs += f"{question_speaker}: {question}\n{answer_speaker}: {answer}\n\n"
            qa_pairs = qa_pairs.strip()

            mda_text = ""
            for _mda in mdas:
                current_mda_text = _mda["chunk"]
                mda_text += f"\n{current_mda_text}\n"
            mda_text = mda_text.strip()
            event_type = event.get("event_type")
            if mda_text == "" and qa_pairs == "":
                continue
            summarization_prompt_with_bullets = get_summarization_prompt_with_bullets(ticker=ticker, qa_pairs=qa_pairs, mda_text=mda_text, event_type=event_type)

            summarization_prompt = get_summarization_prompt(ticker=ticker, qa_pairs=qa_pairs, mda_text=mda_text, event_type=event_type)

            summary_bullets = openai_service.get_completion_without_limits(prompt=summarization_prompt_with_bullets, temperature=0)
            summary = openai_service.get_completion_without_limits(prompt=summarization_prompt, temperature=0)
            earnings_analysis = earnings_analysis_collection.find_one({"event_id": event_id})

            if earnings_analysis:
                analysis = earnings_analysis.get("analysis", "")

                if analysis:
                    summary_new = analysis + " " + summary
                    if analysis != "":
                        summary_bullets = f"- {analysis}\n\n" + summary_bullets.strip()
                else:
                    summary_new = summary
            else:
                summary_new = summary

            events_output_collection.update_one(
                {"event_id": event_id},
                {
                    "$set": {
                        "summary": summary_new.strip(),
                        "sentiment": "no_view",
                        'score': 0,
                        "net_th": 0,
                        "down_th": 0,
                        'net_score': 0,
                        'downtick_score': 0,
                        "uptick_score": 0,
                        'mean_downtick_score': 0,
                        'std_net_score': 0,
                        'std_downtick_score': 0,
                        'mean_net': 0,
                        "summary_body": summary.strip(),
                        "summary_message": None,
                        "summary_sources": [],
                        "summary_bullet": summary_bullets,
                        "summary_bullet_sources": [],
                    }
                },
                upsert=True
            )


if __name__ == "__main__":
    cutoff_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    create_event_transcript_summary(cutoff_date, 500)
