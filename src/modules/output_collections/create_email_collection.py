import pandas as pd
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger

logger = get_logger(__name__)


def calculate_mean_sd():
    connection = DatabaseFactory().get_mongo_connection()
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    mean_uptick_score_aggregation = [
        {
            '$match': {
                'event_type': 'non_earnings'
            }
        }, {
            '$group': {
                '_id': None,
                'mean_uptick_score': {
                    '$avg': '$uptick_score'
                }
            }
        }
    ]
    mean_uptick_score_result = list(public_investor_events_outputs_collection.aggregate(mean_uptick_score_aggregation))
    if mean_uptick_score_result:
        mean_uptick_score = mean_uptick_score_result[0]['mean_uptick_score']
    else:
        mean_uptick_score = 6.3  # Default value if aggregation fails

    # Calculate the standard deviation of uptick scores for non-earnings events
    std_dev_uptick_score_aggregation = [
        {
            '$match': {
                'event_type': 'non_earnings'
            }
        }, {
            '$group': {
                '_id': None,
                'stdDevUptickScore': {
                    '$stdDevSamp': '$uptick_score'
                }
            }
        }
    ]

    std_dev_uptick_score_result = list(public_investor_events_outputs_collection.aggregate(std_dev_uptick_score_aggregation))
    if std_dev_uptick_score_result:
        std_dev_uptick_score = std_dev_uptick_score_result[0]['stdDevUptickScore']
    else:
        std_dev_uptick_score = 7.4  # Default value if aggregation fails

    mean_downtick_score_aggregation = [
        {
            '$match': {
                'event_type': 'non_earnings'
            }
        }, {
            '$group': {
                '_id': None,
                'mean_downtick_score': {
                    '$avg': '$downtick_score'
                }
            }
        }
    ]
    mean_downtick_score_result = list(public_investor_events_outputs_collection.aggregate(mean_downtick_score_aggregation))
    if mean_downtick_score_result:
        mean_downtick_score = mean_downtick_score_result[0]['mean_downtick_score']
    else:
        mean_downtick_score = -2.5  # Default value if aggregation fails

    std_dev_downtick_score_aggregation = [
        {
            '$match': {
                'event_type': 'non_earnings'
            }
        }, {
            '$group': {
                '_id': None,
                'stdDevDowntickScore': {
                    '$stdDevSamp': '$downtick_score'
                }
            }
        }
    ]

    std_dev_downtick_score_result = list(public_investor_events_outputs_collection.aggregate(std_dev_downtick_score_aggregation))
    if std_dev_downtick_score_result:
        std_dev_downtick_score = std_dev_downtick_score_result[0]['stdDevDowntickScore']
    else:
        std_dev_downtick_score = 4.2  # Default value if aggregation fails
    return mean_uptick_score, std_dev_uptick_score, mean_downtick_score, std_dev_downtick_score


def generate_email(ticker, date):
    # find the event on that date, error if no event found
    connection = DatabaseFactory().get_mongo_connection()
    outputs_collection = connection.get_collection("outputs")
    earnings_anomalies_collection = connection.get_collection("earnings_anomalies")
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    event_email_collection = connection.get_email_collection("event_emails")
    mean_uptick_score, std_dev_uptick_score, mean_downtick_score, std_dev_downtick_score = calculate_mean_sd()
    mean_net_score = mean_uptick_score + mean_downtick_score
    sd_net_score = (std_dev_uptick_score + std_dev_downtick_score) * 0.5
    mean_absolute_score = abs(mean_uptick_score) + abs(mean_downtick_score)
    std_dev_absolute_score = (std_dev_uptick_score + std_dev_downtick_score)
    start_date = datetime.strptime(date, "%Y-%m-%d")
    end_date = start_date + timedelta(days=3)

    relevant_ticker_events = pd.DataFrame(list(public_investor_events_outputs_collection.find({
        "ticker": ticker,
        "datetime": {
            "$gte": start_date,
            "$lt": end_date
        }
    }, no_cursor_timeout=True)))

    body = ""
    if relevant_ticker_events.empty:
        logger.info(f"No event found for {ticker} on {date}")
        return
    else:
        for _, event in relevant_ticker_events.iterrows():
            event_id = event['event_id']
            if event_email_collection.find_one({"event_id": event_id}):
                logger.info("Email already exists and is approved")
                continue
            
            try:
                event_date = event['date']
                event_datetime = event['datetime']
                event_title = event['title']
                event_title_cropped = ','.join(event_title.split(',')[:-1])
                score = event['score']
                uptick_score = event['uptick_score']
                downtick_score = event['downtick_score']
                absolute_score = event['absolute_score']
                summary = event['summary']
            except KeyError as e:
                logger.error(f"KeyError: {e}")
                continue

            # check if event_id exists in earnings_anomalies collection

            earnings_anomalies = earnings_anomalies_collection.find_one({"event_id": event_id})
            # extract "anomalies" field from earnings_anomalies collection
            if earnings_anomalies:
                anomalies = earnings_anomalies["anomalies"]
            else:
                anomalies = None
            # Extract all the questions from outputs collection with the event_id along with all the other fields in outputs collections

            df4 = pd.DataFrame(list(outputs_collection.find({"event_id": event_id}, no_cursor_timeout=True)))
            if not df4.empty:
                uptick_questions = df4[df4["uptick_score"] > 0]
                downtick_questions = df4[df4["downtick_score"] > 0]
            else:
                uptick_questions = pd.DataFrame([])
                downtick_questions = pd.DataFrame([])
            # Find the most recent event for the ticker before the event_date
            previous_events = relevant_ticker_events[relevant_ticker_events["date"] < event_date]
            if not previous_events.empty:
                most_recent_event = previous_events.iloc[-1]
                if most_recent_event['sentiment'] == "positive":
                    prior_view = "Positive"
                elif most_recent_event['sentiment'] == "negative":
                    prior_view = "Negative"
                elif most_recent_event['sentiment'] == "stockUP":
                    prior_view = "Stock Up"
                elif most_recent_event['sentiment'] == "stockDOWN":
                    prior_view = "Stock Down"
                else:
                    prior_view = "Neutral"
                prior_view_date = most_recent_event['date'].strftime('%Y-%m-%d')
            else:
                prior_view = None
                prior_view_date = None
            # Create the email body
            body += f"Score: {score}\n"
            body += f"Absolute Score: {absolute_score}\n"
            body += f"Summary:\n{summary}\n\n"
            body += "Uptick Questions:\n"
            for _, row in uptick_questions.iterrows():
                body += f"Question: {row['question']}\n"
                body += f"Answer: {row['answer']}\n"
                if row['classification_final'] == 0.5:
                    similarity = "Somewhat Different"
                elif row['classification_final'] == 1:
                    similarity = "Very Different"
                else:
                    similarity = "Similar"
                body += f"{similarity}: {row['LLM_rationale_final']}\n"

                if row["LLM_trend_final"] == 1:
                    trend = "Uptick"
                elif row["LLM_trend_final"] == -1:
                    trend = "Downtick"
                body += f"{trend}: {row['LLM_trend_rationale_final']}\n"

            body += "\nDowntick Questions:\n"
            for _, row in downtick_questions.iterrows():
                body += f"Question: {row['question']}\n"
                body += f"Answer: {row['answer']}\n"
                if row['classification_final'] == 0.5:
                    similarity = "Somewhat Different"
                elif row['classification_final'] == 1:
                    similarity = "Very Different"
                else:
                    similarity = "Similar"
                body += f"{similarity}: {row['LLM_rationale_final']}\n"

                if row["LLM_trend_final"] == 1:
                    trend = "Uptick"
                elif row["LLM_trend_final"] == -1:
                    trend = "Downtick"
                body += f"{trend}: {row['LLM_trend_rationale_final']}\n"

            # Write to a MongoDB collection called event_emails
            event_email_collection.update_one(
                {"event_id": event_id},
                {"$set": {
                    "ticker": ticker,
                    "date": event_date,
                    "event_title": event_title_cropped,
                    "datetime": event_datetime,
                    "subject": f" Slated.ai | {ticker} | {event_title_cropped}",
                    "summary": summary,
                    "anomalies": anomalies,
                    "uptick_questions": uptick_questions.to_dict(orient='records'),
                    "downtick_questions": downtick_questions.to_dict(orient='records'),
                    "score": round(score, 2),
                    "downtick_score": round(downtick_score, 2),
                    "uptick_score": round(uptick_score, 2),
                    "absolute_score": round(absolute_score, 2),
                    "body": body,
                    "updated_at": datetime.now(),
                    "net_score_SD": round((score - mean_net_score) / sd_net_score, 2),
                    "absolute_score_SD": round((absolute_score - mean_absolute_score) / std_dev_absolute_score, 2),
                    "hit_rate": round(0.58, 2),
                    "uptick_score_SD": round((uptick_score - mean_uptick_score) / std_dev_uptick_score, 2),
                    "downtick_score_SD": round((downtick_score - mean_downtick_score) / std_dev_downtick_score, 2),
                    "prior_view": prior_view,
                    "prior_view_date": prior_view_date
                }},
                upsert=True
            )
            logger.info(f"Email for {ticker} on {date} created and saved to MongoDB")


def create_email_collection(date):
    connection = DatabaseFactory().get_mongo_connection()
    public_investor_events_output_cursor = connection.get_collection("public_investor_events_outputs")
    start_date = datetime.strptime(date, "%Y-%m-%d")
    end_date = start_date + timedelta(days=3)
    tickers = public_investor_events_output_cursor.distinct("ticker", {
        "date": {
            "$gte": start_date,
            "$lt": end_date
        }
    })

    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = []
        for ticker in tickers:
            logger.info(f"Creating email for ticker: {ticker}")
            future = executor.submit(generate_email, ticker, date)
            futures.append(future)

        # Wait for all tasks to complete
        for future in futures:
            future.result()

    logger.info(f"Email collection created for {date}")
    return


if __name__ == "__main__":
    date_email = (datetime.now() + timedelta(days=-1)).strftime("%Y-%m-%d")
    create_email_collection(date_email)
