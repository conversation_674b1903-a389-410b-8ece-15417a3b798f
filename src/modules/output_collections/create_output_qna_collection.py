from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger
from src.database.mongo.queries import delete_records_marked_for_deletion


logger = get_logger(__name__)


MIN_QUESTION_LENGTH = 10


def create_output_qna_collection(cutoff_date, lookback_days, min_answer_length):
    """
    Creates a final collection combining all relevant Q&A data, similarity scores, entailment,
    LLM classification, and rationale.
    """
    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)
    connection = DatabaseFactory().get_mongo_connection()

    companies_collection = connection.get_collection("companies")
    final_qna_collection = connection.get_collection("outputs")
    qna_collection = connection.get_collection("qnas")
    similar_questions_collection = connection.get_collection("similar_questions")
    entailments_collection = connection.get_collection("entailments")
    llm_answer_collection = connection.get_collection("LLM_answers")
    llm_similarity_collection = connection.get_collection("LLM_similarity")
    llm_trends_collection = connection.get_collection("LLM_trend")
    llm_insights_collection = connection.get_collection("LLM_insights")
    gemini_llm_similarity_collection = connection.get_collection("Gemini_LLM_similarity")
    qna_flags_collection = connection.get_collection("qna_flags")
    summaries_collection = connection.get_collection("summaries")
    qna_importance_collection = connection.get_collection("qna_importance")

    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return

    query = {
        "ticker": {"$in": tickers}, "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end}
    }
    deleted_events = []
    # Use no_cursor_timeout to prevent timeouts and process in batches
    with qna_collection.find(query, no_cursor_timeout=True).batch_size(5) as qnas:
        ctr = 0
        for qna_flag in qnas:
            qna_id = qna_flag["_id"]
            logger.info(f"Creating Ouput collection for qnaID: {qna_id}")
            event_id = qna_flag["event_id"]
            if event_id not in deleted_events:
                delete_records_marked_for_deletion(event_id=event_id, coll=final_qna_collection)
                deleted_events.append(event_id)
            ctr += 1

            # Fetch data from other collections based on qnaId
            similar_questions = similar_questions_collection.find_one({"qnaId": qna_id}) or {}
            entailment = entailments_collection.find_one({"qnaId": qna_id}) or {}
            llm_answer = llm_answer_collection.find_one({"qnaId": qna_id}) or {}
            LLM_similarity = llm_similarity_collection.find_one({"qnaId": qna_id}) or {}
            gemini_llm_similarity = gemini_llm_similarity_collection.find_one({"qnaId": qna_id}) or {}
            qna_flags_data = qna_flags_collection.find_one({"qnaId": qna_id}) or {}
            summaries = summaries_collection.find_one({"qnaId": qna_id}) or {}
            llm_trend = llm_trends_collection.find_one({"qnaId": qna_id}) or {}
            llm_insight = llm_insights_collection.find_one({"qnaId": qna_id}) or {}
            qna_importance = qna_importance_collection.find_one({"qnaId": qna_id}) or {}
            importance_score = 1 if qna_importance.get("importance") == "Very Important" else 0.5 if qna_importance.get("importance") == "Somewhat Important" else 0

            # Calculate qna_flags
            qna_flags = (
                qna_flags_data.get("entailment_roberta_flag", False) +
                qna_flags_data.get("llm_similarity_flag", False)
            ) * importance_score

            upticks = 1 if llm_trend.get("trend") == "uptick" else 0
            downticks = 1 if llm_trend.get("trend") == "downtick" else 0

            uptick_score = upticks * importance_score
            downtick_score = downticks * importance_score

            show = (
                qna_flag.get("answer") is not None and len(qna_flag.get("answer", "")) > min_answer_length and
                qna_flag.get("question") is not None and len(qna_flag.get("question", "")) > MIN_QUESTION_LENGTH
            )

            # Construct the final document
            final_document = {
                "qna_id": qna_id,
                "event": qna_flag.get("event"),
                "event_id": qna_flag.get("event_id"),
                "ticker": qna_flag.get("ticker"),
                "question_speaker": qna_flag.get("question_speaker"),
                "question": qna_flag.get("question"),
                "answer_speaker": qna_flag.get("answer_speaker"),
                "answer": qna_flag.get("answer"),
                "similar_questions_faiss": similar_questions.get("related_qna_faiss", []),
                "entailment": entailment.get("entailment"),
                "entailment_rationale": entailment.get("Rationale"),
                "entailment_probability_contradiction": entailment.get("entailment_probability_contradiction"),
                "entailment_probability_neutral": entailment.get("entailment_probability_neutral"),
                "entailment_probability_entailment": entailment.get("entailment_probability_entailment"),
                "classification_gpt": {"Similar": 0, "Somewhat Different": 0.5, "Very Different": 1}.get(LLM_similarity.get("classification")),
                "LLM_rationale_gpt": LLM_similarity.get("rationale"),
                "classification_gemini": {"Similar": 0, "Somewhat Different": 0.5, "Very Different": 1}.get(gemini_llm_similarity.get("classification")),
                "LLM_rationale_gemini": gemini_llm_similarity.get("rationale"),
                "classification_final": {"Similar": 0, "Somewhat Different": 0.5, "Very Different": 1}.get(LLM_similarity.get("classification")),
                "LLM_rationale_final": LLM_similarity.get("rationale"),
                "LLM_answer_llama": llm_answer.get("LLM_answer_llama"),
                "LLM_answer_llama_similarity": llm_answer.get("LLM_answer_llama_similarity"),
                "LLM_answer_final": llm_answer.get("LLM_answer_llama"),
                "LLM_answer_final_similarity": llm_answer.get("LLM_answer_llama_similarity"),
                "LLM_trend_final": {"uptick": 1, "downtick": -1}.get(llm_trend.get("trend")),
                "LLM_trend_rationale_final": llm_trend.get("trend_rationale"),
                "date": qna_flag.get("date"),
                "section": qna_flag.get("section"),
                "company_name": "",
                "LLM_summary": summaries.get("summary"),
                "qna_flags": qna_flags,
                "show": show,
                "category": qna_importance.get("category"),
                "importance": qna_importance.get("importance"),
                "sector": qna_importance.get("sector"),
                "upticks": upticks,
                "downticks": downticks,
                "uptick_score": uptick_score,
                "downtick_score": downtick_score,
                "uptickminusdowntick": upticks - downticks,
                "LLM_insight": llm_insight.get("insight"),
                "score": uptick_score - downtick_score,
                "updated_at": datetime.now()
            }

            final_qna_collection.update_one({"qnaId": qna_id}, {"$set": final_document}, upsert=True)
            logger.info(f"Completed Creating Ouput collection for qnaID: {qna_id}")

    logger.info("Final QNA collection created")
