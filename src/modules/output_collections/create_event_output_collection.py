import re
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def create_event_output_collection(cutoff_date, lookback_days):
    connection = DatabaseFactory().get_mongo_connection()
    events_collection = connection.get_collection("public_investor_events")
    llm_mda_trend_collection = connection.get_collection("LLM_MDA_trend")
    mda_outputs_collection = connection.get_collection("mda_outputs")
    companies_collection = connection.get_collection("companies")
    qna_flags_collection = connection.get_collection("qna_flags")
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")

    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return

    query = {
        "ticker": {"$in": tickers}, "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end}
    }
    deleted_events = []

    with events_collection.find(query, no_cursor_timeout=True).batch_size(200) as events_cursor:
        for event in events_cursor:
            event_id = event["_id"]
            logger.info(f"Start processing Event id: {event_id}")
            if event_id not in deleted_events:
                delete_records_marked_for_deletion(event_id=event_id, coll=public_investor_events_outputs_collection)
                deleted_events.append(event_id)

            if public_investor_events_outputs_collection.find_one({"event_id": event_id}):
                logger.info(f"Output for Event id: {event_id} already exists. Processing Skipped")
                continue

            date = event["date"]
            if date < cutoff_date_start or date > cutoff_date_end:
                logger.info(f"Dates outside the processing dates for Event Id: {event_id}. Processing skipped")
                continue

            ticker = event.get("ticker", None)
            # find the sector for the ticker from companies collection
            company = companies_collection.find_one({"ticker": ticker})
            sector = company.get("sector", None) if company else None

            llm_llama_flag_count = qna_flags_collection.count_documents({"event_id": event_id, "llm_llama_flag": True})
            llm_similarity_flag_count = qna_flags_collection.count_documents({"event_id": event_id, "llm_similarity_flag": True})
            entailment_roberta_flag_count = qna_flags_collection.count_documents({"event_id": event_id, "entailment_roberta_flag": True})
            similar_questions_faiss_flag_count = qna_flags_collection.count_documents({"event_id": event_id, "similar_questions_faiss_flag": True})

            event_flag_count = llm_llama_flag_count + llm_similarity_flag_count + entailment_roberta_flag_count + similar_questions_faiss_flag_count

            upticks = qna_flags_collection.count_documents({"event_id": event_id, "uptick": True}) if qna_flags_collection.count_documents({"event_id": event_id, "uptick": True}) else 0
            downticks = qna_flags_collection.count_documents({"event_id": event_id, "downtick": True}) if qna_flags_collection.count_documents({"event_id": event_id, "downtick": True}) else 0

            mda_upticks = llm_mda_trend_collection.count_documents({"event_id": event_id, "trend": "uptick"}) if llm_mda_trend_collection.count_documents({"event_id": event_id, "trend": "uptick"}) else 0
            mda_downticks = llm_mda_trend_collection.count_documents({"event_id": event_id, "trend": "downtick"}) if llm_mda_trend_collection.count_documents({"event_id": event_id, "trend": "downtick"}) else 0

            mda_count = mda_outputs_collection.count_documents({"event_id": event_id, "LLM_insight": {"$ne": None}})

            total_upticks = (upticks or 0) + (mda_upticks or 0)
            total_downticks = (downticks or 0) + (mda_downticks or 0)

            uptick_score = sum([qna["score"] for qna in qna_flags_collection.find({"event_id": event_id, "uptick": True}, no_cursor_timeout=True)]) or 0
            downtick_score = sum([qna["score"] for qna in qna_flags_collection.find({"event_id": event_id, "downtick": True}, no_cursor_timeout=True)]) or 0
            if abs(uptick_score) > 0 or abs(downtick_score) > 0:
                event_flag = True
            else:
                event_flag = False

            title = event.get("title", None)

            event_type = "earnings" if "earnings call" in event.get("title", "").lower() else "non_earnings"

            if event_type == "earnings":
                if re.search(r'Q\d', title):
                    title_short = title.split("Q")[1]
                    title_short = "Q" + title_short
                    # remove after the last , in the title; if there is no , keep whole
                    if "," in title_short:
                        title_short = title_short.split(",")[0]

                else:
                    title_short = title

            else:
                # if title contains "Presents at" keep after "Presents at" until the ,Jan etc after that
                if "Presents at " in title:
                    split_title = re.split(r"Presents at |, (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)", title)
                    if len(split_title) > 2:
                        title_short = split_title[2]
                    else:
                        title_short = title
                else:
                    title_short = title

            # Collect all qnaIds for the event where either llm_llama_flag, entailment_roberta_flag, similar_questions_faiss_flag, or llm_similarity_flag is True
            query = {
                "event_id": event_id,
                "$or": [
                    {"llm_llama_flag": True},
                    {"entailment_roberta_flag": True},
                    {"similar_questions_faiss_flag": True},
                    {"llm_similarity_flag": True},
                    {"uptick": True},
                    {"downtick": True}
                ]
            }

            # If there are documents, proceed with the cursor
            qna_in_event = qna_flags_collection.find(query, no_cursor_timeout=True)

            # Collect qnaIds from the cursor
            qnaIds = [qna["qnaId"] for qna in qna_in_event]

            if qnaIds and mda_count:
                output_type = "QNA_MDA"
            elif qnaIds:
                output_type = "QNA"
            elif mda_count:
                output_type = "MDA"
            else:
                output_type = "SUMMARY"

            document = {
                "event_id": event_id,
                "title": title,
                "sector": sector,
                "date": event.get("date", None),
                "llm_llama_flag_count": llm_llama_flag_count if llm_llama_flag_count else None,
                "entailment_roberta_flag_count": entailment_roberta_flag_count if entailment_roberta_flag_count else None,
                "similar_questions_faiss_flag_count": similar_questions_faiss_flag_count if similar_questions_faiss_flag_count else None,
                "llm_similarity_flag_count": llm_similarity_flag_count if llm_similarity_flag_count else None,
                "event_flag": event_flag,
                "qns_with_flags": qnaIds if qnaIds else [],
                "analysts": event.get("analysts", None),
                "corp_reps": event.get("corp_reps", None),
                "factset_event_id": event.get("factset_event_id", None),
                "datetime": event.get("datetime", None),
                "file_name": event.get("file_name", None),
                "company_id": event.get("company_id", None),
                "s3_object_url": event.get("s3_object_url", None),
                "s3_slides_url": event.get("s3_slides_url", None),
                "flag_count": event_flag_count if event_flag_count else 0,
                "ticker": event.get("ticker", None),
                "upticks": upticks if upticks else 0,
                "downticks": downticks if downticks else 0,
                "mda_upticks": mda_upticks if mda_upticks else 0,
                "mda_downticks": mda_downticks if mda_downticks else 0,
                "total_upticks": total_upticks,
                "total_downticks": total_downticks,
                "uptick_score": uptick_score if uptick_score else 0,
                "downtick_score": downtick_score if downtick_score else 0,
                "show": True,
                "updated_at": datetime.now(),
                "absolute_score": abs(uptick_score) + abs(downtick_score),
                "event_type": event_type,
                "title_short": title_short,
                "output_type": output_type,
                "is_operator_instruction_removed": 1
            }

            public_investor_events_outputs_collection.update_one(
                {"event_id": document["event_id"]},
                {"$set": document},
                upsert=True
            )
            logger.info(f"Generated Event output for Event Id: {event_id}")

    logger.info("event output collections updated")


if __name__ == "__main__":
    cutoff_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    create_event_output_collection(cutoff_date=cutoff_date, lookback_days=1000)
