"""
Creates summaries for events which has either MDA insights or QNA Insights.
"""
import re
import hashlib
from bson import ObjectId
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.core.logging import get_logger
from src.modules.earnings_message.processors.event_processor import process_single_earnings_event

logger = get_logger(__name__)


def encode_mongo_id(mongo_id):
    if isinstance(mongo_id, ObjectId):
        mongo_id = str(mongo_id)
    hash_object = hashlib.md5(mongo_id.encode())
    digest = hash_object.digest()
    value = int.from_bytes(digest[:3], byteorder='big')

    def base36_encode(number):
        chars = '0123456789abcdefghijklmnopqrstuvwxyz'
        result = ''
        while number > 0:
            number, remainder = divmod(number, 36)
            result = chars[remainder] + result
        return result or '0'

    encoded = base36_encode(value)
    encoded = encoded.rjust(4, 'a')
    if len(encoded) > 4:
        encoded = encoded[-4:]
    return encoded


def decode_mongo_id(encoded_id, mda_ids, qna_ids):
    # Convert ObjectIds to strings for both arrays
    mda_ids = [str(oid) if isinstance(oid, ObjectId) else oid for oid in mda_ids]
    qna_ids = [str(oid) if isinstance(oid, ObjectId) else oid for oid in qna_ids]

    # Check MDA IDs first
    for original_id in mda_ids:
        if encode_mongo_id(original_id) == encoded_id:
            return original_id, "MDA"

    # Then check QNA IDs
    for original_id in qna_ids:
        if encode_mongo_id(original_id) == encoded_id:
            return original_id, "QNA"

    return None, None


def replace_sup_tags(text, event_id, mda_ids, qna_ids):
    pattern = r'<sup>(.*?)</sup>'
    source_map = {}
    serial_counter = 1

    def replacement(match):
        nonlocal serial_counter
        mongo_id = match.group(1)
        actual_id, id_type = decode_mongo_id(mongo_id, mda_ids, qna_ids)

        if actual_id is None:
            # If ID not found in either array, return original tag
            return match.group(0)

        # Check if we've already mapped this ID
        for key, val in source_map.items():
            if val.endswith(actual_id) or val.endswith(f"{actual_id}?insightsListTab=1"):
                return f'<sup>{key}</sup>'

        # Create new entry if not found
        new_serial = str(serial_counter)
        if id_type == "MDA":
            source_map[new_serial] = f"/event/{str(event_id)}/mda/{actual_id}?insightsListTab=1"
        else:
            source_map[new_serial] = f"/event/{str(event_id)}/{actual_id}"
        serial_counter += 1
        return f'<sup>{new_serial}</sup>'

    modified_text = re.sub(pattern, replacement, text)
    return modified_text, source_map


def get_company_details(ticker):
    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    company = companies_collection.find_one({"ticker": ticker})
    company_name = company["company_name"]
    company_details_collection = connection.get_collection("company_details")
    company_details = company_details_collection.find_one({"ticker": ticker})
    company_summary = None
    if company_details:
        company_summary = company_details.get("company_summary")
    return company_summary, company_name


def get_summarization_prompt(ticker, event_type, qa_pairs, mda_sections, event_sentiment, uptick_count, downtick_count):
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    if event_type != "earnings":
        summary_start = "Found {uptick_count} upticks and {downtick_count} downticks, and was overall {event_sentiment}.".format(uptick_count=uptick_count, downtick_count=downtick_count, event_sentiment=event_sentiment)
    else:
        summary_start = "Found {uptick_count} upticks and {downtick_count} downticks".format(uptick_count=uptick_count, downtick_count=downtick_count)

    company_details, company_name = get_company_details(ticker=ticker)
    company_summary = ""
    if company_details:
        company_summary = f"""A brief about the company:
        {company_details}"""

    prompt = prompt_collection.find_one({"prompt_name": "event_summary_prompt_v3"})["prompt"]
    summarization_prompt = prompt.format(company_name=company_name, ticker=ticker, event_type=event_type, qa_pairs=qa_pairs, mda_sections=mda_sections, company_summary=company_summary, event_sentiment=event_sentiment, summary_start=summary_start)
    return summarization_prompt


def get_summarization_prompt_bullets(ticker, event_type, qa_pairs, mda_sections, event_sentiment):
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    company_details, company_name = get_company_details(ticker=ticker)
    company_summary = ""
    if company_details:
        company_summary = f"""A brief about the company:
        {company_details}"""
    prompt: str = prompt_collection.find_one({"prompt_name": "event_summary_prompt_bullets_v3"})["prompt"]

    summarization_prompt = prompt.format(company_name=company_name, ticker=ticker, event_type=event_type, qa_pairs=qa_pairs, mda_sections=mda_sections, company_summary=company_summary, event_sentiment=event_sentiment)
    return summarization_prompt


def create_event_summary(cutoff_date, lookback_days, anaysis_window_days):
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    qna_collection = connection.get_collection("qnas")
    llm_insights_collection = connection.get_collection("LLM_insights")
    qna_importance_collection = connection.get_collection("qna_importance")
    output_collection = connection.get_collection("outputs")
    qnas_collection = connection.get_collection("qnas")
    events_collection = connection.get_collection("public_investor_events_outputs")
    earnings_analysis_collection = connection.get_collection("earnings_analysis")
    mda_outputs_collection = connection.get_collection("mda_outputs")
    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)
    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "summary": {"$exists": False},
        "output_type": {"$ne": "SUMMARY"}
    }

    with events_collection.find(query, no_cursor_timeout=True) as events_cursor:

        for event in events_cursor:
            event_id = event["event_id"]
            date = event["date"]
            if date < cutoff_date_start or date > cutoff_date_end:
                continue
            # Check if the ticker is in the consumer_disc or consumer_staples sector
            ticker = event.get("ticker")

            if "summary" in event and event["summary"] is not None:
                continue

            if event["upticks"] is None:
                uptick_count = 0
            else:
                uptick_count = event.get("upticks", 0)

            if event["downticks"] is None:
                downtick_count = 0
            else:
                downtick_count = event.get("downticks", 0)

            event_details = ""

            uptick_count = event["upticks"]
            downtick_count = event["downticks"]
            # score = event['score']
            ticker = event['ticker']

            # load the qns_with_flags from the event into a dataframe and then import the question, question speaker, answer, answer speaker, llm classification, llm rationale, trend, trend rationale into a dataframe
            qna_ids = event["qns_with_flags"]

            for qnaId in qna_ids:
                qna_doc = qna_collection.find_one({"_id": qnaId})
                if not qna_doc:
                    continue

                qna_insight = llm_insights_collection.find_one({"qnaId": qnaId})
                insight = qna_insight.get("insight") if qna_insight else " "

                qna_importance = qna_importance_collection.find_one({"qnaId": qnaId})
                importance = qna_importance.get("importance") if qna_importance else "Not Important"
                if importance == "Not Important":
                    continue

                event_details += f" {insight} ,"   # Create a prompt for the event

            # find all documents from MDA_outputs collection for event_id, then extract LLM_insight and LLM_trend_final from the document and add to the event_details

            insights = list(llm_insights_collection.find({"event_id": event_id}))
            original_qna_ids = []
            qa_pairs = ""
            for _insight in insights:
                output_id = output_collection.find_one({"qnaId": _insight["qnaId"]})
                if output_id is not None:
                    output_id = output_id["_id"]
                else:
                    continue

                qna = qnas_collection.find_one({"_id": qnaId})
                if not qna:
                    continue
                insight_trend = _insight["trend"]
                encoded_output_id = encode_mongo_id(output_id)
                original_qna_ids.append(output_id)
                insight = _insight["insight"]
                qnaId = _insight["qnaId"]
                question = qna["question"]
                question_speaker = qna["question_speaker"]
                answer = qna["answer"]
                answer_speaker = qna["answer_speaker"]
                qa_pairs += f"Id:*{encoded_output_id}*\n{question_speaker}: {question}\n{answer_speaker}: {answer}\nInsight({insight_trend}): {insight}\n\n"

            original_mda_ids = []
            mdas = list(mda_outputs_collection.find({"event_id": event_id}))
            mda_text = ""
            for _mda in mdas:
                output_id = _mda["_id"]
                if not _mda["LLM_insight"]:
                    continue

                encoded_output_id = encode_mongo_id(output_id)
                original_mda_ids.append(output_id)
                insight_trend = _mda["LLM_trend_final"]
                insight = _mda["LLM_insight"]
                current_mda_text = _mda["MDA_text_current"]
                mda_text += f"Id:*{encoded_output_id}*\nMDA Text:{current_mda_text}\nInsight({insight_trend}): {insight}\n"

            sentiment = event.get("sentiment")
            summarization_prompt = get_summarization_prompt(ticker=ticker, event_type=event["event_type"], qa_pairs=qa_pairs, mda_sections=mda_text, event_sentiment=sentiment, uptick_count=uptick_count, downtick_count=downtick_count)

            summarization_prompt_bullet = get_summarization_prompt_bullets(ticker=ticker, event_type=event["event_type"], qa_pairs=qa_pairs, mda_sections=mda_text, event_sentiment=sentiment)

            with ThreadPoolExecutor(max_workers=2) as executor:
                future1 = executor.submit(openai_service.get_completion_without_limits, prompt=summarization_prompt, temperature=0)
                future2 = executor.submit(openai_service.get_completion_without_limits, prompt=summarization_prompt_bullet, temperature=0)

            response = future1.result()
            response_bullet = future2.result()

            summary, summary_sources = replace_sup_tags(response, event_id, original_mda_ids, original_qna_ids)
            summary_bullets, summary_bullet_sources = replace_sup_tags(response_bullet, event_id, original_mda_ids, original_qna_ids)

            logger.info(f"Summary for event {event_id}: {summary}")
            # remove the first sentence from summary and store the rest into the summary_short

            summary_body = summary.split(".", 1)[1]
            summary_message = summary.split(".", 1)[0]
            summary_new = " "

            # find analysis from earnings_analysis for the event_id
            analysis_bullet = None
            earnings_analysis_doc = earnings_analysis_collection.find_one({"event_id": event_id})
            if not earnings_analysis_doc:
                earnings_analysis_doc = process_single_earnings_event(event, cutoff_date_start, cutoff_date_end)
                if earnings_analysis_doc:
                    earnings_analysis_collection.update_one(
                        {"event_id": event_id},
                        {"$set": earnings_analysis_doc},
                        upsert=True
                    )

            if earnings_analysis_doc:
                analysis = earnings_analysis_doc.get("analysis")

                if analysis and analysis.strip() != "":
                    analysis_bullet = analysis

            summary_message_no_sentiment = summary_message.split(",")[0]
            summary_new = summary_message_no_sentiment + ". " + summary_body

            # upsert the summary into public_investor_events collection
            events_collection.update_one(
                {"event_id": event_id},
                {
                    "$set": {
                        "summary": summary_new.strip(),
                        "earnings_analysis": analysis_bullet,
                        "summary_body": summary_body.strip(),
                        "summary_message": summary_message.strip(),
                        "summary_sources": summary_sources,
                        "summary_bullet": summary_bullets.strip(),
                        "summary_bullet_sources": summary_bullet_sources,
                    }
                },
                upsert=True
            )
            logger.info(f"Summary for event {event_id} updated in database.")


if __name__ == "__main__":
    cutoff_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    create_event_summary(cutoff_date, 3, 180)
