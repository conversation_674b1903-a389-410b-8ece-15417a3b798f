from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
import threading
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def process_single_question(question, cutoff_date_start, cutoff_date_end, LLM_similarity_threshold, entailment_probability_contradiction_threshold, similar_questions_faiss_threshold, deleted_events_set, delete_lock):
    """Process a single question and return the document to be inserted/updated"""
    connection = DatabaseFactory().get_mongo_connection()
    llm_answer_collection = connection.get_collection("LLM_answers")
    entailments_collection = connection.get_collection("entailments")
    llm_similarity_collection = connection.get_collection("LLM_similarity")
    similar_questions_collection = connection.get_collection("similar_questions")
    llm_trend_collection = connection.get_collection("LLM_trend")
    qna_importance_collection = connection.get_collection("qna_importance")
    qna_flags_collection = connection.get_collection("qna_flags")

    qnaId = question["_id"]
    ticker = question["ticker"]
    question_text = question["question"]
    answer_text = question["answer"]
    date = question["date"]
    event_id = question["event_id"]
    question_speaker = question["question_speaker"]
    answer_speaker = question["answer_speaker"]

    llm_llama_flag = False
    entailment_roberta_flag = False
    similar_questions_faiss_flag = False
    llm_similarity_flag = False

    with delete_lock:
        if event_id not in deleted_events_set:
            delete_records_marked_for_deletion(event_id=event_id, coll=qna_flags_collection)
            deleted_events_set.add(event_id)

    if date < cutoff_date_start or date > cutoff_date_end:
        return None

    flag_count = 0

    # Set llm_llama_flag to 1 if LLM generated an answer with similarity <0.5
    llm_answer = llm_answer_collection.find_one({"qnaId": qnaId})
    if llm_answer:
        logger.info(llm_answer["LLM_answer_llama_similarity"])
        if llm_answer["LLM_answer_llama_similarity"] < LLM_similarity_threshold:
            llm_llama_flag = True
            flag_count += 1

    # Set entailment_roberta_flag to 1 if the entailment is "entailment"
    entailment = entailments_collection.find_one({"qnaId": qnaId})
    if entailment:
        if entailment["entailment_probability_contradiction"] > entailment_probability_contradiction_threshold:
            entailment_roberta_flag = True
            flag_count += 1

    # Set LLM_similarity_flag to 1 if LLM generated an answer with similarity <0.5
    LLM_similarity = llm_similarity_collection.find_one({"qnaId": qnaId})
    if LLM_similarity:
        if LLM_similarity["classification"] == "Very Different" or LLM_similarity["classification"] == "Somewhat Different":
            llm_similarity_flag = True
            flag_count += 1

    # Set similar_questions_faiss_flag to 1 if there are Similar questions from FAISS with similarity between 0.4 and 0.7
    similar_questions = similar_questions_collection.find_one({"qnaId": qnaId})
    if similar_questions:
        similar_questions_faiss = similar_questions["related_qna_faiss"]
        if similar_questions_faiss:
            for similar_question in similar_questions_faiss:
                if 0.4 <= similar_question["similarity"] < similar_questions_faiss_threshold:
                    similar_questions_faiss_flag = True
                    flag_count += 1
                    break

    trends = llm_trend_collection.find_one({"qnaId": qnaId}) or {}
    uptick = False
    downtick = False
    if trends:
        if trends["trend"] == "uptick":
            uptick = True
        else:
            if trends["trend"] == "downtick":
                downtick = True

    qna_importance = qna_importance_collection.find_one({"qnaId": qnaId}) or {}
    importance_score = 1 if qna_importance.get("importance") == "Very Important" else 0.5 if qna_importance.get("importance") == "Somewhat Important" else 0

    logger.info(f"LLM {llm_llama_flag}")
    logger.info(f"Entailment {entailment_roberta_flag}")
    logger.info(f"Similar questions FAISS {similar_questions_faiss_flag}")
    logger.info(f"LLM similarity {llm_similarity_flag}")

    document = {
        "qnaId": qnaId,
        "event_id": event_id,
        "ticker": ticker,
        "question": question_text,
        "answer": answer_text,
        "date": date,
        "question_speaker": question_speaker,
        "answer_speaker": answer_speaker,
        "llm_llama_flag": llm_llama_flag,
        "entailment_roberta_flag": entailment_roberta_flag,
        "similar_questions_faiss_flag": similar_questions_faiss_flag,
        "llm_similarity_flag": llm_similarity_flag,
        "uptick": uptick,
        "downtick": downtick,
        "importance": importance_score,
        "flag_count": flag_count,
        "score": flag_count * importance_score * (+1 if uptick else -1 if downtick else 0),
        "updated_at": datetime.now()
    }

    return (qnaId, document)


def create_qna_flags_collection(cutoff_date, lookback_days, LLM_similarity_threshold, entailment_probability_contradiction_threshold, similar_questions_faiss_threshold, max_workers=10):
    connection = DatabaseFactory().get_mongo_connection()

    qna_collection = connection.get_collection("qnas")
    companies_collection = connection.get_collection("companies")
    qna_flags_collection = connection.get_collection("qna_flags")

    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end}, 
        "ticker": {"$in": tickers}
    }

    deleted_events_set = set()
    delete_lock = threading.Lock()

    # Collect all questions first
    questions = []
    with qna_collection.find(query, no_cursor_timeout=True).batch_size(200) as qnas:
        questions = list(qnas)

    # Process questions in parallel
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for question in questions:
            future = executor.submit(
                process_single_question,
                question,
                cutoff_date_start,
                cutoff_date_end,
                LLM_similarity_threshold,
                entailment_probability_contradiction_threshold,
                similar_questions_faiss_threshold,
                deleted_events_set,
                delete_lock
            )
            futures.append(future)

        for future in futures:
            try:
                result = future.result()
                if result is not None:
                    qnaId, document = result
                    qna_flags_collection.update_one(
                        {"qnaId": qnaId},
                        {"$set": document},
                        upsert=True
                    )
            except Exception as e:
                logger.error(f"Error processing question: {e}")
