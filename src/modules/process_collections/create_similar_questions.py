import faiss
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def convert_np_float32_to_float(data):
    if isinstance(data, dict):
        return {k: convert_np_float32_to_float(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [convert_np_float32_to_float(item) for item in data]
    elif isinstance(data, np.float32):
        return float(data)
    else:
        return data


def create_similar_questions_collection(analysis_window_days, min_similarity, topn, min_question_length, min_answer_length, cutoff_date, lookback_days):
    """following code creates a collection of Similar questions based on cosine similarity of embeddings using faiss"""
    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    embedding_collection = connection.get_collection("embeddings")
    similar_questions_collection = connection.get_collection("similar_questions")
    current_datetime = datetime.now()
    logger.info(f"checkpoint alpha{current_datetime}")

    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return

    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    for ticker in tickers:
        logger.info(f"Processing ticker: {ticker}")
        logger.info(cutoff_date_start)
        logger.info("to")
        logger.info(cutoff_date_end)
        # Query only the documents matching the current ticker and date range, using lookback_date as we need older similar qns
        query = {
            "ticker": ticker,
            "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end}
        }

        with embedding_collection.find(query, no_cursor_timeout=True).batch_size(2) as embeddings_cursor:
            questions_list = list(embeddings_cursor)
            if not questions_list:
                logger.info(f"No questions found for ticker {ticker}")
                continue

            ctr = 0
            expected_dim = 1024  # Set the expected dimension of the embeddings
            deleted_events = []

            for question in questions_list:
                current_date = question['date']
                current_question = question['question']
                current_embedding = question['question_embedding']
                current_answer = question['answer']
                current_ticker = question['ticker']
                current_section = question['section']
                current_qnaId = question['qnaId']
                current_event_id = question['event_id']
                logger.info(f"starting to process questions for QNA Id: {current_qnaId}")
                if current_event_id not in deleted_events:
                    delete_records_marked_for_deletion(event_id=current_event_id, coll=similar_questions_collection)
                    deleted_events.append(current_event_id)

                if current_date < cutoff_date_start or current_date > cutoff_date_end:
                    logger.info("outside dates")
                    continue
                if current_section == "MDA":
                    logger.info("MDA")
                    continue

                if current_question is None or current_answer is None:
                    logger.info("qns/ans empty")
                    continue

                if len(current_question) < min_question_length:
                    logger.info("qns too short")
                    continue

                if len(current_answer) < min_answer_length:
                    logger.info("ans too short")
                    continue

                # if current_qnaId exists inside similar_questions collection, skip
                if similar_questions_collection.find_one({"qnaId": current_qnaId}):
                    logger.info("Similar questions already exists")
                    continue

                if np.isnan(current_embedding).any():
                    logger.info("embeddings empty")
                    continue

                analysis_end_date = current_date - timedelta(days=1)
                analysis_start_date = analysis_end_date - timedelta(days=analysis_window_days)

                questions_df = embedding_collection.find({
                    "date": {"$gte": analysis_start_date, "$lte": analysis_end_date},
                    "ticker": current_ticker
                })
                filtered_data = pd.DataFrame(questions_df)

                filtered_data_faiss = filtered_data.copy()

                # USING COSINE SIMILARITY IN FAISS ###

                if filtered_data_faiss.empty:
                    logger.info("filtered data empty")
                    continue

                # Normalize the current embedding for cosine similarity
                current_embedding = current_embedding / np.linalg.norm(current_embedding)

                valid_embedding = []
                original_indices = []  # Store original indices of valid embeddings

                # Collect valid embeddings and store their original indices
                for idx, emb in enumerate(filtered_data_faiss['question_embedding'].values):
                    if emb is not None:
                        emb_array = np.array(emb)
                        if emb_array.shape == (expected_dim,):
                            valid_embedding.append(emb_array)
                            original_indices.append(idx)  # Store the index of the valid embedding

                if not valid_embedding:
                    logger.info("not valid embedding")
                    continue

                # Convert filtered embeddings to a numpy array and normalize
                filtered_embeddings = np.vstack(valid_embedding)
                filtered_embeddings = filtered_embeddings / np.linalg.norm(filtered_embeddings, axis=1, keepdims=True)

                # Build a new Faiss index with the filtered data
                dimension = filtered_embeddings.shape[1]
                index = faiss.IndexFlatIP(dimension)  # IP for cosine similarity
                index.add(filtered_embeddings)

                # Reshape current_embedding to be a 2D array
                current_embedding_reshaped = current_embedding.reshape(1, -1)

                # Query FAISS index for nearest neighbors
                distances, faiss_indices = index.search(current_embedding_reshaped, k=topn)

                # Convert distances to similarities
                similarities = distances[0]

                # Filter based on min_similarity for both indices and similarities
                valid_indices_and_similarities = [(original_indices[i], float(s)) for i, s in zip(faiss_indices[0], similarities) if s >= min_similarity]

                # Unzip valid indices and similarities
                if valid_indices_and_similarities:
                    valid_indices, valid_similarities = zip(*valid_indices_and_similarities)
                else:
                    valid_indices, valid_similarities = [], []

                if not valid_indices:
                    logger.info("not valid indices")
                    continue

                # Create a new DataFrame with only the valid indices and their similarities
                valid_data = filtered_data_faiss.iloc[list(valid_indices)].copy()
                valid_data['similarity'] = valid_similarities

                # Get top N Similar questions
                top_similar_questions_faiss = valid_data.nlargest(topn, 'similarity')[['qnaId', 'date', 'question_speaker', 'question', 'answer_speaker', 'answer', 'similarity']]
                top_similar_questions_faiss_array = top_similar_questions_faiss.to_dict('records')
                top_similar_questions_faiss_array = convert_np_float32_to_float(top_similar_questions_faiss_array)

                # Create a new document to insert into MongoDB
                document = {
                    "qnaId": question['qnaId'],
                    "ticker": question['ticker'],
                    "question": question['question'],
                    "answer": question['answer'],
                    "question_speaker": question['question_speaker'],
                    "answer_speaker": question['answer_speaker'],
                    "date": question['date'],
                    "related_qna_faiss": top_similar_questions_faiss_array,
                    "event_id": question["event_id"],
                    "event_name": question["event_name"],
                    "updated_at": current_datetime
                }
                logger.info(f"Processed similar questions for: {question['qnaId']}")
                # Insert the document into the MongoDB collection
                # document = {k: (float(v) if isinstance(v, np.float32) else v) for k, v in document.items()}

                similar_questions_collection.insert_one(document)
                ctr += 1
                logger.info(ctr)


# create_similar_questions_collection(180, 0.4, 6, 20, 20, '2025-04-07', 500)
