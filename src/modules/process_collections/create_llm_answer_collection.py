import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from src.services.bedrock import BedrockManager
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.core.logging import get_logger
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def process_single_question(question_data, cutoff_date_end, cutoff_date_start, 
                           min_question_length, min_answer_length):
    """Process a single question and return the document to insert"""
    try:
        # Create service instances per thread
        openai_service = OpenAIService()
        bedrock_manager = BedrockManager()
        connection = DatabaseFactory().get_mongo_connection()
        llm_answer_collection = connection.get_collection("LLM_answers")
        similar_qns_ans_collection = connection.get_collection("similar_questions")
        prompt_collection = connection.get_collection("prompts")
        
        MIN_LLM_ANSWER_LENGTH = 100
        MAX_CONTEXT_LENGTH = 6500
        MAX_LLM_ANSWER_LENGTH = 500
        
        qnaId = question_data["_id"]
        event_id = question_data["event_id"]
        current_ticker = question_data["ticker"]
        question_speaker = question_data["question_speaker"]
        answer_speaker = question_data["answer_speaker"]
        current_question = question_data["question"]
        current_answer = question_data["answer"]
        current_date = question_data["date"]
        
        # Skip conditions
        if current_answer is None:
            return None
            
        if current_date > cutoff_date_end or current_date < cutoff_date_start:
            return None
            
        # Check if already exists
        if llm_answer_collection.find_one({"qnaId": qnaId}):
            logger.info(f"LLM answer already exists for qnaId: {qnaId}")
            return None
            
        if (current_question is None or current_answer is None or 
            answer_speaker is None or question_speaker is None):
            return None
            
        if len(current_question) < min_question_length or len(current_answer) < min_answer_length:
            return None
        
        current_answer_length = len(current_answer.split())
        max_answer_length = max(MIN_LLM_ANSWER_LENGTH, current_answer_length, MAX_LLM_ANSWER_LENGTH)
        
        related_qna_faiss_string = ""
        
        # Get related Q&A
        document = similar_qns_ans_collection.find_one({"qnaId": qnaId})
        
        if document and 'related_qna_faiss' in document:
            related_qna_faiss_array = document['related_qna_faiss']
            related_qna_faiss_df = pd.DataFrame(related_qna_faiss_array)
            
            related_qna_faiss_string = " ".join(
                related_qna_faiss_df.apply(
                    lambda row: f"Question: {row['question']}   CFO's Answer: {row['answer']}", 
                    axis=1
                ).tolist()
            )
            related_qna_faiss_string = related_qna_faiss_string.replace("\n", " ")
            
            mangement_qa_reponse_prompt = prompt_collection.find_one(
                {"prompt_name": "mangement_qa_reponse_prompt"}
            )["prompt"]
            
            estimated_prompt_length = len(mangement_qa_reponse_prompt.format(
                related_qna_faiss_string="",
                current_question=current_question,
                max_answer_length=max_answer_length,
                answer_speaker=answer_speaker
            ).split())
            
            max_related_qna_faiss_length = MAX_CONTEXT_LENGTH - estimated_prompt_length
            related_qna_faiss_string_tokens = related_qna_faiss_string.split()
            
            if len(related_qna_faiss_string_tokens) > max_related_qna_faiss_length:
                related_qna_faiss_string = " ".join(
                    related_qna_faiss_string_tokens[:max_related_qna_faiss_length]
                )
            
            prompt = mangement_qa_reponse_prompt.format(
                related_qna_faiss_string=related_qna_faiss_string,
                current_question=current_question,
                max_answer_length=max_answer_length,
                answer_speaker=answer_speaker
            )
            
            generation = openai_service.get_completion(prompt)
            generated_answer = generation
            
            if not current_answer:
                current_answer = "No answer available"
                
            if not generated_answer:
                generated_answer = "No answer available"
            
            # Calculate similarity
            embedding1 = bedrock_manager.get_embeddings(current_answer)
            embedding1_np = np.array(embedding1)
            
            embedding2 = bedrock_manager.get_embeddings(generated_answer)
            embedding2_np = np.array(embedding2)
            
            dot_product = np.dot(embedding1_np, embedding2_np)
            norm1 = np.linalg.norm(embedding1_np)
            norm2 = np.linalg.norm(embedding2_np)
            similarity_generated = dot_product / (norm1 * norm2)
            
            result_document = {
                "qnaId": qnaId,
                "event_id": event_id,
                "ticker": current_ticker,
                "question": current_question,
                "question_speaker": question_speaker,
                "answer_speaker": answer_speaker,
                "date": current_date,
                "answer": current_answer,
                "LLM_answer_llama": generated_answer,
                "LLM_answer_llama_similarity": similarity_generated,
                "updated_at": datetime.now()
            }
            
            return result_document
            
    except Exception as e:
        logger.error(f"Error processing qnaId {question_data.get('_id')}: {str(e)}")
        return None


def create_llm_answer_collection(cutoff_date, lookback_days, min_question_length, 
                                 min_answer_length, max_workers=10):
    connection = DatabaseFactory().get_mongo_connection()
    qna_collection = connection.get_collection("qnas")
    companies_collection = connection.get_collection("companies")
    llm_answer_collection = connection.get_collection("LLM_answers")
    
    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)
    
    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return
        
    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end}, 
        "ticker": {"$in": tickers}
    }
    
    with qna_collection.find(query, no_cursor_timeout=True).batch_size(200) as qna_cursor:
        questions_list = list(qna_cursor)
        questions_df = pd.DataFrame(questions_list)
    
    # Delete records marked for deletion (by event_id)
    deleted_events = set()
    for event_id in questions_df["event_id"].unique():
        if event_id not in deleted_events:
            delete_records_marked_for_deletion(event_id=event_id, coll=llm_answer_collection)
            deleted_events.add(event_id)
    
    # Convert DataFrame to list of dictionaries for parallel processing
    questions_data = questions_df.to_dict('records')
    
    ctr = 0
    
    # Process questions in parallel
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_question = {
            executor.submit(
                process_single_question, 
                question_data, 
                cutoff_date_end, 
                cutoff_date_start,
                min_question_length, 
                min_answer_length
            ): question_data for question_data in questions_data
        }
        
        # Process completed tasks
        for future in as_completed(future_to_question):
            try:
                result_document = future.result()
                
                if result_document is not None:
                    llm_answer_collection.insert_one(result_document)
                    ctr += 1
                    logger.info(f"Processed {ctr} questions")
                    
            except Exception as e:
                question_data = future_to_question[future]
                logger.error(f"Error in future for qnaId {question_data.get('_id')}: {str(e)}")
    
    logger.info(f"Total questions processed: {ctr}")