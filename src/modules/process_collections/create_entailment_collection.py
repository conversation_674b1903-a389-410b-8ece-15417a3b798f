import transformers
import pandas as pd
import torch.nn.functional as F
from datetime import datetime, timedelta
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.database.mongo.queries import delete_records_marked_for_deletion


model_name_entail = "roberta-large-mnli"
tokenizer_entail = transformers.AutoTokenizer.from_pretrained(model_name_entail)
model_entail = transformers.AutoModelForSequenceClassification.from_pretrained(model_name_entail)
logger = get_logger(__name__)


def calculate_entailment_transformers(document, snippet):  # function to calculate entailment and returns predicted label and probabilities array ("contradiction", 1: "neutral", 2: "entailment")

    # Prepare the input for the model
    inputs = tokenizer_entail.encode_plus(snippet, document, return_tensors="pt", max_length=512, truncation=True)

    # Get the model's prediction
    outputs = model_entail(**inputs)
    logits = outputs.logits

    # Apply softmax to get probability scores
    probabilities = F.softmax(logits, dim=1).squeeze().tolist()

    # Get the predicted class
    predicted_class = logits.argmax().item()

    # Map the predicted class to the entailment label
    label_map = {0: "contradiction", 1: "neutral", 2: "entailment"}
    predicted_label = label_map[predicted_class]

    return predicted_label, probabilities


def create_entailment_collection(cutoff_date, lookback_days, min_question_length, min_answer_length):
    """ Uses roberta from huggingface transformers to calclate entailment """

    connection = DatabaseFactory().get_mongo_connection()
    similar_questions_collection = connection.get_collection("similar_questions")
    companies_collection = connection.get_collection("companies")
    entailment_collection = connection.get_collection("entailments")

    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return

    query = {
        "ticker": {"$in": tickers}
    }
    with similar_questions_collection.find(query, no_cursor_timeout=True) as qna_cursor:

        # Convert cursor to list and then to DataFrame
        questions_df = pd.DataFrame(list(qna_cursor))

        # questions_df['date'] = pd.to_datetime(questions_df['date'])
        ctr = 0
        deleted_events = []
        for index, question in questions_df.iterrows():
            qnaId = question["qnaId"]
            event_id = question["event_id"]
            current_ticker = question["ticker"]
            current_question = question["question"]
            current_answer = question["answer"]
            current_date = question["date"]
            if event_id not in deleted_events:
                delete_records_marked_for_deletion(event_id=event_id, coll=entailment_collection)
                deleted_events.append(event_id)

            if len(current_question) < min_question_length:
                continue

            if len(current_answer) < min_answer_length:
                continue

            cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
            cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

            if cutoff_date_start > current_date or current_date > cutoff_date_end:
                continue

            #  if qnaId exists inside entailment collection, skip
            if entailment_collection.find_one({"qnaId": qnaId}):
                logger.info("entailment already exists")
                continue

            faiss_results = question.get("related_qna_faiss", [])
            combined_results = faiss_results
            combined_series = pd.Series(combined_results)
            document_base = " ".join(combined_series.dropna().astype(str).tolist())

            snippet = current_answer  # Use the current answer as the snippet
            entailment, probabilities = calculate_entailment_transformers(document_base, snippet)

            current_datetime = datetime.now()
            document = {
                "qnaId": qnaId,
                "ticker": current_ticker,
                "question": current_question,
                "event_id": event_id,
                "answer": current_answer,
                "date": current_date,
                "updated_at": current_datetime,
                "entailment": entailment,
                "entailment_probability_contradiction": probabilities[0],
                "entailment_probability_neutral": probabilities[1],
                "entailment_probability_entailment": probabilities[2]
            }
            entailment_collection.insert_one(document)
            ctr += 1
            logger.info(f"Entailment for qnaId {qnaId} added")
        logger.info(f"Total entailments added: {ctr}")
