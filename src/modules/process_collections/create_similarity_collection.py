import json
import threading
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def get_generate_difference_prompt(question_speaker, question, answer_speaker, answer, similar_qna_faiss):
    """
    Generate a prompt to ask OpenAI to classify a qna into Very Different/Somewhat Different/Similar, and a 100 word rationale
    """
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")

    similar_qna_faiss_text = " ".join(
        [
            f"Question: {item['question']} Answer: {item['answer']}"
            for item in similar_qna_faiss[:5]
        ]
    )
    similar_qna_faiss_text = similar_qna_faiss_text
    generate_difference_prompt = prompt_collection.find_one({"prompt_name": "generate_difference_prompt"})["prompt"]

    prompt = generate_difference_prompt.format(
        answer_speaker=answer_speaker,
        question_speaker=question_speaker,
        question=question,
        answer=answer,
        similar_qna_faiss_text=similar_qna_faiss_text,
    )
    return prompt


def process_similar_question(similar_question, cutoff_date_start, cutoff_date_end, min_question_length, min_answer_length, deleted_events_set, deleted_events_lock):
    """
    Process a single similar question
    """
    try:
        openai_service = OpenAIService()
        connection = DatabaseFactory().get_mongo_connection()
        llm_similarity_collection = connection.get_collection("LLM_similarity")

        qnaId = similar_question["qnaId"]
        event_id = similar_question["event_id"]
        date = similar_question["date"]
        question_speaker = similar_question["question_speaker"]
        question = similar_question["question"]
        answer_speaker = similar_question["answer_speaker"]
        answer = similar_question["answer"]

        with deleted_events_lock:
            if event_id not in deleted_events_set:
                delete_records_marked_for_deletion(event_id=event_id, coll=llm_similarity_collection)
                deleted_events_set.add(event_id)

        if date < cutoff_date_start or date > cutoff_date_end:
            return

        # continue if qnaId already exists in LLM_similarity collection
        if llm_similarity_collection.find_one({"qnaId": qnaId}):
            logger.info("LLM similarity already exists")
            return

        if len(question) < min_question_length:
            return

        if len(answer) < min_answer_length:
            return

        # Get Similar questions from the `similar_questions` collection for this qnaId
        similar_questions_faiss = similar_question.get("related_qna_faiss", [])
        logger.info("Fetched chunks from MDA similar to the question")

        prompt = get_generate_difference_prompt(question_speaker, question, answer_speaker, answer, similar_questions_faiss)
        # Ask OpenAI to differentiate the answer
        differentiation = openai_service.get_completion(prompt, response_format={"type": "json_object"})
        if not differentiation:
            logger.info("no data")
            return
        if differentiation == []:
            logger.info("no data")
            return

        if isinstance(differentiation, dict):
            data = differentiation
        else:
            data = json.loads(differentiation)

        classification = data["classification"]
        rationale = data["rationale"]
        logger.info(classification)
        logger.info(rationale)

        # Store the differentiation back in MongoDB
        document = {
            "qnaId": qnaId,
            "ticker": similar_question["ticker"],
            "question": question,
            "answer": answer,
            "classification": classification,
            "rationale": rationale,
            "date": similar_question["date"],
            "event_id": similar_question["event_id"],
            "event_name": similar_question["event_name"],
            "updated_at": datetime.now(),
        }
        llm_similarity_collection.insert_one(document)

    except Exception as e:
        logger.error(f"Error processing qnaId {similar_question.get('qnaId', 'unknown')}: {str(e)}")


def create_similarity_collection(cutoff_date, lookback_days, min_question_length, min_answer_length, max_workers=10):
    """
    Loop through each question in similar_questions and ask OpenAI to differentiate the answer from previous answers.
    """
    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    similar_questions_collection = connection.get_collection("similar_questions")
    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "ticker": {"$in": tickers},
    }

    deleted_events_set = set()
    deleted_events_lock = threading.Lock()

    with similar_questions_collection.find(query, no_cursor_timeout=True).batch_size(200) as similar_questions_cursor:
        similar_questions_list = list(similar_questions_cursor)

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for similar_question in similar_questions_list:
            future = executor.submit(
                process_similar_question,
                similar_question,
                cutoff_date_start,
                cutoff_date_end,
                min_question_length,
                min_answer_length,
                deleted_events_set,
                deleted_events_lock
            )
            futures.append(future)
        for future in as_completed(futures):
            try:
                future.result()
            except Exception as e:
                logger.error(f"Task failed: {str(e)}")

    logger.info("LLM_similarity collection created")
    return


if __name__ == "__main__":
    cutoff_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    create_similarity_collection(cutoff_date, 1, 10, 50, 5)
