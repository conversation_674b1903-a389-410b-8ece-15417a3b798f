import json
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.database.mongo.queries import delete_records_marked_for_deletion


logger = get_logger(__name__)


def get_generate_difference_mda_prompt(mda_chunk, mda_chunk_speaker, similar_mda_chunks):
    similar_mda_category_chunk_array = [chunk["chunk"] for chunk in similar_mda_chunks]
    similar_mda_category_chunk_text = '\n'.join(f'- {chunk}' for chunk in similar_mda_category_chunk_array)
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    generate_difference_mda_prompt = prompt_collection.find_one({"prompt_name": "generate_difference_mda_prompt"})["prompt"]
    prompt = generate_difference_mda_prompt.format(mda_chunk=mda_chunk, mda_chunk_speaker=mda_chunk_speaker, similar_mda_category_text=similar_mda_category_chunk_text)
    return prompt


def process_mda_chunk(mda_chunk_category, cutoff_date_start, cutoff_date_end, analysis_window_days, min_chunk_length):
    """
    Process a single MDA chunk category record.
    Returns a document to insert or None if processing should be skipped.
    """
    connection = DatabaseFactory().get_mongo_connection()
    llm_mda_similarity_collection = connection.get_collection("LLM_MDA_similarity")
    mda_chunk_categories_collection = connection.get_collection("mda_chunk_categories")
    openai_service = OpenAIService()
    
    qnaId = mda_chunk_category["qnaId"]
    date = mda_chunk_category["date"]
    mda_chunk = mda_chunk_category["chunk"]
    category = mda_chunk_category["category"]
    importance = mda_chunk_category["importance"]
    mda_chunk_speaker = mda_chunk_category["answer_speaker"]
    ticker = mda_chunk_category["ticker"]
    event_id = mda_chunk_category["event_id"]
    event_name = mda_chunk_category["event"]

    if date < cutoff_date_start or date > cutoff_date_end:
        return None

    # continue if the chunk id already exists in LLM_similarity collection
    if llm_mda_similarity_collection.find_one({"mda_chunk_id": mda_chunk_category["_id"]}):
        logger.info("LLM similarity already exists")
        return None

    if len(mda_chunk) < min_chunk_length:
        mda_chunk_categories_collection.update_one(
            {"_id": mda_chunk_category["_id"]},
            {"$set": {"select_for_llm_similarity_mda_processing": 0}}
        )
        return None

    # QUESTION: Do we need to remove a day from here?
    mda_chunk_analysis_end = date - timedelta(days=1)
    mda_chunk_analysis_start = cutoff_date_end - timedelta(days=analysis_window_days)
    mda_chunk_category_query = {
        "ticker": ticker,
        "category": category,
        "date": {"$gte": mda_chunk_analysis_start, "$lte": mda_chunk_analysis_end},
        "event_id": {"$ne": event_id}
    }
    similar_mda_chunks = mda_chunk_categories_collection.find(mda_chunk_category_query, no_cursor_timeout=True)
    similar_mda_chunks = list(similar_mda_chunks)
    
    if not similar_mda_chunks:
        mda_chunk_categories_collection.update_one(
            {"_id": mda_chunk_category["_id"]},
            {"$set": {"select_for_llm_similarity_mda_processing": 0}}
        )
        return None
    
    generate_difference_mda_prompt = get_generate_difference_mda_prompt(
        mda_chunk=mda_chunk, 
        mda_chunk_speaker=mda_chunk_speaker, 
        similar_mda_chunks=similar_mda_chunks
    )
    differentiation = openai_service.get_completion(
        prompt=generate_difference_mda_prompt, 
        response_format={"type": "json_object"}
    )
    differentiation = json.loads(differentiation)
    
    if not differentiation:
        return None
    
    classification = differentiation["classification"]
    rationale = differentiation["rationale"]

    document = {
        "mda_chunk_id": mda_chunk_category["_id"],
        "chunk_id": mda_chunk_category["chunk_id"],
        "qnaId": qnaId,
        "ticker": ticker,
        "answer": mda_chunk,
        "classification": classification,
        "category": category,
        "importance": importance,
        "rationale": rationale,
        "date": date,
        "event_id": event_id,
        "event": event_name,
        "select_for_llm_trend_mda_processing": 1,
        "updated_at": datetime.now(),
    }
    
    mda_chunk_categories_collection.update_one(
        {"_id": mda_chunk_category["_id"]},
        {"$set": {"select_for_llm_similarity_mda_processing": 0}}
    )
    
    return document


def create_similarity_mda_collection(cutoff_date, lookback_days, min_chunk_length, analysis_window_days, max_workers=10):
    """
    Loop through each question in similar_questions and ask OpenAI to differentiate the answer from previous answers.
    Now runs in parallel with configurable number of workers.
    """
    connection = DatabaseFactory().get_mongo_connection()
    llm_mda_similarity_collection = connection.get_collection("LLM_MDA_similarity")
    mda_chunk_categories_collection = connection.get_collection("mda_chunk_categories")
    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)
    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "select_for_llm_similarity_mda_processing": 1
    }
    deleted_events = []
    deleted_events_lock = Lock()

    # First pass: delete records marked for deletion (sequential to avoid race conditions)
    with mda_chunk_categories_collection.find(query, no_cursor_timeout=True).batch_size(500) as mda_chunk_categories_cursor:
        for mda_chunk_category in mda_chunk_categories_cursor:
            event_id = mda_chunk_category["event_id"]
            if event_id not in deleted_events:
                delete_records_marked_for_deletion(event_id=event_id, coll=llm_mda_similarity_collection)
                deleted_events.append(event_id)

    # Second pass: process records in parallel
    batch = []
    batch_lock = Lock()
    
    with mda_chunk_categories_collection.find(query, no_cursor_timeout=True).batch_size(500) as mda_chunk_categories_cursor:
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            futures = []
            for mda_chunk_category in mda_chunk_categories_cursor:
                future = executor.submit(
                    process_mda_chunk,
                    mda_chunk_category,
                    cutoff_date_start,
                    cutoff_date_end,
                    analysis_window_days,
                    min_chunk_length
                )
                futures.append(future)
            
            # Process completed tasks
            for future in as_completed(futures):
                try:
                    document = future.result()
                    if document:
                        with batch_lock:
                            batch.append(document)
                            if len(batch) >= 100:
                                llm_mda_similarity_collection.insert_many(batch)
                                logger.info(f"Inserted {len(batch)} into llm_mda_similarity_collection")
                                batch = []
                except Exception as e:
                    logger.error(f"Error processing MDA chunk: {e}")
        
        # Insert remaining batch
        if batch:
            llm_mda_similarity_collection.insert_many(batch)
            logger.info("Completed inserting all entries to llm_mda_similarity_collection")

    logger.info("llm_mda_similarity_collection created")


if __name__ == "__main__":
    cutoff_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    create_similarity_mda_collection(cutoff_date, 2, 10, 180, max_workers=10)