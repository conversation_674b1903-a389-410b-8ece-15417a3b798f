from typing import List, Dict
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.backtest.strategies.change_in_event_trend_signals import change_in_event_trend
from src.backtest.strategies.cumulative_score_signals import cumulative_score_signals
from src.backtest.strategies.latest_event_sentiment_signals import latest_event_sentiment_signals
from src.backtest.strategies.latest_non_neutral_sentiment import latest_non_neutral_sentiment_signals


def create_event_segments(events: List[Dict], start_date: datetime, end_date: datetime) -> List[Dict]:
    """
    Create segments between earnings events, capturing non-earnings events in each segment.
    Also track earnings event IDs for the start and end of each segment.
    """
    sorted_events = sorted(events, key=lambda e: e["datetime"])

    # Build earnings boundaries with event_id info
    earnings_boundaries = [{"date": start_date, "event_id": None}]  # start date has no ID
    for event in sorted_events:
        if event["event_type"] == "earnings":
            earnings_boundaries.append({"date": event["datetime"], "event_id": event.get("event_id")})
    earnings_boundaries.append({"date": end_date, "event_id": None})  # end date has no ID

    # Create segments
    segments = []
    for i in range(len(earnings_boundaries) - 1):
        seg_start_info = earnings_boundaries[i]
        seg_end_info = earnings_boundaries[i + 1]

        seg_start = seg_start_info["date"]
        seg_end = seg_end_info["date"]

        # Collect non-earnings events in this range
        non_earnings = [
            e
            for e in sorted_events
            if e["event_type"] == "non_earnings" and seg_start < e["datetime"] <= seg_end
        ]

        segments.append(
            {
                "segment_start_date": seg_start,
                "segment_end_date": seg_end,
                "start_earning_event_id": seg_start_info["event_id"],
                "end_earning_event_id": seg_end_info["event_id"],
                "non_earnings_events": non_earnings,
            }
        )

    return segments


def create_signal_for_ticker(ticker: str, end_date: datetime, lookback_days: int):
    start_date = end_date - timedelta(days=lookback_days)
    connection = DatabaseFactory().get_mongo_connection()
    signals_collection = connection.get_collection("signals")
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    query = {
        "ticker": ticker,
        "show": True,
        "$or": [
            {"event_type": "earnings"},  # earnings → no output_type restriction
            {
                "event_type": {"$ne": "earnings"},  # non-earnings → apply restriction
                "output_type": {"$nin": ["MDA", "SUMMARY"]}
            }
        ],
        "date": {"$gte": start_date, "$lte": end_date}
    }
    events_for_ticker = list(public_investor_events_outputs_collection.find(query))
    segments = create_event_segments(events_for_ticker, start_date, end_date)
    ticker_all_signals = []
    
    # Run event trend signal
    change_in_event_trend_signals = change_in_event_trend(ticker=ticker, segments=segments)
    ticker_all_signals.extend(change_in_event_trend_signals)

    latest_event_sentiment = latest_event_sentiment_signals(ticker=ticker, segments=segments)
    ticker_all_signals.extend(latest_event_sentiment)

    cumulative_signals = cumulative_score_signals(ticker=ticker, segments=segments)
    ticker_all_signals.extend(cumulative_signals)

    latest_non_neutral_signals = latest_non_neutral_sentiment_signals(ticker=ticker, segments=segments)
    ticker_all_signals.extend(latest_non_neutral_signals)

    for _signal in ticker_all_signals:
        if signals_collection.find_one({"event_id": _signal["event_id"], "strategy": _signal["strategy"]}):
            print("Signal already exists for this event")
            continue
    
        signals_collection.insert_one(_signal)
    
    print(f"Completed inserted earnings for ticker {ticker}")

if __name__ == "__main__":
    LOOKBACK_DAYS = 540
    end_date = datetime.now() + timedelta(days=1)
    create_signal_for_ticker(ticker="ADI", end_date=end_date, lookback_days=LOOKBACK_DAYS)



