from datetime import datetime
from src.database.factory import DatabaseFactory


def create_output_signals_per_ticker(ticker: str = None):
    print(f"Creating output signal for ticker: {ticker}")
    connection = DatabaseFactory().get_mongo_connection()
    output_signals_collection = connection.get_collection("output_signals")
    okay_strategy_per_ticker_collection = connection.get_collection("okay_strategy_per_ticker")
    best_strategy_per_ticker_collection = connection.get_collection("best_strategy_per_ticker")
    query = {
            "$and": [
                {"ticker": ticker},
                {"current_signal": {"$in": ["buy", "sell"]}},
                {"hit_rate": {"$gte": 0.55}},
                {"obsolete": {"$exists": False}}
            ]
        }
    # output_signals_collection.update_many({"ticker": ticker}, {"$set": {"latest": False}})
    output_signals_collection.update_many(
        {
            "ticker": ticker,
            "earnings_event_id": None
        }, 
        {
            "$set": {"obsolete": True}
        }
    )
    
    best_strategy = best_strategy_per_ticker_collection.find(query).sort("hit_rate", -1)
    for _strategy in best_strategy:
        strategy_id = _strategy["_id"]
        event_id = _strategy["event_id"]
        if output_signals_collection.find_one({"event_id": event_id, "obsolete": {"$exists": False}}):
            continue
        if output_signals_collection.find_one({"strategy_id" : strategy_id, "obsolete": {"$exists": False}}):
            continue
        # if not output_signals_collection.find_one({"strategy_id" : strategy_id}) and not output_signals_collection.find_one({"event_id": event_id}):
        _strategy["strategy_type"] = "BEST"
        _strategy["updated_at"] = datetime.now()
        _strategy["strategy_id"] = strategy_id
        _strategy["strategy"] = _strategy.pop("best_strategy")
        if _strategy["current_signal"].lower() == "buy":
            _strategy["outlook"] = "positive+"
        elif _strategy["current_signal"].lower() == "sell":
            _strategy["outlook"] = "negative-"
        output_signals_collection.insert_one(_strategy)
    
    okay_strategy = okay_strategy_per_ticker_collection.find(query).sort("hit_rate", -1)
    for _strategy in okay_strategy:
        strategy_id = _strategy["_id"]
        event_id = _strategy["event_id"]
        if output_signals_collection.find_one({"event_id": event_id, "obsolete": {"$exists": False}}):
            continue
        if output_signals_collection.find_one({"strategy_id" : strategy_id, "obsolete": {"$exists": False}}):
            continue
        # if not output_signals_collection.find_one({"strategy_id" : strategy_id}) and not output_signals_collection.find_one({"event_id": event_id}):
        _strategy["strategy_type"] = "OKAY"
        _strategy["strategy"] = _strategy.pop("okay_strategy")
        _strategy["updated_at"] = datetime.now()
        _strategy["strategy_id"] = strategy_id
        if _strategy["current_signal"].lower() == "buy":
            _strategy["outlook"] = "positive"
        elif _strategy["current_signal"].lower() == "sell":
            _strategy["outlook"] = "negative"
        output_signals_collection.insert_one(_strategy)
    
    print(f"Completed creating output signal for ticker: {ticker}")
    


if __name__ == "__main__":
    create_output_signals_per_ticker(ticker="NOW")