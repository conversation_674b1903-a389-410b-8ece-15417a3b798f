from datetime import datetime, timedelta
from src.backtest.email_collection import create_email_output
from src.backtest.create_signals import create_signal_for_ticker
from src.backtest.backtest_signals import backtest_signals
from src.backtest.best_and_okay_strategy_per_ticker import find_best_and_okay_strategy
from src.backtest.create_output_signals import create_output_signals_per_ticker


def run_pipeline_per_ticker(ticker):
    LOOKBACK_DAYS = 540
    end_date = datetime.now() + timedelta(days=1)
    print(f"Starting to create backtests for ticker: {ticker}")
    create_signal_for_ticker(ticker=ticker, end_date=end_date, lookback_days=LOOKBACK_DAYS)
    backtest_signals(ticker=ticker)
    find_best_and_okay_strategy(ticker=ticker, end_date=end_date, lookback_days=LOOKBACK_DAYS)
    create_output_signals_per_ticker(ticker=ticker)
    print("Completed backtests")

   

def run_pipeline_for_all():
    from src.database.factory import DatabaseFactory
    connection = DatabaseFactory().get_mongo_connection()
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    # Get tickers which have earnings and non earnings events
    last_7_days_date = datetime.now() - timedelta(days=7)
    pipeline = [
        {
            "$match": {
            "date": {
                "$gte": last_7_days_date
            }
            }
        },
        {
            "$group": {
            "_id": "$ticker"
            }
        },
        {
            "$project": {
            "_id": 0,
            "ticker": "$_id"
            }
        }
    ]
    ticker_with_earnings = list(public_investor_events_outputs_collection.aggregate(pipeline))
    ticker_list = [tkr["ticker"] for tkr in ticker_with_earnings]
    def process_ticker(ticker):
        print(f"Processing event for ticker: {ticker}")
        run_pipeline_per_ticker(ticker=ticker)
        print(f"Completed Processing event for ticker: {ticker}")
    

    for _ticker in ticker_list:
        process_ticker(ticker=_ticker)
    
    create_email_output()


if __name__ == "__main__":
    run_pipeline_for_all()
