import pandas as pd
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory

def get_close_before(tkr: str, day: datetime) -> float:
        connection = DatabaseFactory().get_mongo_connection()
        stock_prices_collection = connection.get_stock_collection("stock_prices")
        d = day
        for _ in range(4):  # walk back ≤3 calendar days
            start = datetime(d.year, d.month, d.day)  # start of day
            end = start + timedelta(days=1)  # start of next day
            doc = stock_prices_collection.find_one(
                {
                    "ticker": tkr,
                    "date": {"$gte": start, "$lt": end}
                },
                {"closingPrice": 1, "_id": 0}
            )
            if doc:
                return doc["closingPrice"]
            d -= timedelta(days=1)
        raise ValueError(f"Missing price for {tkr} near {day.date()}")

def get_close_after(tkr: str, day: datetime) -> float:
        connection = DatabaseFactory().get_mongo_connection()
        stock_prices_collection = connection.get_stock_collection("stock_prices")
        d = day
        for _ in range(4):  # Move ahead by >=3 days
            start = datetime(d.year, d.month, d.day)  # start of day
            end = start + timedelta(days=1)  # start of next day
            doc = stock_prices_collection.find_one(
                {
                    "ticker": tkr,
                    "date": {"$gte": start, "$lt": end}
                },
                {"closingPrice": 1, "_id": 0}
            )
            if doc:
                return doc["closingPrice"]
            d += timedelta(days=1)
        raise ValueError(f"Missing price for {tkr} near {day.date()}")


def get_rows_per_ticker(start_date, end_date, ticker, benchmark="QQQ"):
    connection = DatabaseFactory().get_mongo_connection()
    output_signals_collection = connection.get_collection("output_signals")
    signals_collection = connection.get_collection("signals")
    

    
    ticker_output_rows = []
    with output_signals_collection.find({"ticker": ticker, "obsolete": {"$exists": False}}) as outputs:
        for output in outputs:            
            event_date = output["idea_date"]
            earning_event_date = output["earnings_event_date"]
            earnings_event_id = output["earnings_event_id"]
            event_signal = output["current_signal"]
            strategy_params = None
            if output["strategy"] == "cumulative_scores":
                signal_doc = signals_collection.find_one({"event_id": output["event_id"], "strategy": "cumulative_scores"})
                strategy_params = signal_doc.get("strategy_params")
            if not earnings_event_id:
                ticker_output_rows.append(
                    {
                        "ticker": ticker,
                        "earnings_date": "",
                        "idea_date": event_date.date(),
                        "best_strategy": output["strategy"],
                        "transcript_id": output["transcript_id"],
                        "strategy_params": strategy_params,
                        "strategy_type": output["strategy_type"],
                        "hit_rate": output["hit_rate"],
                        "signal_into_earnings": event_signal,
                        "1d_return": "",
                        "1d_benchmark_return": "",
                        "1d_net_return": "",
                        "stock_eventtoearningsreturn": "",
                        f"{benchmark.lower()}_eventtoearningsreturn": "",
                        "outcome": ""
                    }
                )
                continue

            try:
                p_before = get_close_before(ticker, earning_event_date - timedelta(days=1))
                p_after = get_close_after(ticker, earning_event_date + timedelta(days=1))
                b_before = get_close_before(benchmark, earning_event_date - timedelta(days=1))
                b_after = get_close_after(benchmark, earning_event_date + timedelta(days=1))
            except ValueError:
                continue

            stock_ret = (p_after - p_before) / p_before
            bench_ret = (b_after - b_before) / b_before
            net_ret = stock_ret - bench_ret

            price_evt = get_close_after(ticker, event_date)
            price_bench_evt = get_close_after(benchmark, event_date)


            stock_evt2earn = (p_before - price_evt) / price_evt
            bench_evt2earn = (b_before - price_bench_evt) / price_bench_evt

            if event_signal == "neutral":
                outcome = "neutral"
            elif (event_signal == "buy" and net_ret > 0) or (event_signal == "sell" and net_ret < 0):
                outcome = "hit"
            else:
                outcome = "miss"
            
            ticker_output_rows.append({
                "ticker": ticker,
                "earnings_date": earning_event_date.date(),
                "idea_date": event_date.date(),
                "best_strategy": output["strategy"],
                "transcript_id": output["transcript_id"],
                "strategy_params": strategy_params,
                "strategy_type": output["strategy_type"],
                "hit_rate": output["hit_rate"],
                "signal_into_earnings": event_signal,
                "1d_return": round(stock_ret, 5),
                "1d_benchmark_return": round(bench_ret, 5),
                "1d_net_return": round(net_ret, 5),
                "stock_eventtoearningsreturn": None if stock_evt2earn is None else round(stock_evt2earn, 3),
                f"{benchmark.lower()}_eventtoearningsreturn": None if bench_evt2earn is None else round(bench_evt2earn, 3),
                "outcome": outcome
            })
        return ticker_output_rows


def generate_csv(start_date, end_date, benchmark="QQQ"):
    connection = DatabaseFactory().get_mongo_connection()
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    pipeline = [
        {
            '$group': {
                '_id': '$ticker',
                'has_earnings': {
                    '$sum': {
                        '$cond': [{'$eq': ['$event_type', 'earnings']}, 1, 0]
                    }
                },
                'has_non_earnings': {
                    '$sum': {
                        '$cond': [{'$ne': ['$event_type', 'earnings']}, 1, 0]
                    }
                }
            }
        },
        {
            '$match': {
                'has_earnings': {'$gt': 0},
                'has_non_earnings': {'$gt': 0}
            }
        },
        {
            '$project': {
                '_id': 0,
                'ticker': '$_id'
            }
        }
    ]

    def process_ticker(ticker):
        return get_rows_per_ticker(
            ticker=ticker,
            start_date=start_date,
            end_date=end_date,
            benchmark=benchmark
        )

    ticker_with_earnings = list(public_investor_events_outputs_collection.aggregate(pipeline))
    ticker_list = [tkr["ticker"] for tkr in ticker_with_earnings]
    csv_rows = []
    for ticker in ticker_list:
        try:
            rows = process_ticker(ticker=ticker)
            csv_rows.extend(rows)
        except Exception as e:
             print(f"Error processing {ticker}: {e}")   
    
    df = pd.DataFrame(csv_rows)
    df = df.sort_values(by=["ticker", "idea_date"])
    df.to_csv("backtest.csv", index=False)

    
if __name__ == "__main__":
    end_date = datetime.now() + timedelta(days=1)
    start_date = datetime.strptime("31/03/2024", "%d/%m/%Y")
    generate_csv(start_date=start_date, end_date=end_date)
