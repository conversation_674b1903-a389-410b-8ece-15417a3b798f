"""
We compute the hit rates for all the earnings event
 and for the latest event that happened after the earnings which is the current signal.
"""
import copy
from datetime import datetime, timedelta
from typing import List, Dict
from src.database.factory import DatabaseFactory

# TODO: Change this to pick only the last event not all the events
def compute_hit_rates(events_before_current) -> List[Dict]:
    """
    Aggregates 'signals_backtest' collection to compute the hit rate for
    each (ticker, strategy) pair, ignoring 'neutral' in the ratio.
    
    Args:
        ticker: If provided, runs the aggregation only for that ticker.
                Otherwise, runs for all tickers.
        start_date: If provided, only includes records from this date onwards.
        end_date: If provided, only includes records up to this date.
    
    Returns:
        A list of dictionaries in this shape:
        [
          {
            "ticker": "...",
            "strategy": "...",
            "hitCount": X,
            "missCount": Y,
            "hitRate": 0.XX,
            "latestHitTimestamp": datetime or null
          },
          ...
        ]
    
    Raises:
        ValueError: If start_date is after end_date.
    """

    connection = DatabaseFactory().get_mongo_connection()
    signals_backtest_collection = connection.get_collection("signals_backtest")
    if not events_before_current:
        return []

    # Build match stage with base conditions
    match_stage = {
        "event_id": {"$in": events_before_current},
        "hit_or_miss": {"$in": ["hit", "miss"]}, 
        "latest": True
    }

    pipeline = [
        {"$match": match_stage},
        {
            "$group": {
                "_id": {
                    "ticker": "$ticker",
                    "strategy": "$strategy"
                },
                "hitCount": {
                    "$sum": {
                        "$cond": [{"$eq": ["$hit_or_miss", "hit"]}, 1, 0]
                    }
                },
                "missCount": {
                    "$sum": {
                        "$cond": [{"$eq": ["$hit_or_miss", "miss"]}, 1, 0]
                    }
                },
                "latestHitTimestamp": {
                    "$max": {
                        "$cond": [
                            {"$eq": ["$hit_or_miss", "hit"]}, 
                            "$event_date",
                            None
                        ]
                    }
                }
            }
        },
        {
            "$project": {
                "ticker": "$_id.ticker",
                "strategy": "$_id.strategy",
                "hitCount": 1,
                "missCount": 1,
                "latestHitTimestamp": 1,
                "hitRate": {
                    "$cond": [
                        {"$eq": [{"$add": ["$hitCount", "$missCount"]}, 0]},
                        0,
                        {
                            "$divide": [
                                "$hitCount",
                                {"$add": ["$hitCount", "$missCount"]}
                            ]
                        }
                    ]
                },
                "strategySortPriority": {
                    "$cond": [
                        {"$eq": ["$_id.strategy", "latest_non_neutral_sentiment"]},
                        0,  # Highest priority
                        1   # Lower priority
                    ]
                },
                "_id": 0
            }
        },
        {
            "$sort": {
                "ticker": 1, 
                "hitRate": -1, 
                "hitCount": -1,
                "latestHitTimestamp": -1,
                "strategySortPriority": 1
            }
        }
    ]

    results = list(signals_backtest_collection.aggregate(pipeline))
    return results


def get_latest_events_with_earnings(events: List[Dict], start_date: datetime, end_date: datetime) -> List[Dict]:
    """
    This helps to get the latest earning in segment along with the next earning event for the event
    """
    sorted_events = sorted(events, key=lambda e: e["datetime"])

    # Build earnings boundaries with event_id info
    earnings_boundaries = [{"date": start_date, "event_id": None, "event": None}]  # start date has no ID
    for event in sorted_events:
        if event["event_type"] == "earnings":
            earnings_boundaries.append({"date": event["datetime"], "event_id": event.get("event_id"), "event": event})
    earnings_boundaries.append({"date": end_date, "event_id": None, "event": None})  # end date has no ID

    # Create segments
    segments = []
    for i in range(len(earnings_boundaries) - 1):
        seg_start_info = earnings_boundaries[i]
        seg_end_info = earnings_boundaries[i + 1]

        seg_start = seg_start_info["date"]
        seg_end = seg_end_info["date"]

        # Collect non-earnings events in this range
        non_earnings = [
            e
            for e in sorted_events
            if e["event_type"] == "non_earnings" and seg_start < e["datetime"] <= seg_end
        ]
        latest_non_earning_in_segment = non_earnings[-1] if len(non_earnings) else None
        next_earning_event = seg_end_info["event"]
        prev_earning_event = seg_start_info["event"]
        # If there is no non earning event then there is no point
        if not latest_non_earning_in_segment:
            continue

        segments.append(
            {
                "prev_earning_event": prev_earning_event,
                "latest_non_earning_in_segment": latest_non_earning_in_segment,
                "next_earning_event": next_earning_event
            }
        )

    return segments


def find_best_and_okay_strategy(ticker, end_date, lookback_days, threshold = 0.55):
    start_date = end_date - timedelta(days=lookback_days)
    connection = DatabaseFactory().get_mongo_connection()
    signals_collection = connection.get_collection("signals")
    public_investor_events_collection = connection.get_collection("public_investor_events")
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    best_strategy_per_ticker_collection = connection.get_collection("best_strategy_per_ticker")
    okay_strategy_per_ticker_collection = connection.get_collection("okay_strategy_per_ticker")
    query = {
        "ticker": ticker,
        "show": True,
        "$or": [
            {"event_type": "earnings"},  # earnings → no output_type restriction
            {
                "event_type": {"$ne": "earnings"},  # non-earnings → apply restriction
                "output_type": {"$nin": ["MDA", "SUMMARY"]}
            }
        ],
        "date": {"$gte": start_date, "$lte": end_date}
    }
    events_for_ticker = list(public_investor_events_outputs_collection.find(query))
    event_segments = get_latest_events_with_earnings(events_for_ticker, start_date=start_date, end_date=end_date)
    # We are running this since we got a new event so mark the previous one in this quarter as obsolete.
    # Not deleting it for record keeping
    best_strategy_per_ticker_collection.update_many(
        {
            "ticker": ticker,
            "earnings_event_id": None
        }, 
        {
            "$set": {"obsolete": True}
        }
    )
    okay_strategy_per_ticker_collection.update_many(
        {
            "ticker": ticker,
            "earnings_event_id": None
        }, 
        {
            "$set": {"obsolete": True}
        }
    )
    
    event_segments_copy = copy.deepcopy(event_segments)
    for _event in event_segments:
        """
        We compute the signals for each event before the earnings date
        """
        cutoff_end_date = _event["latest_non_earning_in_segment"]["datetime"]
        event_id = _event["latest_non_earning_in_segment"]["event_id"]
        next_earning_event = _event["next_earning_event"]
        next_earnings_event_id = next_earning_event["event_id"] if next_earning_event else None
        prev_earning_event_id = _event["prev_earning_event"].get("event_id") if _event["prev_earning_event"] else None
        prev_earning_event_date = _event["prev_earning_event"].get("date") if _event["prev_earning_event"] else None
        next_event_earnings_date = None
        if next_earnings_event_id:
            next_event_earnings_date = _event["next_earning_event"]["datetime"]
            # Ensures we dont calculate for the events which are already calculated
            best = best_strategy_per_ticker_collection.find_one({"earnings_event_id": next_earnings_event_id, "obsolete": {"$exists": False}})
            if best:
                continue
            
            okay = okay_strategy_per_ticker_collection.find_one({"earnings_event_id": next_earnings_event_id, "obsolete": {"$exists": False}})
            if okay:
                continue
        
        # This is the list of events before the current event, just the non earnings events before the earnings event
        events_before_current = []
        for _e in event_segments_copy:
            if _e["latest_non_earning_in_segment"]["event_id"] == event_id:
                break
            if _e["next_earning_event"]:
                events_before_current.append(_e["latest_non_earning_in_segment"]["event_id"])
            
        hit_rates_by_stategy = compute_hit_rates(events_before_current)
        best_per_ticker = {}
        okay_per_ticker = {}

        # First pass: find the best record for each ticker
        for record in hit_rates_by_stategy:
            ticker = record["ticker"]
            if record["hitRate"] > threshold and (ticker not in best_per_ticker or record["hitRate"] > best_per_ticker[ticker]["hitRate"]):
                best_per_ticker[ticker] = record

        # Second pass: add non-best records that meet okay threshold
        for record in hit_rates_by_stategy:
            ticker = record["ticker"]
            # Only add if it's not the best record and passes okay threshold
            if record["hitRate"] > threshold and record != best_per_ticker[ticker]:
                if ticker not in okay_per_ticker:
                    okay_per_ticker[ticker] = []
                okay_per_ticker[ticker].append(record)

        

        signals_for_event = list(signals_collection.find({"event_id": event_id}))
        signals_dict = {item["strategy"]: item for item in signals_for_event}

        for ticker, record in best_per_ticker.items():
            strategy = record["strategy"]
            try:
                current_signal = signals_dict[strategy]["signal"]
            except:
                current_signal = "neutral"
            record["current_signal"] = current_signal
            event_item = public_investor_events_collection.find_one({"_id": event_id})
            record["transcript_id"] = event_item["transcript_id"]
        
        for ticker, records in okay_per_ticker.items():
            for record in records:
                strategy = record["strategy"]
                try:
                    current_signal = signals_dict[strategy]["signal"]
                except:
                    current_signal = "neutral"
                record["current_signal"] = current_signal
                event_item = public_investor_events_collection.find_one({"_id": event_id})
                record["transcript_id"] = event_item["transcript_id"]

        for ticker, record in best_per_ticker.items():
            best_strategy_per_ticker_collection.insert_one({
                "ticker": ticker,
                "best_strategy": record["strategy"],
                "hit": record["hitCount"],
                "miss": record["missCount"],
                "hit_rate": record["hitRate"],
                "event_id": event_id,
                "prev_earning_event_id": prev_earning_event_id,
                "prev_earning_event_date": prev_earning_event_date,
                "earnings_event_date": next_event_earnings_date,
                "earnings_event_id": next_earnings_event_id,
                "transcript_id": record["transcript_id"],
                "idea_date": cutoff_end_date,   # date of the event
                "current_signal": record["current_signal"],
                "updated_at": datetime.now()
            })
            print(f"Insert best strategy for ticker: {ticker}")
        
        for ticker, records in okay_per_ticker.items():
            okay_insert_docs = []
            for record in records:
                okay_insert_docs.append({
                    "ticker": record["ticker"],
                    "okay_strategy": record["strategy"],
                    "hit": record["hitCount"],
                    "miss": record["missCount"],
                    "hit_rate": record["hitRate"],
                    "event_id": event_id,
                    "earnings_event_date": next_event_earnings_date,
                    "earnings_event_id": next_earnings_event_id,
                    "transcript_id": record["transcript_id"],
                    "prev_earning_event_id": prev_earning_event_id,
                    "prev_earning_event_date": prev_earning_event_date,
                    "idea_date": cutoff_end_date,
                    "current_signal": record["current_signal"],
                    "updated_at": datetime.now()
                })
            if okay_insert_docs:
                okay_strategy_per_ticker_collection.insert_many(okay_insert_docs)
                print(f"Insert Okay strategy for ticker: {ticker}")
            else:
                print(f"No Okay strategy for ticker: {ticker}")
        
        
        


if __name__ == "__main__":
    end_date = datetime.now() + timedelta(days=1)
    find_best_and_okay_strategy(ticker="RJF", end_date=end_date, lookback_days=540)
        