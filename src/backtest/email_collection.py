from datetime import datetime
from src.database.factory import DatabaseFactory


def create_email_output():
    connection = DatabaseFactory().get_mongo_connection()
    short_term_idea_emails_collection = connection.get_email_collection("short_term_idea_emails")

    output_signals_collection = connection.get_collection("output_signals")
    query = {"obsolete": {"$exists": False}, "earnings_event_date": None}
    with output_signals_collection.find(query, no_cursor_timeout=True) as ideas:
        for idea in ideas:
            event_id = idea.get("event_id")
            if short_term_idea_emails_collection.find_one({"event_id": event_id}):
                print(f"Idea already exists for {event_id}")
                continue
            else:
                email_item = {
                    "is_approved": 0,
                    "is_delivered": 0,
                    "is_summary_approved": 0,
                    "idea_output_id": idea["_id"],
                    "event_id": event_id,
                    "updated_at": datetime.now()
                }
                short_term_idea_emails_collection.insert_one(email_item)
                print(f"Succesfully generated email for {event_id}")
        print(f"Completed generation idea emails")



if __name__ == "__main__":
    create_email_output()
