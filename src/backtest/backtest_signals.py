from datetime import datetime
from src.database.factory import DatabaseFactory

def backtest_signals(ticker: str = None):
    """
    Another possibility is to pick it by segments
    - Pick the most latest signal in each segment
    """
    connection = DatabaseFactory().get_mongo_connection()
    signals_collection = connection.get_collection("signals")
    signals_backtest_collection = connection.get_collection("signals_backtest")
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    query = {"latest": True}
    if ticker:
        query["ticker"] = ticker
    
    with signals_collection.find(query, no_cursor_timeout=True).batch_size(5000) as signals:
        for signal in signals:
            next_earnings_event_id = signal["next_earning_event_id"]
            if not next_earnings_event_id:
                # This happens when earnings hasnt happened
                print("Cannot run backtest for this event since earning hasnt happened")
                continue
            if signals_backtest_collection.find_one({"event_id": signal["event_id"], "strategy": signal["strategy"]}):
                print("Backtest already exists for this event")
                continue
            next_earnings_event = public_investor_events_outputs_collection.find_one({"event_id": next_earnings_event_id})
            earnings_reaction = next_earnings_event.get("earnings_reaction")
            actual_sentiment = next_earnings_event["sentiment"]
            signal_into_earnings = signal["signal"]

            if actual_sentiment == "TBD":
                print("Cannot calculate hit or miss when actual earning is TBD")
                continue

            if signal_into_earnings == "neutral":
                    hit_or_miss = "neutral"
            elif signal_into_earnings == "buy" and actual_sentiment == "stockUP":
                hit_or_miss = "hit"
            elif signal_into_earnings == "sell" and actual_sentiment == "stockDOWN":
                hit_or_miss = "hit"
            else:
                hit_or_miss = "miss"
            
            signals_backtest_collection.insert_one(
                {
                    "ticker": ticker,
                    "signal_id": signal["_id"],
                    'event_id': signal['event_id'],
                    "event_date": signal["event_date"],
                    "transcript_id": signal["transcript_id"],
                    "earnings_event_id": next_earnings_event["event_id"],
                    "earnings_event_date": next_earnings_event["date"],
                    'previous_earnings_event_id': signal["previous_earnings_event_id"],
                    'previous_earnings_event_date': signal["most_recent_earnings_date"],
                    "strategy": signal["strategy"],
                    "signal_into_earnings": signal_into_earnings,
                    "hit_or_miss": hit_or_miss,
                    "returns": earnings_reaction,
                    "created_at": datetime.now(),
                    "sector": signal["sector"],
                    "latest": True
                }
            )
            print(f"Inserted back test for signal_id: {signal['_id']}")

    
if __name__ == "__main__":
    backtest_signals("T")
