from datetime import datetime, timedelta
from src.backtest.create_signals import create_signal_for_ticker
from src.backtest.backtest_signals import backtest_signals
from src.backtest.best_and_okay_strategy_per_ticker import find_best_and_okay_strategy
from src.backtest.create_output_signals import create_output_signals_per_ticker
from src.backtest.generate_csv import generate_csv


def run_pipeline_per_ticker(ticker):
    LOOKBACK_DAYS = 540
    end_date = datetime.now() + timedelta(days=1)
    print(f"Starting to create backtests for ticker: {ticker}")
    create_signal_for_ticker(ticker=ticker, end_date=end_date, lookback_days=LOOKBACK_DAYS)
    backtest_signals(ticker=ticker)
    find_best_and_okay_strategy(ticker=ticker, end_date=end_date, lookback_days=LOOKBACK_DAYS)
    create_output_signals_per_ticker(ticker=ticker)
    print("Completed backtests")

   

def main():
    from src.database.factory import DatabaseFactory
    connection = DatabaseFactory().get_mongo_connection()
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    # Get tickers which have earnings and non earnings events
    pipeline = [
        {
            '$group': {
                '_id': '$ticker',
                'has_earnings': {
                    '$sum': {
                        '$cond': [{'$eq': ['$event_type', 'earnings']}, 1, 0]
                    }
                },
                'has_non_earnings': {
                    '$sum': {
                        '$cond': [{'$ne': ['$event_type', 'earnings']}, 1, 0]
                    }
                }
            }
        },
        {
            '$match': {
                'has_earnings': {'$gt': 0},
                'has_non_earnings': {'$gt': 0}
            }
        },
        {
            '$project': {
                '_id': 0,
                'ticker': '$_id'
            }
        }
    ]
    ticker_with_earnings = list(public_investor_events_outputs_collection.aggregate(pipeline))
    ticker_list = [tkr["ticker"] for tkr in ticker_with_earnings]
    def process_ticker(ticker):
        print(f"Processing event for ticker: {ticker}")
        run_pipeline_per_ticker(ticker=ticker)
        print(f"Completed Processing event for ticker: {ticker}")
    

    for _ticker in ticker_list:
        process_ticker(ticker=_ticker)


if __name__ == "__main__":
    main()
    
    end_date = datetime.now() + timedelta(days=1)
    start_date = datetime.strptime("01/01/2024", "%d/%m/%Y")
    generate_csv(start_date=start_date, end_date=end_date)
