from typing import Dict, List
from datetime import datetime

def change_in_event_trend(ticker: str, segments: List[Dict]):
    change_in_event_trend_signals = []
    
    for segment in segments:
        previous_event = None
        segment_start_date = segment['segment_start_date']
        events = segment.get('non_earnings_events', [])
        
        for event in events:
            # Skip the first event if there's no previous event
            if previous_event is None:
                previous_event = event
                continue
            
            # Get sentiments
            prev_sentiment = previous_event['sentiment']
            curr_sentiment = event['sentiment']
            
            # Apply the rules to determine signal
            signal = None
            
            if prev_sentiment == "positive":
                if curr_sentiment in ["neutral", "negative"]:
                    signal = "sell"
                elif curr_sentiment == "positive":
                    signal = "buy"
            
            elif prev_sentiment == "negative":
                if curr_sentiment == "negative":
                    signal = "sell"
                elif curr_sentiment in ["positive", "neutral"]:
                    signal = "buy"
            
            elif prev_sentiment == "neutral":
                if curr_sentiment == "neutral":
                    signal = "neutral"
                elif curr_sentiment == "positive":
                    signal = "buy"
                elif curr_sentiment == "negative":
                    signal = "sell"
            
            if signal:
                signal_obj = {
                    'ticker': ticker,
                    'event_id': event['event_id'],
                    'event_title': event['title'],
                    'event_date': event['date'],
                    'event_datetime': event['datetime'],
                    'signal': signal,
                    'sentiment': event['sentiment'],
                    'sector': event["sector"],
                    'transcript_id': event["factset_event_id"],
                    'strategy': "change_in_event_trend",
                    'most_recent_earnings_date': segment_start_date,
                    'previous_earnings_event_id': segment["start_earning_event_id"],
                    "next_earning_event_id":  segment["end_earning_event_id"],
                    "next_earning_event_date": segment["segment_end_date"],
                    'updated_at': datetime.now(),
                    'latest': True
                }
                change_in_event_trend_signals.append(signal_obj)
            
            # Update previous event for next iteration
            previous_event = event
    
    return change_in_event_trend_signals
