from datetime import datetime
from typing import List, Dict


def cumulative_score_signals(ticker: str, segments: List[Dict]):
    cumulative_signals = []
    for segment in segments:
        segment_start_date = segment['segment_start_date']
        events = segment.get('non_earnings_events', [])
        total_event_count = 0
        total_uptick_score = 0
        total_downtick_score = 0
        total_down_th = 0
        total_net_th = 0
        for event in events:
            total_event_count += 1
            total_uptick_score += event["uptick_score"]
            total_downtick_score += event["downtick_score"]
            total_down_th += event["down_th"]
            total_net_th += event["net_th"]

            total_uptick_mean = total_uptick_score/total_event_count
            total_downtick_mean = total_downtick_score/total_event_count
            total_down_th_mean = total_down_th/total_event_count
            total_net_th_mean = total_net_th/total_event_count

            if total_downtick_mean < total_down_th_mean:
                signal = "sell"
            elif (total_uptick_mean + total_downtick_mean) > total_net_th_mean:
                signal = "buy"
            else:
                signal = "neutral"
            
            cumulative_signals.append({
                'ticker': ticker,
                'event_id': event["event_id"],
                'event_title': event["title"],
                'event_date': event["date"],
                'event_datetime': event["datetime"],
                'signal': signal,
                'sentiment': event["sentiment"],
                'sector': event["sector"],
                'strategy': "cumulative_scores",
                'transcript_id': event["factset_event_id"],
                'most_recent_earnings_date': segment_start_date,
                'previous_earnings_event_id': segment["start_earning_event_id"],
                "next_earning_event_id":  segment["end_earning_event_id"],
                "next_earning_event_date": segment["segment_end_date"],
                "strategy_params": {
                    "total_event_count": total_event_count,
                    "total_event_count":total_event_count,
                    "total_uptick_score": total_uptick_score,
                    "total_downtick_score": total_downtick_score,
                    "total_down_th": total_down_th,
                    "total_net_th": total_net_th,
                    "total_uptick_mean": total_uptick_mean,
                    "total_downtick_mean": total_downtick_mean,
                    "total_down_th_mean": total_down_th_mean,
                    "total_net_th_mean": total_net_th_mean,
                    "total_net": total_uptick_score + total_downtick_score,
                    "total_net_mean": (total_uptick_score + total_downtick_score)/total_event_count,
                    "uptick_score": event["uptick_score"],
                    "downtick_score": event["downtick_score"],
                    "down_th": event["down_th"],
                    "net_th": event["net_th"]

                },
                'updated_at': datetime.now(),
                'latest': True
            })
    
    return cumulative_signals




