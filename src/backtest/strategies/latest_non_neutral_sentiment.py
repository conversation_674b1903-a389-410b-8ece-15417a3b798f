from datetime import datetime
from typing import List, Dict

def latest_non_neutral_sentiment_signals(ticker: str, segments: List[Dict]):
    latest_non_neutral_signals = []
    for segment in segments:
        segment_start_date = segment['segment_start_date']
        events = segment.get('non_earnings_events', [])
        previous_sentiment = "neutral"
        for event in events:
            current_sentiment = event["sentiment"]
            if current_sentiment == "neutral":
                sentiment = previous_sentiment
            else:
                sentiment = current_sentiment
                previous_sentiment = current_sentiment

            if sentiment.lower() == "negative":
                signal = "sell"
            elif sentiment.lower() == "positive":
                signal = "buy"
            else:
                signal = "neutral"

            latest_non_neutral_signals.append({
                'ticker': ticker,
                'event_id': event['event_id'],
                "event_title": event['title'],
                'event_date': event['date'],
                'event_datetime': event['datetime'],
                'signal': signal,
                'transcript_id': event["factset_event_id"],
                'sentiment': sentiment,
                'event_sentiment': event['sentiment'],
                'sector': event["sector"],
                'strategy': "latest_non_neutral_sentiment",
                'most_recent_earnings_date': segment_start_date,
                'previous_earnings_event_id': segment["start_earning_event_id"],
                "next_earning_event_id":  segment["end_earning_event_id"],
                "next_earning_event_date": segment["segment_end_date"],
                'updated_at': datetime.now(),
                'latest': True
            })
    return latest_non_neutral_signals