from datetime import datetime
from typing import List, Dict

def latest_event_sentiment_signals(ticker: str, segments: List[Dict]):
    latest_signals = []
    for segment in segments:
        segment_start_date = segment['segment_start_date']
        events = segment.get('non_earnings_events', [])
        for event in events:
            if event["sentiment"].lower() == "negative":
                signal = "sell"
            elif event["sentiment"].lower() == "positive":
                signal = "buy"
            else:
                signal = "neutral"
            
            latest_signals.append({
                'ticker': ticker,
                'event_id': event['event_id'],
                "event_title": event['title'],
                'event_date': event['date'],
                'event_datetime': event['datetime'],
                'signal': signal,
                'transcript_id': event["factset_event_id"],
                'sentiment': event['sentiment'],
                'sector': event["sector"],
                'strategy': "latest_event_sentiment",
                'most_recent_earnings_date': segment_start_date,
                'previous_earnings_event_id': segment["start_earning_event_id"],
                "next_earning_event_id":  segment["end_earning_event_id"],
                "next_earning_event_date": segment["segment_end_date"],
                'updated_at': datetime.now(),
                'latest': True
            })
    return latest_signals