from datetime import datetime

from openai._types import NOT_GIVEN
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.helpers.parse_json_from_markdown import parse_json_from_markdown


logger = get_logger(__name__)


def get_bullish_thesis_validation_prompt(ticker, bull_view, review_texts):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collection = connection.get_collection("prompts")
    prompt = prompts_collection.find_one({
        "prompt_name": "bullish_thesis_validation_prompt",
        "version": 1
    })
    bullish_thesis_validation_prompt = prompt["prompt"].format(ticker=ticker, bull_view=bull_view, review_texts=review_texts)
    return bullish_thesis_validation_prompt


def get_bearish_thesis_validation_prompt(ticker, bear_view, review_texts):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collection = connection.get_collection("prompts")
    prompt = prompts_collection.find_one({
        "prompt_name": "bearish_thesis_validation_prompt",
        "version": 1
    })
    bearish_thesis_validation_prompt = prompt["prompt"].format(ticker=ticker, bear_view=bear_view, review_texts=review_texts)
    return bearish_thesis_validation_prompt


def check_thesis_with_data():
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    company_thesis_collection = connection.get_collection("company_thesis")
    sales_review_chunks_collection = connection.get_sales_collection("review_chunks")
    employee_review_chunks_collection = connection.get_employee_insight_collection("review_chunks")
    product_review_chunks_collection = connection.get_sales_collection("review_chunks")

    tickers = company_thesis_collection.distinct("ticker", {"check_thesis_with_data": False})
    for ticker in tickers:
        # extract bear_view, bull_view, final_view, bull_or_bear, probability from company_thesis collection
        thesis = company_thesis_collection.find_one({"ticker": ticker})

        if not thesis:
            logger.info(f"No thesis found for {ticker}")
            continue

        bull_view = thesis.get("bull_view", "")
        bear_view = thesis.get("bear_view", "")
        bull_or_bear = thesis.get("bull_or_bear", "")

        # extract all review_text from review_chunks collection for ticker
        review_chunks = employee_review_chunks_collection.find({"ticker": ticker})
        review_texts = [{"_id": chunk["review_id_modified"], "review_text": chunk["review_text"], "review_type": "employee"} for chunk in review_chunks]
        if not review_texts:
            logger.info(f"No employee reviews texts found for {ticker}")

        # do same for sales_prod2
        repvue_chunks = sales_review_chunks_collection.find({"ticker": ticker})
        repvue_texts = [{"_id": chunk["review_id"], "review_text": chunk["review_text"], "review_type": "sales"} for chunk in repvue_chunks]
        if not repvue_texts:
            logger.info(f"No repvue texts found for {ticker}")

        # combine review_texts and repvue_texts
        review_texts.extend(repvue_texts)

        # do same for trustpilot_prod2
        trustpilot_chunks = product_review_chunks_collection.find({"ticker": ticker})
        trustpilot_texts = [{"_id": chunk["review_id"], "review_text": chunk["review_text"], "review_type": "trustpilot"} for chunk in trustpilot_chunks]
        review_texts.extend(trustpilot_texts)

        if not review_texts:
            company_thesis_collection.update_one(
                    {"ticker": ticker},
                    {
                        "$set": {
                            "check_thesis_with_data": True,
                    }
                }
            )
            logger.info(f"No review texts found for {ticker}")
            continue
        review_texts_str = ""
        for _review in review_texts:
            if _review["review_type"] == "sales":
                review_texts_str += f"sales_id: {str(_review['_id'])}\n{_review['review_text']}\n\n"
            elif _review["review_type"] == "employee":
                review_texts_str += f"emp_id: {str(_review['_id'])}\n{_review['review_text']}\n\n"
            elif _review["review_type"] == "trustpilot":
                review_texts_str += f"trustpilot_id: {str(_review['_id'])}\n{_review['review_text']}\n\n"

        if bull_or_bear == "Ahead":
            # check if bull_view is supported by review_texts
            bullish_thesis_validation_prompt = get_bullish_thesis_validation_prompt(ticker=ticker, bull_view=bull_view, review_texts=review_texts_str)
            response = openai_service.get_completion(prompt=bullish_thesis_validation_prompt, response_format={"type": "json_object"}, max_token=NOT_GIVEN)
            logger.info(f"Response for {ticker} bull view: {response}")
        elif bull_or_bear == "Behind":
            # check if bear_view is supported by review_texts
            bearish_thesis_validation_prompt = get_bearish_thesis_validation_prompt(ticker=ticker, bear_view=bear_view, review_texts=review_texts)
            response = openai_service.get_completion(prompt=bearish_thesis_validation_prompt, response_format={"type": "json_object"}, max_token=NOT_GIVEN)
            logger.info(f"Response for {ticker} bear view: {response}")
        
        response_parsed = parse_json_from_markdown(response)
        if not response_parsed:
            company_thesis_collection.update_one(
                    {"ticker": ticker},
                    {
                        "$set": {
                            "check_thesis_with_data": True,
                    }
                }
            )
            logger.warning("Cannot read parsed responses")
            continue

        if isinstance(response_parsed, dict):
            # Wrap single dictionary in a list
            response_parsed = [response_parsed]
        for response in response_parsed:
            if isinstance(response, dict):
                company_thesis_collection.update_one(
                    {"ticker": ticker},
                    {
                        "$set": {
                            "check_thesis_with_data": True,
                            "validation": response.get("validation", ""),
                            "rationale": response.get("rationale", ""),
                            "updated_at": datetime.now()
                        }
                    },
                    upsert=True
                )
                logger.info(f"Updated thesis for {ticker} with validation and rationale.")


if __name__ == "__main__":
    check_thesis_with_data()
