import re
import json
import pandas as pd
from datetime import datetime
from google.genai import types

from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.vertex_service import GeminiService
from src.helpers.parse_json_from_markdown import parse_json_from_markdown


logger = get_logger(__name__)


def generate_earnings_baseline(prev_earning_event_id):
    connection = DatabaseFactory().get_mongo_connection()
    outputs_collection = connection.get_collection("outputs")
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    event = public_investor_events_outputs_collection.find_one({"event_id": prev_earning_event_id, "summary": {"$exists": True}})
    if not event:
        return None
    summary = " "
    summary = "\n".join([summary, f"Date: {event['date']}\nTitle: {event['title']}\n{event['summary']}\n"])
    qns_with_flags = event.get("qns_with_flags", [])
    qna_texts = []

    for qna_id in qns_with_flags:
        qna = outputs_collection.find_one({"qnaId": qna_id})
        if qna:
            qna_texts.append({
                "Question": qna.get("question", ""),
                "Answer": qna.get("answer", "")
            })

    for qna in qna_texts:
        summary = "\n".join([summary, f"Q: {qna['Question']}\nA: {qna['Answer']}\n\n"])

    return summary


def find_earnings_dates(ticker):
    connection = DatabaseFactory().get_mongo_connection()
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    df = pd.DataFrame(list(public_investor_events_outputs_collection.find({"ticker": ticker})))

    earnings_dates = []
    for _, row in df.iterrows():
        if "Earnings Call" in row['title']:
            earnings_dates.append({
                'event_date': row['date'],
                'event_datetime': row['datetime'],
                'title': row['title']
            })
    earnings_dates = sorted(earnings_dates, key=lambda x: x['event_date'])
    return earnings_dates


def event_content(ticker, prev_earning_event_date):
    connection = DatabaseFactory().get_mongo_connection()
    outputs_collection = connection.get_collection("outputs")
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")

    events = list(public_investor_events_outputs_collection.find({"ticker": ticker, "event_type": "non_earnings", "date": {"$gte": prev_earning_event_date}}))

    summary = ""
    qns_with_flags = []

    for event in events:
        event_summary = re.sub(r'<sup>.*?</sup>', '', event['summary'])
        event_summary = event_summary.replace('<mark>', '').replace('</mark>', '')
        summary = "\n".join([summary, f"Date: {event['date']}\nTitle: {event['title']}\n{event_summary}\n"])
        qns_with_flags.extend(event.get("qns_with_flags", []))

    qna_texts = []

    for qna_id in qns_with_flags:
        qna = outputs_collection.find_one({"qnaId": qna_id})
        if qna:
            llm_trend = qna.get("LLM_trend_final")
            if llm_trend == 1:
                trend = "Uptick"
            elif llm_trend == -1:
                trend = "Downtick"
            elif llm_trend == 0:
                trend = "Neutral"
            else:
                trend = ""
            qna_texts.append({
                "qnaId": qna.get("qnaId", ""),
                "Question": qna.get("question", ""),
                "Answer": qna.get("answer", ""),
                "trend": trend,
                "insight": qna.get("LLM_insight", ""),
            })

    for qna in qna_texts:
        summary = "\n".join([
            summary,
            f"qnaId: {qna['qnaId']}\nQuestion: {qna['Question']}\nAnswer: {qna['Answer']}\nTrend: {qna['trend']}\nInsight: {qna['insight']}\n\n"
        ])

    return summary


def get_report_generation_prompt(ticker, view, events_summary):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collection = connection.get_collection("prompts")
    prompt_entry = prompts_collection.find_one({"version": 1, "prompt_name": "company_thesis_generation_prompt"})
    analysis_prompt_text = prompt_entry["prompt"].format(ticker=ticker, view=view, events_summary=events_summary)
    system_prompt_text = prompt_entry["system_prompt"]
    # analysis_prompt_text = types.Part.from_text(text=analysis_prompt_text)

    # contents = [
    #     types.Content(
    #         role="user",
    #         parts=[
    #             analysis_prompt_text
    #         ]
    #     )
    # ]
    # tools = [
    #     types.Tool(google_search=types.GoogleSearch())
    # ]
    generate_content_config = types.GenerateContentConfig(
        response_mime_type='application/json',
        temperature=0,
        top_p=0.95,
        max_output_tokens=8192,
        response_modalities=["TEXT"],
        # tools=tools,
        system_instruction=system_prompt_text,
    )
    return analysis_prompt_text, generate_content_config


def generate_report(ticker, prev_earning_event_id, prev_earning_event_date, view):
    gemini_service = GeminiService()
    events_summary = event_content(ticker, prev_earning_event_date)
    if not events_summary:
        logger.info("No events found")
        return
    earnings_summary = generate_earnings_baseline(prev_earning_event_id)
    if not earnings_summary:
        logger.info("No earnings found")
        return
    prompt, config = get_report_generation_prompt(ticker=ticker, view=view, events_summary=events_summary)
    output_text = gemini_service.get_completion(model="gemini-2.5-pro", prompt=prompt, config=config)
    return output_text


def create_thesis_collection():
    connection = DatabaseFactory().get_mongo_connection()
    company_thesis_collection = connection.get_collection("company_thesis")
    output_signals_collection = connection.get_collection("output_signals")
    output_signals_list = list(output_signals_collection.find(
        {
            "earnings_event_id": None,
            "obsolete": {"$exists": False},
            "$or": [
                {"is_thesis_generated": {"$exists": False}},
                {"is_thesis_generated": False}
            ]
        },
        {
            "_id": 1,
            "ticker": 1,
            "current_signal": 1,
            "transcript_id": 1,
            "event_id": 1,
            "prev_earning_event_date": 1,
            "prev_earning_event_id": 1
        }
    ))

    for _output in output_signals_list:
        # Remove the following condition
        ticker = _output["ticker"]
        event_id = _output["event_id"]
        transcript_id = _output["transcript_id"]
        if company_thesis_collection.find_one({"event_id": event_id, "transcript_id": transcript_id}):
            print("Skipping event as it already exits.")
            output_signals_collection.update_one(
                {"_id": _output["_id"]},
                {
                    "$set": {
                        "is_thesis_generated": True
                    }
                }
            )
            continue
        current_view = _output["current_signal"]
        if current_view == "buy":
            current_view = "Ahead"
        elif current_view == "sell":
            current_view = "Behind"
        else:
            continue

        logger.info(f"Generating report for {ticker}")
        report = generate_report(ticker=ticker, 
                                 prev_earning_event_id= _output.get("prev_earning_event_id"),
                                 prev_earning_event_date=_output.get("prev_earning_event_date"),
                                 view=current_view)
        if not report:
            logger.info(f"No output generated for {ticker}")
            continue
        try:
            output_json = parse_json_from_markdown(report)
            bull_view = output_json.get("bull_view", "")
            bear_view = output_json.get("bear_view", "")
            final_view = output_json.get("final_view", "")
            bull_or_bear_llm = output_json.get("bull_or_bear", "")
            current_view_llm = output_json.get("bull_or_bear", "")
            probability = output_json.get("probability", "")
        except json.JSONDecodeError as e:
            logger.info(f"Error decoding JSON for {ticker}: {e}")
            continue

        company_thesis_collection.update_one(
            {"ticker": ticker},
            {
                "$set": {
                    "event_id": event_id,
                    "transcript_id": transcript_id,
                    "is_thesis_generated": False,
                    "ask_follow_up_questions": False,
                    "check_thesis_with_data": False,
                    "check_thesis_with_podcasts": False,
                    "ticker": ticker,
                    "bull_view": str(bull_view),
                    "bear_view": str(bear_view),
                    "final_view": str(final_view),
                    "bull_or_bear": str(current_view),
                    "bull_or_bear_llm": str(bull_or_bear_llm),
                    "current_view_llm": str(current_view_llm),
                    "current_view": str(current_view),
                    "probability": str(probability),
                    "updated_at": datetime.now()
                }
            },
            upsert=True
        )

        output_signals_collection.update_one(
            {"_id": _output["_id"]},
            {
                "$set": {
                    "is_thesis_generated": True
                }
            }
        )
        
if __name__ == "__main__":
    create_thesis_collection()
