from datetime import datetime, timedelta

from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.helpers.parse_json_from_markdown import parse_json_from_markdown


logger = get_logger(__name__)


def get_bullish_thesis_validation_podcast_prompt(ticker, bull_view, podcast_texts):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collection = connection.get_collection("prompts")
    prompt = prompts_collection.find_one({
        "prompt_name": "bullish_thesis_validation_podcast_prompt",
        "version": 1
    })
    bullish_thesis_validation_podcast_prompt = prompt["prompt"].format(ticker=ticker, bull_view=bull_view, podcast_texts=podcast_texts)
    return bullish_thesis_validation_podcast_prompt


def get_bearish_thesis_validation_podcast_prompt(ticker, bear_view, podcast_texts):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collection = connection.get_collection("prompts")
    prompt = prompts_collection.find_one({
        "prompt_name": "bearish_thesis_validation_podcast_prompt",
        "version": 1
    })
    bearish_thesis_validation_podcast_prompt = prompt["prompt"].format(ticker=ticker, bear_view=bear_view, podcast_texts=podcast_texts)
    return bearish_thesis_validation_podcast_prompt


def check_thesis_with_podcasts():
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    company_thesis_collection = connection.get_collection("company_thesis")
    podcast_events_final_collection = connection.get_podcast_collection("podcast_events_final")

    tickers = company_thesis_collection.distinct("ticker", {"check_thesis_with_podcasts": False})
    for ticker in tickers:
        # extract bear_view, bull_view, final_view, bull_or_bear, probability from company_thesis collection
        thesis = company_thesis_collection.find_one({"ticker": ticker})

        if not thesis:
            logger.info(f"No thesis found for {ticker}")
            continue
        bull_view = thesis.get("bull_view", "")
        bear_view = thesis.get("bear_view", "")
        bull_or_bear = thesis.get("bull_or_bear", "")

        # extract all podcast_texts from podcast_chunks collection for ticker
        podcast_chunks = podcast_events_final_collection.find({
            "ticker": ticker,
            "date": {"$gte": datetime.now() - timedelta(days=180)},
            "status": "COMPLETED"
        })
        podcast_texts = [{"podcast_id": chunk["_id"], "transcript": chunk["transcript"]} for chunk in podcast_chunks]
        if not podcast_texts:
            company_thesis_collection.update_one(
                    {"ticker": ticker},
                    {
                        "$set": {
                            "check_thesis_with_podcasts": True,
                        }
                    }
                )
            logger.info(f"No podcast texts found for {ticker}")
            continue

        # combine podcast_texts into one string
        podcast_texts = " ".join([f"{text['podcast_id']}: {text['transcript']}" for text in podcast_texts if "transcript" in text])
        logger.info(f"Podcast texts for {ticker}: {podcast_texts[:1000]}...")  # logger.info first 1000 characters for brevity
        # restrict podcast_texts to 200000 characters
        if len(podcast_texts) > 200000:
            podcast_texts = podcast_texts[:200000]
            logger.info(f"Podcast texts for {ticker} truncated to 200000 characters.")
        if not podcast_texts:
            company_thesis_collection.update_one(
                    {"ticker": ticker},
                    {
                        "$set": {
                            "check_thesis_with_podcasts": True,
                        }
                    }
                )
            logger.info(f"No podcast texts found for {ticker}")
            continue

        if bull_or_bear == "Ahead":
            # check if bull_view is supported by podcast_texts by calling ask_openai_to_doeverything
            bullish_thesis_validation_podcast_prompt = get_bullish_thesis_validation_podcast_prompt(ticker=ticker, bull_view=bull_view, podcast_texts=podcast_texts)
            response = openai_service.get_completion_without_limits(prompt=bullish_thesis_validation_podcast_prompt, response_format={"type": "json_object"})
        else:
            if bull_or_bear == "Behind":
                bearish_thesis_validation_podcast_prompt = get_bearish_thesis_validation_podcast_prompt(ticker=ticker, bear_view=bear_view, podcast_texts=podcast_texts)
                response = openai_service.get_completion_without_limits(prompt=bearish_thesis_validation_podcast_prompt, response_format={"type": "json_object"})
                # check if bear_view is supported by podcast_texts by calling ask_openai_to_doeverything
        
        response_parsed = parse_json_from_markdown(response)
        logger.info(f"Response for {ticker} bear view: {response}")
        if not response_parsed:
            company_thesis_collection.update_one(
                    {"ticker": ticker},
                    {
                        "$set": {
                            "check_thesis_with_podcasts": True,
                        }
                    }
                )
            continue

        if isinstance(response_parsed, dict):
            # Wrap single dictionary in a list
            response_parsed = [response_parsed]

        for response in response_parsed:
            if isinstance(response, dict):
                company_thesis_collection.update_one(
                    {"ticker": ticker},
                    {
                        "$set": {
                            "check_thesis_with_podcasts": True,
                            "podcast_validation": response.get("validation", ""),
                            "podcast_rationale": response.get("rationale", ""),
                            "updated_at": datetime.now()
                        }
                    },
                    upsert=True
                )
                logger.info(f"Updated thesis for {ticker} with podcast validation and rationale.")


if __name__ == "__main__":
    check_thesis_with_podcasts()
