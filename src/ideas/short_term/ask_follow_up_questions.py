from datetime import datetime

from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.helpers.parse_json_from_markdown import parse_json_from_markdown


logger = get_logger(__name__)


def get_bullish_management_interview_question_generator_prompt(ticker, bull_view):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collection = connection.get_collection("prompts")
    prompt = prompts_collection.find_one({
        "prompt_name": "bullish_management_interview_question_generator_prompt",
        "version": 1
    })
    bullish_management_interview_question_generator_prompt = prompt["prompt"].format(ticker=ticker, bull_view=bull_view)
    return bullish_management_interview_question_generator_prompt


def get_bearish_management_interview_question_generator_prompt(ticker, bear_view):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collection = connection.get_collection("prompts")
    prompt = prompts_collection.find_one({
        "prompt_name": "bearish_management_interview_question_generator_prompt",
        "version": 1
    })
    bearish_management_interview_question_generator_prompt = prompt["prompt"].format(ticker=ticker, bear_view=bear_view)
    return bearish_management_interview_question_generator_prompt


def ask_follow_up_questions_thesisdriven():
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    company_thesis_collection = connection.get_collection("company_thesis")
    tickers = company_thesis_collection.distinct("ticker", {"ask_follow_up_questions": False})

    for ticker in tickers:
        # extract bear_view, bull_view, final_view, bull_or_bear, probability from company_thesis collection
        thesis = company_thesis_collection.find_one({"ticker": ticker})
        if not thesis:
            logger.info(f"No thesis found for {ticker}")
            continue
        bull_view = thesis.get("bull_view", "")
        bear_view = thesis.get("bear_view", "")
        bull_or_bear = thesis.get("bull_or_bear", "")

        if bull_or_bear == "Ahead":
            # check if bull_view is supported by review_texts by calling ask_openai_to_doeverything
            bullish_management_interview_question_prompt = get_bullish_management_interview_question_generator_prompt(ticker=ticker, bull_view=bull_view)
            response = openai_service.get_completion_without_limits(prompt=bullish_management_interview_question_prompt, response_format={"type": "json_object"})
        elif bull_or_bear == "Behind":
            bearish_management_interview_question_prompt = get_bearish_management_interview_question_generator_prompt(ticker=ticker, bear_view=bear_view)
            response = openai_service.get_completion_without_limits(prompt=bearish_management_interview_question_prompt, response_format={"type": "json_object"})
        
        response_parsed = parse_json_from_markdown(response)
        if not response_parsed:
            company_thesis_collection.update_one(
                    {"ticker": ticker},
                    {
                        "$set": {
                            "ask_follow_up_questions": True,
                        }
                    }
            )
            continue
        if isinstance(response_parsed, dict):
            # Wrap single dictionary in a list
            response_parsed = [response_parsed]
        for response in response_parsed:
            if isinstance(response, dict):
                company_thesis_collection.update_one(
                    {"ticker": ticker},
                    {
                        "$set": {
                            "ask_follow_up_questions": True,
                            "question1": response.get("question1", ""),
                            "rationale1": response.get("rationale1", ""),
                            "question2": response.get("question2", ""),
                            "rationale2": response.get("rationale2", ""),
                            "question3": response.get("question3", ""),
                            "rationale3": response.get("rationale3", ""),
                            "updated_at": datetime.now()
                        }
                    }
                )
            logger.info(f"Updated thesis for {ticker} with questions.")


if __name__ == "__main__":
    ask_follow_up_questions_thesisdriven()
