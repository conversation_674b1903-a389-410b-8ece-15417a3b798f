from src.ideas.short_term.check_thesis_with_data import check_thesis_with_data
from src.ideas.short_term.create_thesis_collection import create_thesis_collection
from src.ideas.short_term.check_thesis_with_podcasts import check_thesis_with_podcasts
from src.ideas.short_term.ask_follow_up_questions import ask_follow_up_questions_thesisdriven


def run_short_term_ideas():
    create_thesis_collection()
    check_thesis_with_data()
    check_thesis_with_podcasts()
    ask_follow_up_questions_thesisdriven()


if __name__ == "__main__":
    run_short_term_ideas()
