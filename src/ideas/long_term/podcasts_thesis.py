from datetime import datetime, UTC
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.helpers.parse_json_from_markdown import parse_json_from_markdown


def update_product_thesis_with_podcasts():
    # for each ticker in product_thesis, find the podcasts in db["podcasts"] with sameticker.
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    product_thesis_collection = connection.get_collection("product_thesis")
    podcast_events_final_collection = connection.get_podcast_collection("podcast_events_final")

    today = datetime.now(UTC).replace(hour=0, minute=0, second=0, microsecond=0)
    for product in product_thesis_collection.find({"updated_at": {"$gt": today}}, no_cursor_timeout=True):
        ticker = product["ticker"]
        print(ticker)
        podcasts_for_ticker = list(podcast_events_final_collection.find({"ticker": ticker, "status": "COMPLETED", "transcript": {"$exists": True}}))
        # If no podcasts, skip to the next ticker
        if len(podcasts_for_ticker) == 0:
            continue    # extract episode_title, transcript_processed from podcasts_for_ticker
        episode_titles = []
        transcripts = []
        summary = product["summary"]

        for podcast in podcasts_for_ticker:
            episode_titles.append(podcast["episode_title"])
            transcripts_str = "podcast_id: " + str(podcast["_id"]) + " | Title: " + str(podcast["episode_title"])
            transcripts.append(podcast["transcript"])
            transcripts_str += " | Text: " + str(podcast["transcript"])

        prompt = f"""You are a financial analyst. The management team of {ticker} has been talking about their products in their investor meetings in the following way

<Management summary start>
{summary}
<Management summary end>

For ONLY the products mentioned in this summary above, please check for references in the following podcast transcripts .
The output should be in json format with the following fields:
- products: which products are mentioned BOTH in management summary and podcast. Example: "product1", "product2"
- synthesis: based on the comments in the podcasts, summarize in 100 words or less how the management team has been talking about the products in the podcasts. DONOT start with "in the podcast", "the podcast says" etc, talk specifically about the product trends. Example: " Product 1 is seeing strong traction within enteprise. <sup>[ podcast_id: 12345]</sup> Product 2 has just started seeing adoption. <sup>[ podcast_id: 12345]</sup>"
- validated: yes/no - whether the products mentioned in the management summary are validated by the podcasts. If there are no references, please say "NULL". Example: "yes" or "no" or "NULL"
The transcripts are
<podcast transcripts start>
{transcripts_str}
<podcast transcripts end>
        """
        # compress the prompt to 128000 tokens
        prompt = prompt[:500000]
        results = openai_service.get_completion(prompt)
        insights_list = parse_json_from_markdown(results)
        if isinstance(insights_list, dict):
            # Wrap single dictionary in a list
            insights_list = [insights_list]

        if not insights_list:
            print("No insights generated. Skipping.")
            continue
        print(f"Insights: {insights_list}")
        # Iterate through the insights list and write each entry into MongoDB
        for insight in insights_list:
            products = insight.get('products', [])
            validation = insight.get('synthesis', {})
            validated = insight.get('validated', "NULL")

            # Insert the insights into the product_thesis collection
            product_thesis_collection.update_many(
                {"ticker": ticker},
                {
                    "$set": {
                        "podcast_insights": {
                            "products": products,
                            "validation": validation,
                            "validated": validated
                        },
                        "updated_at": datetime.now()
                    }
                },
                upsert=True
            )


if __name__ == "__main__":
    update_product_thesis_with_podcasts()
