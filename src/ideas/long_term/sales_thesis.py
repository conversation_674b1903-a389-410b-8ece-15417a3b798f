from datetime import datetime, UTC
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.helpers.parse_json_from_markdown import parse_json_from_markdown


def update_product_thesis_with_salesreviews():
    # for each ticker in product_thesis, find the podcasts in db["podcasts"] with sameticker.
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    product_thesis_collection = connection.get_collection("product_thesis")
    reviews_collection = connection.get_blackberry_sales_collection("reviews")
    ticker_identifier_collection = connection.get_blackberry_sales_collection("ticker_identifier")

    today = datetime.now(UTC).replace(hour=0, minute=0, second=0, microsecond=0)
    for product in product_thesis_collection.find({"updated_at": {"$gt": today}}, no_cursor_timeout=True):
        ticker = product["ticker"]
        summary = product["summary"]

        # if sales_insights exists, skip
        if product_thesis_collection.find_one({"ticker": ticker, "sales_insights": {"$exists": True}}):
            print(f"{ticker} already exists in product_thesis")
            continue
        # for ticker find slug from ticker_identifier
        ticker_identifier = ticker_identifier_collection.find_one({"ticker": ticker})
        if not ticker_identifier:
            print(f"Ticker identifier not found for {ticker}")
            continue
        slug = ticker_identifier["slug"]
        print(f"Slug: {slug}")

        print(ticker)

        # Debugging: Check if the ticker exists in the articles collection
        if reviews_collection.count_documents({"slug": slug}) == 0:
            print(f"No articles found for ticker: {ticker}")
            continue
        else:
            reviews_articles = reviews_collection.find({"slug": slug})

        # extract description from reviews_articles and content from reviews_comments into strings
        reviews_articles_str = ""
        for reviews_article in reviews_articles:
            reviews_articles_str += "sales_id" + str(reviews_article["_id"]) + " "

            reviews_articles_str += str(reviews_article["text"]) + " "

        prompt = f"""You are a financial analyst. The management team of {slug} has been talking about their products in their investor meetings in the following way
<Management summary start>
{summary}
<Management summary end>

Focusing on the products mentioned in this summary, please check for insights in the following sales employee reviews.
The output should be in json format with the following fields:

- synthesis: based on the comments in the REVIEWS, summarize in 100 words or less how the SALES EMPLOYEES  have been talking about ALL PRODUCTS OF {slug} with references in square brackets. If there are no comments, please say "NULL". Example: "NULL" or "Product 1 is seeing strong traction within enteprise. Product 2 has just started seeing adoption. <sup>[ sales_id:"67890"]</sup>"
- products: which products are mentioned BOTH in management summary and REVIEWS.
- validated: yes/no - whether the products mentioned in the management summary are validated by the reviews. If there are no references, please say "NULL". Example: "yes" or "no" or "NULL"
The reviews are
<reviews  start>
{reviews_articles_str}
< reviews end>
        """
        print(prompt)
        # compress the prompt to 128000 tokens
        prompt = prompt[:500000]

        results = openai_service.get_completion(prompt)
        insights_list = parse_json_from_markdown(results)
        if isinstance(insights_list, dict):
            # Wrap single dictionary in a list
            insights_list = [insights_list]

        if not insights_list:
            print("No insights generated. Skipping.")
            continue
        print(f"Insights: {insights_list}")
        for insight in insights_list:
            products = insight.get('products', [])
            references = insight.get('references', {})
            validation = insight.get('synthesis', {})
            validated = insight.get('validated', "NULL")

            # Insert the insights into the product_thesis collection
            product_thesis_collection.update_many(
                {"ticker": ticker},
                {
                    "$set": {
                        "sales_insights": {
                            "products": products,
                            "references": references,
                            "validation": validation,
                            "validated": validated
                        },
                        "updated_at": datetime.now()
                    }
                },
                upsert=True
            )


if __name__ == "__main__":
    update_product_thesis_with_salesreviews()
