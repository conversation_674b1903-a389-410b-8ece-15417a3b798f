from src.ideas.long_term.update_sd import update_sd
from src.ideas.long_term.create_email import create_email_output
from src.ideas.long_term.blind_thesis import update_product_thesis_with_empreviews
from src.ideas.long_term.podcasts_thesis import update_product_thesis_with_podcasts
from src.ideas.long_term.sales_thesis import update_product_thesis_with_salesreviews
from src.ideas.long_term.trustpilot_thesis import update_product_thesis_with_prodreviews
from src.ideas.long_term.create_product_thesis_collection import create_product_thesis_collection


def run_long_term_ideas():
    update_sd()
    create_product_thesis_collection()
    update_product_thesis_with_podcasts()
    update_product_thesis_with_empreviews()
    update_product_thesis_with_prodreviews()
    update_product_thesis_with_salesreviews()
    create_email_output()


if __name__ == "__main__":
    run_long_term_ideas()
