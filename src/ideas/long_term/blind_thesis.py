from datetime import datetime, UTC
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.helpers.parse_json_from_markdown import parse_json_from_markdown


def update_product_thesis_with_empreviews():
    # for each ticker in product_thesis, find the podcasts in db["podcasts"] with sameticker.
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    product_thesis_collection = connection.get_collection("product_thesis")
    articles_collection = connection.get_blackberry_employee_collection("articles")
    comments_collection = connection.get_blackberry_employee_collection("comments")
    today = datetime.now(UTC).replace(hour=0, minute=0, second=0, microsecond=0)
    for product in product_thesis_collection.find({"updated_at": {"$gt": today}}, no_cursor_timeout=True):
        ticker = product["ticker"]
        summary = product["summary"]
        reviews_articles = articles_collection.find({
            "$and": [
                {"company_ticker": ticker},
                {"$expr": {"$eq": ["$author_company", "$company_slug"]}}
            ]
        })

        reviews_comments = comments_collection.find({
            "$and": [
                {"company_ticker": ticker},
                {"$expr": {"$eq": ["$author_company", "$company_slug"]}}
            ]
        })
        # If no reviews, skip to the next ticker
        if articles_collection.count_documents({"company_ticker": ticker}) == 0:
            print(f"No reviews found for {ticker} in articles")
            continue

        if comments_collection.count_documents({"company_ticker": ticker}) == 0:
            print(f"No reviews found for {ticker} in comments")
            continue
        # extract description from reviews_articles and content from reviews_comments into strings
        reviews_articles_str = ""
        reviews_comments_str = ""

        for reviews_article in reviews_articles:
            reviews_articles_str += "emp_id: a_" + str(reviews_article["_id"]) + " "

            reviews_articles_str += reviews_article["description"] + " "
        for reviews_comment in reviews_comments:
            reviews_comments_str += "emp_id: b_" + str(reviews_comment["_id"]) + " "

            reviews_comments_str += reviews_comment["content"] + " "

        prompt = f"""You are a financial analyst. The management team of {ticker} has been talking about their products in their investor meetings in the following way
<Management summary start>
{summary}
<Management summary end>

For ONLY the products mentioned in this summary above AND NOTHING ELSE, please check for references in the following employee reviews.
The output should be in json format with the following fields:

- products: which products are mentioned BOTH in management summary and REVIEWS
- synthesis: based on the comments in the REVIEWS, summarize in 100 words or less how the EMPLOYEES  have been talking about the products in the REVIEWS. in case there are no comments, please say "NULL". Dont start with "in the reviews", "the reviews say" etc, talk specifically about the product trends. Use square brackets to refer to specific _id being refered to. Example: " Product 1 is seeing strong traction within enteprise<sup>[emp_id: "a67890"]</sup>. Product 2 has just started seeing adoption.<sup>[ emp_id:"a67890"]</sup>"
- validated: yes/no - whether the products mentioned in the management summary are validated by the reviews. If there are no references, please say "NULL". Example: "yes" or "no" or "NULL"
The reviews are
<reviews  start>
{reviews_articles_str}
{reviews_comments_str}
< reviews end>
        """
        # print(prompt)
        # compress the prompt to 128000 tokens
        prompt = prompt[:500000]
        results = openai_service.get_completion(prompt)
        insights_list = parse_json_from_markdown(results)
        if isinstance(insights_list, dict):
            # Wrap single dictionary in a list
            insights_list = [insights_list]
        if not insights_list:
            print("No insights generated. Skipping.")
            continue
        print(f"Insights: {insights_list}")
        for insight in insights_list:
            products = insight.get('products', [])
            references = insight.get('references', {})
            validation = insight.get('synthesis', {})
            validated = insight.get('validated', "NULL")

            # Insert the insights into the product_thesis collection
            product_thesis_collection.update_many(
                {"ticker": ticker},
                {
                    "$set": {
                        "employee_insights": {
                            "products": products,
                            "references": references,
                            "validation": validation,
                            "validated": validated
                        },
                        "updated_at": datetime.now()
                    }
                },
                upsert=True
            )


if __name__ == "__main__":
    update_product_thesis_with_empreviews()
