import pandas as pd
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService


def get_insights_for_ticker(ticker, days=90):
    connection = DatabaseFactory().get_mongo_connection()
    qnas_collection = connection.get_collection("qnas")
    qna_importance_collection = connection.get_collection("qna_importance")
    llm_insights_collection = connection.get_collection("LLM_insights")
    llm_trend_collection = connection.get_collection("LLM_trend")
    llm_similarity_collection = connection.get_collection("LLM_similarity")
    insights = []
    for insight in llm_insights_collection.find({"ticker": ticker, "date": {"$gte": datetime.now() - timedelta(days)}}):
        insight_text = insight["insight"]
        qnaId = insight["qnaId"]
        date = insight["date"]
        # find the category for qnaId form qna_importance
        qna_importance = qna_importance_collection.find_one({"qnaId": qnaId})
        if qna_importance:
            category = qna_importance["category"]
        else:
            category = ""
        # find the trend for qnaId form LLM_trend
        llm_trend = llm_trend_collection.find_one({"qnaId": qnaId})
        trend = llm_trend["trend"] if llm_trend else ""
        trend_text = llm_trend["trend_rationale"] if llm_trend else ""

        llm_similarity = llm_similarity_collection.find_one({"qnaId": qnaId})
        similarity = llm_similarity["classification"] if llm_similarity else ""
        similarity_text = llm_similarity["rationale"] if llm_similarity else ""

        qna = qnas_collection.find_one({"_id": qnaId})
        answer = qna["answer"] if qna else ""

        insight_df = pd.DataFrame(
            {
                "qnaId": [qnaId],
                "date": [date],
                "answer": [answer],
                "trend": [trend],
                "trend_rationale": [trend_text],
                "similarity": [similarity],
                "similarity_text": [similarity_text],
                "category": [category],
                "insight": [insight_text]
            }
        )
        # append to insights
        insights.append(insight_df)
    # concatenate all dataframes in insights to a single dataframe
    if len(insights) > 0:
        insights = pd.concat(insights, ignore_index=True)
    else:
        insights = pd.DataFrame(columns=["qnaId", "date", "answer", "trend", "trend_rationale", "similarity", "similarity_text", "category", "insight"])
    return insights


def create_product_thesis_collection():
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    companies = connection.get_collection("companies")
    product_thesis_collection = connection.get_collection("product_thesis")
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")

    # Get all tickers from the database
    last_three_days = datetime.now() - timedelta(days=3)
    pipeline = [
        {
            "$match": {
                "date": {"$gte": last_three_days}
            }
        },
        {"$sort": {"date": -1}},
        {
            "$group": {
                "_id": "$ticker",
                "latest_event_id": {"$first": "$event_id"},
                "latest_date": {"$first": "$date"}
            }
        },
        {
            "$project": {
                "_id": 0,
                "ticker": "$_id",
            }
        }
    ]
    relevant_events = list(public_investor_events_outputs_collection.aggregate(pipeline))
    tickers = [_event["ticker"] for _event in relevant_events]
    tickers = list(set(tickers))
    



    START_POINT = datetime.now() - timedelta(days=180)
    END_POINT = datetime.now()
    for ticker in tickers:
        print(ticker)
        # Check if the ticker and summary2 already exists in the product_thesis collection
        # if zscore less than 1, skip
        if product_thesis_collection.find_one({"ticker": ticker, "zscore": {"$lt": 0}}):
            print(f"{ticker} zscore less than 0")
            continue
        # find sector for ticker from db["companies"]
        company = companies.find_one({"ticker": ticker})
        if not company:
            print(f"Company {ticker} not found in companies collection")
            continue
        sector = company["sector"]
        print(f"Sector: {sector}")
        insights = pd.DataFrame()
        insights = get_insights_for_ticker(ticker, days=500)
        if insights.empty:
            print("No insights found.")
            continue
        insights_for_prompt = insights[["qnaId", "date", "answer", "insight", "trend", "category"]]
        print(START_POINT)
        # look at the existing generations and then decide what to include in prompt or change in prompt.

        category_list = [
            "new product strategy or product launches",
            "business model or revenue or pricing model changes",
            "AI Machine learning products and monetization"
        ]

        # Filter the insights dataframe to only include the specified categories
        insights = insights_for_prompt[insights_for_prompt['category'].isin(category_list)]
        insights_in_range = insights[(insights['date'] < END_POINT) & (insights['date'] >= START_POINT)]
        comments_text = "\n".join(
            f"qnaId: {row.qnaId}\nDate: {row.date}\nInsight: {row.insight}\nAnswer: {row.answer}\n"
            for _, row in insights_in_range.iterrows()
        )

        # count the number of uptick in insights["trend"] from the dataframe and store it into uptick_count
        uptick_count = insights_in_range["trend"].value_counts().get("uptick", 0)
        print(f'Uptick count: {uptick_count}')
        if uptick_count < 1:
            print(f"No uptick trends found for {ticker} in the specified date range.")
            continue
        insights_in_range = insights_in_range[["qnaId", "date", "insight", "answer"]]

        prompt = f"""You are a financial analyst.

Extract from the management comments below exactly 4 bullet points (no more, no less) about new products discussed by management. Each bullet must be:
- Maximum 15-25 words
- One specific product insight per bullet
- Format: • [Insight with "quoted text" <sup>[qnaId:MONGODB_ID]</sup>]

CRITICAL REQUIREMENTS:
- Always cite insights using <sup>[qnaId:MONGODB_ID]</sup> tags where MONGODB_ID is the exact MongoDB ObjectId from the source
- Include actual management quotes in "double quotes"
- NO introductory phrases like "From the insights", "management says", "the comments indicate"
- Start directly with the product insight
- NO assumptions or inferences beyond what's explicitly stated in management comments

Management comments format:
qnaId: unique identifier (MongoDB ObjectId)
Date: comment date
Answer: management team comments
Insight: summary of product strategy changes and launches

<management comments start>
{comments_text.strip()}
<management comments end>

Example output:
- Product seeing "strong traction within enterprise" <sup>[qnaId:507f1f77bcf86cd799439011]</sup> with "20% customer adoption"
- New feature "started seeing adoption" <sup>[qnaId:507f1f77bcf86cd799439012]</sup> and is "popular with financial services customers"
- Launch shows "fastest adoption curve among any new product" <sup>[qnaId:507f1f77bcf86cd799439013]</sup>
- Enterprise tier "exceeded Q1 targets by 40%" <sup>[qnaId:507f1f77bcf86cd799439014]</sup> across all segments""".strip()
        summary = openai_service.get_completion(prompt=prompt)
        print(summary)
        latest_event = public_investor_events_outputs_collection.find_one(
            {"ticker": ticker},
            sort=[("date", -1)]
        )
        latest_event_id = latest_event.get("_id") if latest_event else None

        product_thesis_collection.update_many(
            {"ticker": ticker},
            {
                "$set": {
                    "summary": summary,
                    "sector": sector,
                    "event_id": latest_event_id,
                    "start_point": START_POINT,
                    "end_point": END_POINT,
                    "uptick_count": int(uptick_count),  # Convert numpy.int64 to int
                    "updated_at": datetime.now()
                }
            },
            upsert=True
        )
        print(f"Updated {ticker} in product_thesis with summary and uptick count.")


if __name__ == "__main__":
    create_product_thesis_collection()
