import numpy as np
from src.database.factory import DatabaseFactory


def update_sd():
    connection = DatabaseFactory().get_mongo_connection()
    product_thesis_collection = connection.get_collection("product_thesis")
    ideas = product_thesis_collection.find({"summary": {"$exists": True}})
    # find allsectors in the product_thesis collection
    sectors = product_thesis_collection.distinct("sector")
    print(sectors)
    # for each sector,find mean uptick count and stddev uptickcount
    for sector in sectors:
        print(sector)
        # find all uptick count for the sector
        uptick_counts = product_thesis_collection.find({"sector": sector}, {"uptick_count": 1})
        uptick_counts = [uptick_count["uptick_count"] for uptick_count in uptick_counts]
        print(uptick_counts)
        # calculate mean and stddev
        mean = np.mean(uptick_counts)
        stddev = np.std(uptick_counts)
        print(f"Mean: {mean}, Stddev: {stddev}")
        # update the product_thesis collection with mean and stddev
        product_thesis_collection.update_many(
            {"sector": sector},
            {
                "$set": {
                    "mean_uptick_count": mean,
                    "stddev_uptick_count": stddev
                }
            },
            upsert=True
        )
    for idea in ideas:
        # calculate zscore
        if idea["stddev_uptick_count"] == 0:
            zscore = 0
        else:

            zscore = (idea["uptick_count"] - idea["mean_uptick_count"]) / idea["stddev_uptick_count"]
        print(f"Zscore: {zscore}")
        # update the product_thesis collection with zscore
        product_thesis_collection.update_one(
            {"_id": idea["_id"]},
            {
                "$set": {
                    "zscore": zscore
                }
            },
            upsert=True
        )
        print(f"Updated {idea['ticker']} with zscore: {zscore}")


if __name__ == "__main__":
    update_sd()
