"""
For some cases we just summarize the podcasts without any insight, if the podcast has the flag summarize without insights
"""


from concurrent.futures import ThreadPoolExecutor
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.podcasts.process_status import ProcessStatus, ProcessStep


logger = get_logger(__name__)


def get_summarization_prompt(transcript):
    prompt = """## Role and Context
<PERSON> are an expert podcast analyst specializing in synthesizing key information from podcast transcripts. You will analyze complete podcast transcripts to create concise summaries for finance professionals making investment decisions, so precision, clarity, and comprehensiveness are critical.

## Task
Create a concise summary (under 100 words) of the podcast transcript that captures the most important and financially material points discussed. Focus on key themes, significant statements, and any information that could impact investment decisions.

## Input Variables
- **TRANSCRIPT**: Complete podcast transcript
{transcript}

## Output Requirements
- Always use direct quotes from the transcript in the summary
- Produce a paragraph-format summary (no bullet points or numbered lists)
- Highlight the two most important quotes using HTML mark tags like <mark>"quote"</mark>
- Focus on the substantive content of what was said, not the manner or tone in which it was delivered
- Include direct quotes from the transcript to support key points
- Emphasize specific, concrete statements that could affect investment decisions
- Prioritize the most significant themes and perspectives discussed
- Ensure all market-moving or financially material information is captured
- Extract and include any specific comments about revenue, hiring, or costs when present in the transcript

## Constraints
- Keep the summary under 100 words
- Mark only the two most important quotes
- Do not repeat the same points multiple times
- Avoid vague generalizations or commentary on tone, sentiment, or confidence levels
- Include only information directly from the provided transcript
- Omit your own opinions or speculations
- Maintain strict factual accuracy essential for financial decision-making
- Do not include statements about missing information or data not found in the transcript

## Example Format
"The podcast discusses Company X's strategic pivot into AI services. CEO Lisa Wong announced <mark>"we're allocating $500M to AI infrastructure over the next 18 months,"</mark> representing a major shift in capital allocation. She acknowledged current margin pressure but emphasized <mark>"we expect 20% revenue growth from AI products by 2026."</mark> The discussion also covered competitive dynamics and regulatory challenges in the sector, with particular focus on data privacy concerns affecting the European market. Wong mentioned plans to hire 200 AI engineers while reducing costs in legacy divisions by 15%."
"""
    summarization_prompt = prompt.format(transcript=transcript)
    return summarization_prompt


def get_summarization_prompt_bullets(transcript):
    prompt = """## Role and Context
You are an expert financial analyst specializing in analyzing financial podcasts. You will analyze podcast transcripts to identify key perspectives, themes, and insights about companies discussed. Your analysis will be used by finance professionals for critical investment decision-making.

## Task
Create a concise, actionable summary (under 4 points) of the entire podcast transcript that captures the most important insights and perspectives about the company. This summary must be optimized for quick consumption by busy financial professionals.

## Input Variables
- **TRANSCRIPT**: Complete podcast transcript
{transcript}

## Content Focus
- Focus on the substantive content of what was said, not on tone, delivery style, or emotional attributes (e.g., avoid phrases like "sounded confident," "seemed optimistic," or "appeared concerned")
- Prioritize concrete information: financial metrics, strategic decisions, operational changes, market developments, and specific business updates
- Extract and highlight any explicit comments about revenue performance, hiring plans, workforce changes, or cost management initiatives

## Output Requirements
- Format as concise bullet points (maximum 4 points total)
- Group related insights and themes into comprehensive bullet points
- Include direct quote references from the transcript for key insights
- From all quotes mentioned, highlight ONLY the two most significant quotes using HTML <mark> tags: <mark>"quote"</mark>
- Focus on the most material information that could impact investment decisions
- Prioritize insights about company performance, management perspectives, financial outlook, strategy changes, and market positioning
- Only include bullet points when substantive information is available; omit bullets or sections when no relevant data exists in the transcript

## Constraints
- Keep the summary under 4 points with each point being around 40-70 words
- Each bullet point should cover a distinct theme or topic - consolidate related information within each point
- Select and mark ONLY the two most important quotes across the entire summary that represent the most significant insights
- Avoid vague generalizations and subjective assessments of tone or demeanor
- Include only information directly from the provided transcript
- Omit your own opinions or speculations
- Do not include "nothing found" or placeholder text when information on a particular topic is not discussed in the transcript

## Example Format
- The company reported Q3 revenue of $2.1B, missing analyst expectations by 8%. Management attributes the shortfall to <mark>"unexpected headwinds in our European markets and delayed product launches"</mark> but maintains full-year guidance of $9-9.5B based on anticipated Q4 recovery.

- CEO expressed concern about competitive pressures, noting that market share declined 3 points YoY. The company is pivoting strategy to focus on premium segments and plans to <mark>"consolidate operations and reduce SKU count by 30% over the next 18 months"</mark> to improve margins.

- Management announced a shift from aggressive expansion to capital preservation, suspending the previously planned $500M share buyback program and redirecting funds toward R&D and debt reduction in response to macroeconomic uncertainty.

- The CFO revealed that operating margins improved 200 basis points despite revenue decline, driven by aggressive cost-cutting measures and supply chain optimization that exceeded internal targets by 15%.
"""
    summarization_prompt = prompt.format(
        transcript=transcript,
    )
    return summarization_prompt


def summarize_without_insights():
    connection = DatabaseFactory().get_mongo_connection()
    openai_service = OpenAIService()
    podcast_events_final_collection = connection.get_podcast_collection("podcast_events_final")

    with podcast_events_final_collection.find({"summary_only": True}, no_cursor_timeout=True).batch_size(30) as podcast_cursor:
        for podcast in podcast_cursor:
            transcript = podcast["transcript"]
            summarization_prompt = get_summarization_prompt(transcript=transcript)
            summarization_prompt_bullets = get_summarization_prompt_bullets(transcript=transcript)

            # summary = openai_service.get_completion_without_limits(prompt=summarization_prompt, temperature=0)
            # summary2 = openai_service.get_completion_without_limits(prompt=summarization_prompt_bullets, temperature=0)

            with ThreadPoolExecutor(max_workers=2) as executor:
                future1 = executor.submit(openai_service.get_completion_without_limits, prompt=summarization_prompt, temperature=0)
                future2 = executor.submit(openai_service.get_completion_without_limits, prompt=summarization_prompt_bullets, temperature=0)

                summary = future1.result()
                summary2 = future2.result()

            podcast_events_final_collection.update_one(
                {"_id": podcast["_id"]},
                {
                    "$set": {
                        "summary": summary,
                        "summary2": summary2,
                        "status": ProcessStatus.COMPLETED.value,
                        "completed_step": ProcessStep.SUMMARY_ONLY.value,
                        "show": True,
                        "next_step": None,
                    }
                }
            )
            logger.info(f"Summarizing podcast episode: {podcast['episode_title']}")


if __name__ == "__main__":
    summarize_without_insights()
