from datetime import datetime, timedelta
from listennotes import podcast_api
from src.core.logging import get_logger
from src.core.constants import LISTENNOTES_API_KEY
from src.database.factory import DatabaseFactory


logger = get_logger(__name__)


def convert_ms_to_date(date_ms):
    date_s = date_ms / 1000
    dt_utc = datetime.fromtimestamp(date_s)
    return dt_utc


def get_podcast_from_listennotes(podcast_id):
    client = podcast_api.Client(api_key=LISTENNOTES_API_KEY)
    response = client.fetch_podcast_by_id(
        id=podcast_id,
        sort='recent_first',
    )
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Error fetching podcast: {podcast_id}")


def _download_and_save_podcast(cut_off_date, podcast_id, summary_only, is_private):
    connection = DatabaseFactory().get_mongo_connection()
    podcasts_events_collection = connection.get_podcast_collection("podcast_events")
    try:
        podcast = get_podcast_from_listennotes(podcast_id)
    except Exception as e:
        logger.exception(f"Could not find podcast. Exception {e}")
        podcast = {"episodes": []}
    episodes_to_process = []
    for episode in podcast["episodes"]:
        episode_date = convert_ms_to_date(episode['pub_date_ms'])
        listennotes_id = episode["id"]
        if podcasts_events_collection.find_one({"listennotes_id": listennotes_id}):
            continue

        if episode_date > cut_off_date:
            episode_info = {
                "podcast_id": podcast_id,
                "podcast_title": podcast["title"],
                "episode_title": episode['title'],
                "audio_length_sec": episode["audio_length_sec"],
                "audio_link": episode["audio"],
                "date": episode_date,
                "description": episode["description"],
                "listennotes_id": listennotes_id,
                "podcast_link": episode["link"],
                "published_at": episode_date,
                "is_processed": False,
                "summary_only": summary_only,
                "is_private": is_private,
                "updated_at": datetime.now(),
            }
            episodes_to_process.append(episode_info)
    if len(episodes_to_process):
        podcasts_events_collection.insert_many(episodes_to_process)
    logger.info(f"Completed inserting episodes for {podcast['title']}")


def download_and_save_podcast(cut_off_date):
    connection = DatabaseFactory().get_mongo_connection()
    podcast_list_included_collection = connection.get_podcast_collection("podcast_list_included")
    with podcast_list_included_collection.find({"include_all": True}, no_cursor_timeout=True).batch_size(50) as podcast_list_cursor:
        for podcast in podcast_list_cursor:
            logger.info(f"Processing Podcast {podcast['podcast_title']}")
            podcast_id = podcast["podcast_id"]
            summary_only = podcast.get("summary_only", False)
            is_private = podcast.get("is_private", False)
            _download_and_save_podcast(cut_off_date=cut_off_date, podcast_id=podcast_id, summary_only=summary_only, is_private=is_private)
            logger.info(f"Completed podcast {podcast['podcast_title']}")


if __name__ == "__main__":
    ten_days_ago = datetime.now() - timedelta(days=10)
    download_and_save_podcast(cut_off_date=ten_days_ago)
    # get_podcast_from_listennotes("6326e537258c4e9cafffa203fff2d4eb")
