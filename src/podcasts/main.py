from datetime import datetime, timedelta
from src.podcasts.download_podcast import download_and_save_podcast
from src.podcasts.get_podcast_speakers import get_podcast_speakers
from src.podcasts.upload_podcasts_to_s3 import upload_podcast_to_s3
from src.podcasts.transcribe_podcast import transcribe_podcast
from src.podcasts.tag_speakers import tag_speakers
from src.podcasts.chunk_podcast import chunk_podcast_transcript
from src.podcasts.extract_insights import generate_insights
from src.podcasts.summarize_podcasts import summarize_podcasts
from src.podcasts.summarize_without_insights import summarize_without_insights
from src.podcasts.create_podcast_email import create_podcast_emails


def process_podcasts():
    ANALYSIS_WINDOW_DAYS = 180
    LOOKBACK_DAYS = 10
    days_ago = datetime.now() - timedelta(days=LOOKBACK_DAYS)
    download_and_save_podcast(cut_off_date=days_ago)
    get_podcast_speakers()
    upload_podcast_to_s3()
    transcribe_podcast()
    tag_speakers()
    chunk_podcast_transcript()
    generate_insights(analysis_window_days=ANALYSIS_WINDOW_DAYS)
    summarize_podcasts()
    summarize_without_insights()
    create_podcast_emails(CUTOFF_DATE=datetime.now(), LOOKBACK_DAYS=LOOKBACK_DAYS)


if __name__ == "__main__":
    process_podcasts()
