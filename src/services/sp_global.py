import requests
from threading import Lock
from src.core.logging import get_logger
from src.core.constants import SP_USERNAME, SP_PASSWORD
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_fixed
from src.services.rate_limiter import RateLimiter

logger = get_logger(__name__)

class RateLimitException(Exception):
    """Custom exception for rate limiting errors."""
    pass

class SPGlobalClient:
    _instance = None
    _lock = Lock()
    _rate_limiter = None

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(SPGlobalClient, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self._token = None
        self._auth_url = "https://api-ciq.marketintelligence.spglobal.com/gdsapi/rest/authenticate/api/v1/token"
        self._search_url = "https://api-ciq.marketintelligence.spglobal.com/gds/documents/api/v1/search?docType=TRANSCRIPTS_DOCUMENTS_API"
        self._download_url = "https://api-ciq.marketintelligence.spglobal.com/gds/documents/api/v1/download?docType=TRANSCRIPTS_DOCUMENTS_API"
        self._initialized = True

        if SPGlobalClient._rate_limiter is None:
            SPGlobalClient._rate_limiter = RateLimiter(min_interval=1.8)

    def _get_token(self):
        """Get a valid authentication token, refreshing if necessary"""
        auth_headers = {
            "accept": "application/json",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        auth_data = {
            "username": SP_USERNAME,
            "password": SP_PASSWORD
        }

        try:
            auth_response = requests.post(self._auth_url, headers=auth_headers, data=auth_data)
            auth_response.raise_for_status()
            self._token = auth_response.json().get("access_token")
            if not self._token:
                raise ValueError("Authentication successful but no access token received.")
            return self._token

        except Exception as e:
            logger.info(f"Authentication failed: {e}")
            self._token = None
            raise

    @retry(
        retry=retry_if_exception_type(RateLimitException),
        stop=stop_after_attempt(5),
        wait=wait_fixed(2)
    )
    def download_from_sp(self, unique_event_id):
        """Download transcript document from SP Global using the unique event ID"""
        try:
            token = self._get_token()
            search_headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            search_data = {
                "properties": {
                    "ciqKeyDevId": [unique_event_id],
                    "documentFormatTypeId": [7]
                }
            }

            SPGlobalClient._rate_limiter.wait_if_needed()
            response = requests.post(self._search_url, headers=search_headers, json=search_data)
            if response.status_code == 429:
                raise RateLimitException("Rate limit exceeded, retrying...")
            response.raise_for_status()
            response_data = response.json()

            document_id_index = response_data['headers'].index('transcriptDocumentId')
            document_id = response_data['rows'][0]['row'][document_id_index]
            logger.info(f"Search successful. Document ID: {document_id}")

            download_headers = {
                "accept": "*/*",
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
            download_data = {
                "properties": {
                    "transcriptDocumentId": document_id,
                    "documentFormatTypeId": 7
                }
            }

            data_response = requests.post(self._download_url, headers=download_headers, json=download_data)
            if data_response.status_code == 429:
                raise RateLimitException("Rate limit exceeded, retrying...")
            data_response.raise_for_status()
            logger.info("Transcript document downloaded successfully")
            return data_response.content

        except RateLimitException:
            raise
        except Exception as e:
            logger.error(f"Error downloading transcript document: {e}")
            return None
    
    @retry(
        retry=retry_if_exception_type(RateLimitException),
        stop=stop_after_attempt(5),
        wait=wait_fixed(2)
    )
    def download_pdf_from_sp(self, document_id: int, document_format_type_id: int = 3):
        """Download PDF Document from SP Global using SP Document ID"""
        headers = {
            'Content-Type': 'application/json',
            "Authorization": f"Bearer {self._get_token()}"
        }
        payload = {
            "properties": {
                "documentId": document_id,
                "documentFormatTypeId": document_format_type_id
            }
        }
        SPGlobalClient._rate_limiter.wait_if_needed()
        response = requests.post(self._download_url, headers=headers, json=payload)
        
        if response.status_code == 200:
            return response.content
        elif response.status_code == 429:
            raise RateLimitException("Rate limit exceeded, retrying...")
        else:
            logger.error(f"Error: {response.status_code} - {response.text}")
            return None
        
    @retry(
        retry=retry_if_exception_type(RateLimitException),
        stop=stop_after_attempt(5),
        wait=wait_fixed(2)
    )
    def search_events_by_ciqkeydevid(self, ciq_key_dev_id: int):
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self._get_token()}",
        }
        payload = {
            "properties": {
                "ciqKeyDevId": [ciq_key_dev_id],
                "documentFormatTypeId": [
                    3
                ]
            }
        }
        SPGlobalClient._rate_limiter.wait_if_needed()
        response = requests.post(self._search_url, headers=headers, json=payload)
        if response.status_code == 200:
            data = response.json()
            if data.get("numRows") != data.get("currentPageSize"):
                logger.info("There are more results than the current page size. Consider implementing pagination.")
                logger.info(f"Sample output:\n{data}")
            output = []
            if data["rows"]:
                for row in data["rows"]:
                    output.append(
                        {
                            data["headers"][index]: item
                            for index, item in enumerate(row["row"])
                        }
                    )
            return output
        elif response.status_code == 429:
            raise RateLimitException("Rate limit exceeded, retrying...")
        else:
            logger.error(f"Error: {response.status_code} - {response.text}")
            return []
    
    @retry(
        retry=retry_if_exception_type(RateLimitException),
        stop=stop_after_attempt(5),
        wait=wait_fixed(2)
    )
    def search_events_by_company(self, company_id: int, start_date: str, end_date: str, document_format_type_id: int = 3):
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self._get_token()}",
        }
        payload = {
            "properties": {
                "companyId": [company_id],
                "minTranscriptCreationDate": start_date,
                "maxTranscriptCreationDate": end_date,
                "documentFormatTypeId": [document_format_type_id],
            }
        }
        SPGlobalClient._rate_limiter.wait_if_needed()
        response = requests.post(self._search_url, headers=headers, json=payload)
        if response.status_code == 200:
            data = response.json()
            if data.get("numRows") != data.get("currentPageSize"):
                logger.info("There are more results than the current page size. Consider implementing pagination.")
                logger.info(f"Sample output:\n{data}")
            output = []
            if data["rows"]:
                for row in data["rows"]:
                    output.append(
                        {
                            data["headers"][index]: item
                            for index, item in enumerate(row["row"])
                        }
                    )
            return output
        elif response.status_code == 429:
            raise RateLimitException("Rate limit exceeded, retrying...")
        else:
            logger.error(f"Error: {response.status_code} - {response.text}")
            return []

# Example usage:
# client = SPGlobalClient()
# transcript = client.download_from_sp("your_unique_event_id")
