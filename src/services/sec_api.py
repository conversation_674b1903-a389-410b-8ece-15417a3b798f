from sec_api import Query<PERSON><PERSON>, XbrlApi
from src.core.constants import SEC_API_KEY
from datetime import datetime

def get_quarterly_revenue(ticker):
    """
    Extract quarterly revenue for a company from their 10-Q filing.

    Parameters:
    ticker (str): The company's ticker symbol (e.g., 'MSFT')
    api_key (str): Your SEC-API API key

    Returns:
    float: Revenue amount in millions/billions (as reported)
    """
    queryApi = QueryApi(api_key=SEC_API_KEY)
    xbrlApi = XbrlApi(api_key=SEC_API_KEY)

    query = {
        "query": f'formType:"10-Q" AND ticker:{ticker}',
        "from": "0",
        "size": "2",
        "sort": [{"filedAt": {"order": "desc"}}]
    }

    # Execute the query to find the filing
    response = queryApi.get_filings(query)

    # Check if a filing was found
    if len(response['filings']) == 0:
        return f"No 10-Q filing found for {ticker}"

    # Get the first filing (most recent if multiple exist)
    filing = response['filings'][0]
    accession_no = filing['accessionNo']
    # Use the XBRL API to extract financial data
    xbrl_data = xbrlApi.xbrl_to_json(accession_no=accession_no)
    return xbrl_data


def search_8k_filings(ticker: str, start_date: datetime, end_date: datetime) -> list:
    all_filings = []
    from_index = 0
    size_limit = 50  # Maximum size allowed by the API
    try:
        queryApi = QueryApi(api_key=SEC_API_KEY)
        date1 = start_date.strftime("%Y-%m-%d")
        date2 = end_date.strftime("%Y-%m-%d")
        
        while True:
            query = {
                "query": f"formType:\"8-K\" AND documentFormatFiles.type:*99* AND ticker:{ticker} AND filedAt:[{date1}T00:00:00 TO {date2}T23:59:59]",
                "from": str(from_index),
                "size": str(size_limit),
                "sort": [{"filedAt": {"order": "desc"}}]
            }
            
            response = queryApi.get_filings(query)
            if not response:
                break
            
            filings = response.get("filings", [])
            if not filings:
                break
                
            all_filings.extend(filings)
            
            # Check if we've retrieved all results
            total_value = response.get("total", {}).get("value", 0)
            if from_index + size_limit >= total_value:
                break
                
            # Move to the next page
            from_index += size_limit
    
    except Exception as e:
        print(f"Error searching 8-K filings: {e}")
    
    return all_filings