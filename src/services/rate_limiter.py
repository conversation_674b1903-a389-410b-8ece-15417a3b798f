import threading
import time


class RateLimiter:
    """Thread-safe rate limiter to ensure minimum delay between API calls"""

    def __init__(self, min_interval=1.8):
        self.min_interval = min_interval
        self.last_call_time = 0
        self.lock = threading.Lock()

    def wait_if_needed(self):
        """Wait if necessary to maintain minimum interval between calls"""
        with self.lock:
            current_time = time.perf_counter()
            time_since_last_call = current_time - self.last_call_time

            if time_since_last_call < self.min_interval:
                sleep_time = self.min_interval - time_since_last_call
                time.sleep(sleep_time)

            self.last_call_time = time.perf_counter()
