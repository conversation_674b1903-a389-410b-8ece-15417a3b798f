import json
import boto3
import logging
import concurrent.futures
from src.core.logging import get_logger
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type, before_sleep_log
from src.core.constants import AWS_ACCESS_KEY_ID, AWS_REGION, AWS_SECRET_ACCESS_KEY


logger = get_logger(__name__)

GLOBAL_BEDROCK_DELAY = 0.01


class BedrockManager:
    """
    Singleton class to manage AWS Bedrock client connections.
    Ensures a single connection is maintained and reused across the application.
    Supports parallel embedding generation for multiple texts.
    """
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(BedrockManager, cls).__new__(cls)
            cls._instance._client = None
            cls._instance._runtime_client = None
        return cls._instance

    def get_runtime_client(self) -> boto3.client:
        """
        Get or create a Bedrock Runtime client using cached connection.

        Returns:
            boto3.client: Configured Bedrock Runtime client
        """
        if self._runtime_client is None:
            try:
                self._runtime_client = boto3.client(
                    service_name='bedrock-runtime',
                    region_name=AWS_REGION,
                    aws_access_key_id=AWS_ACCESS_KEY_ID,
                    aws_secret_access_key=AWS_SECRET_ACCESS_KEY
                )
                logger.info("Successfully created AWS Bedrock Runtime client")
            except Exception as e:
                logger.error(f"Failed to create AWS Bedrock Runtime client: {str(e)}")
                raise
        return self._runtime_client

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=5, max=60),
        retry=retry_if_exception_type(Exception),
        before_sleep=before_sleep_log(logger, logging.WARNING),
        reraise=True
    )
    def get_embeddings(self, sentence, model_id="amazon.titan-embed-text-v2:0"):
        """
        Get embeddings for a single sentence using AWS Bedrock with automatic retries.
        Internal helper method used by both single and batch embedding methods.

        Args:
            sentence (str): The text to get embeddings for (max 10000 chars)
            model_id (str): The Bedrock model ID to use

        Returns:
            list: The embedding vector

        Raises:
            Exception: If the API call fails after retries
        """
        sentence = sentence[:10000]  # This wont happen in practice, but just in case
        logger.debug(f"Getting embeddings for text: {sentence[:50]}...")
        runtime_client = self.get_runtime_client()
        kwargs = {
            "modelId": model_id,
            "contentType": "application/json",
            "accept": "application/json",
            "body": json.dumps({
                "inputText": sentence
            })
        }
        response = runtime_client.invoke_model(**kwargs)
        response_body = json.loads(response['body'].read())
        return response_body['embedding']

    def get_embeddings_batch(self, texts, model_id="amazon.titan-embed-text-v2:0", max_workers=10):
        """
        Get embeddings for multiple texts in parallel using AWS Bedrock.

        Args:
            texts (list): List of texts to get embeddings for
            model_id (str): The Bedrock model ID to use
            max_workers (int): Maximum number of parallel workers

        Returns:
            list: List of embedding vectors corresponding to input texts

        Raises:
            Exception: If the API calls fail after retries
        """
        logger.info(f"Get embeddings called in batch mode for {len(texts)} texts")
        if not texts:
            return []
        if len(texts) == 1:
            return [self.get_embeddings(texts[0], model_id)]
        max_workers = min(max_workers, len(texts))

        embeddings = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_idx = {
                executor.submit(self.get_embeddings, text, model_id): i
                for i, text in enumerate(texts)
            }
            embeddings = [None] * len(texts)
            for future in concurrent.futures.as_completed(future_to_idx):
                idx = future_to_idx[future]
                try:
                    embedding = future.result()
                    embeddings[idx] = embedding
                except Exception as e:
                    logger.error(f"Error getting embedding for text at index {idx}: {str(e)}")
                    raise

        return embeddings
