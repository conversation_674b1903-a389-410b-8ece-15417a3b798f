from azure.ai.inference import <PERSON><PERSON><PERSON><PERSON>pletionsClient
from azure.core.credentials import AzureKeyCredential
from src.core.logging import get_logger
import logging
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type, before_sleep_log
from src.core.constants import AZURE_MINISTRAL_ENDPOINT, AZURE_MINISTRAL_API_VERSION, AZURE_MINISTRAL_KEY

logger = get_logger(__name__)

class MistralService:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(MistralService, cls).__new__(cls)
            cls._instance._client = None
        return cls._instance

    def get_client(self):
        if self._client is None:
            try:
                self._client = ChatCompletionsClient(
                    endpoint=AZURE_MINISTRAL_ENDPOINT,
                    credential=AzureKeyCredential(AZURE_MINISTRAL_KEY),
                    api_version=AZURE_MINISTRAL_API_VERSION
                )
                logger.info("Successfully created Mistral client")
            except Exception as e:
                logger.error(f"Failed to create Mistral client: {str(e)}")
                raise
        return self._client
    
    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=5, max=60),
        retry=retry_if_exception_type(Exception),
        before_sleep=before_sleep_log(logger, logging.WARNING),
        reraise=True
    )
    def get_completion_without_limits(
        self,
        prompt,
        model="Ministral-3B",
        temperature=0.0
    ):
        try:
            client = self.get_client()
            response = client.complete(
                model=model,
                messages=[
                    {"role": "user", "content": prompt},
                ],
                temperature=temperature,
            )
            parsed_response = response.choices[0].message.content.strip()
            return parsed_response
        except Exception as e:
            logger.error(e)
            return ""