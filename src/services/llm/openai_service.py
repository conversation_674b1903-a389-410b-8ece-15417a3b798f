import json
import logging
from openai import <PERSON>Request<PERSON>rror
from openai import <PERSON>zureOpenAI
from openai._types import NOT_GIVEN
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log,
)
from src.core.logging import get_logger
from src.core.constants import (
    OPENAI_AZURE_ENDPOINT_EAST_US,
    OPENAI_AZURE_KEY_EAST_US,
    OPENAI_AZURE_API_VERSION_EAST_US,
    OPENAI_AZURE_ENDPOINT_EAST_US_2,
    OPENAI_AZURE_API_VERSION_EAST_US_2,
    OPENAI_AZURE_KEY_EAST_US_2,
)

logger = get_logger(__name__)


class OpenAIService:
    """
    Singleton service for managing Azure OpenAI clients.
    Ensures a single client per endpoint is reused across the app.
    """

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(OpenAIService, cls).__new__(cls)
            cls._instance._client_eastus = None
            cls._instance._client_eastus2 = None
        return cls._instance

    def get_client_eastus(self):
        if self._client_eastus is None:
            try:
                self._client_eastus = AzureOpenAI(
                    azure_endpoint=OPENAI_AZURE_ENDPOINT_EAST_US,
                    api_version=OPENAI_AZURE_API_VERSION_EAST_US,
                    api_key=OPENAI_AZURE_KEY_EAST_US,
                )
                logger.info("Successfully created OpenAI East US client")
            except Exception as e:
                logger.error(f"Failed to create OpenAI East US client: {str(e)}")
                raise
        return self._client_eastus

    def get_client_eastus2(self):
        if self._client_eastus2 is None:
            try:
                self._client_eastus2 = AzureOpenAI(
                    azure_endpoint=OPENAI_AZURE_ENDPOINT_EAST_US_2,
                    api_version=OPENAI_AZURE_API_VERSION_EAST_US_2,
                    api_key=OPENAI_AZURE_KEY_EAST_US_2,
                )
                logger.info("Successfully created OpenAI East US 2 client")
            except Exception as e:
                logger.error(f"Failed to create OpenAI East US 2 client: {str(e)}")
                raise
        return self._client_eastus2

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=5, max=60),
        retry=retry_if_exception_type(Exception),
        before_sleep=before_sleep_log(logger, logging.WARNING),
        reraise=True,
    )
    def get_completion(
        self,
        prompt,
        model="gpt-4o",
        system_prompt="You are a helpful analyst",
        max_token=500,
        temperature=0.0,
        response_format=NOT_GIVEN,
    ):
        try:
            client = self.get_client_eastus()  # ✅ reuse singleton client
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt},
                ],
                max_tokens=max_token,
                response_format=response_format,
                temperature=temperature,
            )
            if response.choices[0].finish_reason == "content_filter":
                return ""
            return response.choices[0].message.content.strip()
        except BadRequestError as e:
            logger.error(e)
            if response_format == {"type": "json_object"}:
                return json.dumps({})
            return ""

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=5, max=60),
        retry=retry_if_exception_type(Exception),
        before_sleep=before_sleep_log(logger, logging.WARNING),
        reraise=True,
    )
    def get_completion_without_limits(
        self,
        prompt,
        model="gpt-4o",
        temperature=0.0,
        response_format=NOT_GIVEN,
    ):
        try:
            client = self.get_client_eastus()  # ✅ reuse singleton client
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                response_format=response_format,
                temperature=temperature,
            )
            if response.choices[0].finish_reason == "content_filter":
                return ""
            return response.choices[0].message.content.strip()
        except BadRequestError as e:
            logger.error(e)
            if response_format == {"type": "json_object"}:
                return json.dumps({})
            return ""

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=60, min=60, max=600),
        retry=retry_if_exception_type(Exception),
        before_sleep=before_sleep_log(logger, logging.WARNING),
        reraise=True,
    )
    def get_gpt_5_completion(
        self,
        prompt,
        model="gpt-5",
        response_format=NOT_GIVEN,
    ):
        try:
            client = self.get_client_eastus2()
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                response_format=response_format,
            )
            if response.choices[0].finish_reason == "content_filter":
                return ""
            return response.choices[0].message.content.strip()
        except BadRequestError as e:
            logger.error(e)
            if response_format == {"type": "json_object"}:
                return json.dumps({})
            return ""

    def close(self):
        """
        Close underlying httpx clients to free sockets.
        Call this on shutdown.
        """
        if self._client_eastus is not None:
            self._client_eastus.close()
            self._client_eastus = None
            logger.info("Closed OpenAI East US client")

        if self._client_eastus2 is not None:
            self._client_eastus2.close()
            self._client_eastus2 = None
            logger.info("Closed OpenAI East US 2 client")
