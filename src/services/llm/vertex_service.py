import logging
from google import genai
from google.oauth2 import service_account
from tenacity import (
    retry,
    before_sleep_log,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)
from src.core.logging import get_logger
from src.core.constants import (
    VERTEX_LOCATION,
    VERTEX_CREDENTIALS_PATH,
    VERTEX_PROJECT_ID,
)


logger = get_logger(__name__)


class GeminiService:
    """
    Singleton class to manage Google Gemini client connections through vertex.
    Ensures a single connection is maintained and reused across the application.
    """

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(GeminiService, cls).__new__(cls)
            cls._instance._client = None
        return cls._instance

    def _get_google_creds(self):
        creds = service_account.Credentials.from_service_account_file(
            VERTEX_CREDENTIALS_PATH,
            scopes=["https://www.googleapis.com/auth/cloud-platform"],
        )
        return creds

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=5, max=60),
        retry=retry_if_exception_type(Exception),
        before_sleep=before_sleep_log(logger, logging.WARNING),
        reraise=True,
    )
    def get_completion(
        self,
        prompt,
        model="gemini-2.5-pro",
        config=None
    ):
        creds = self._get_google_creds()

        client = genai.Client(
            vertexai=True,
            project=VERTEX_PROJECT_ID,
            location=VERTEX_LOCATION,
            credentials=creds,
        )
        response = client.models.generate_content(
            model=model,
            contents=prompt,
            config=config
        )
        llm_response = response.text
        return llm_response
