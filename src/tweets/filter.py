import re
from src.services.llm.openai_service import OpenAIService
from src.services.llm.mistral_service import MistralService
from src.tweets.prompt import PROMPT_TEMPLATE, CATEGORIZATION_PROMPT_TEMPLATE
from src.database.factory import DatabaseFactory

# Initialize LLM services for content analysis
openai_service = OpenAIService()
mistral_service = MistralService()

# Database collections for category and company data
connection = DatabaseFactory().get_mongo_connection()
categories_collection = connection.get_collection("sector_categories")
companies_collection = connection.get_collection("companies")

def is_safe_tweet(tweet: str) -> bool:
    """
    Filter tweets for inappropriate or sensitive content using LLM.

    Uses an LLM to analyze tweet content and flag potentially problematic tweets
    including those with religious references, violence, hate speech, political
    content, or non-English text.

    Args:
        tweet: The tweet text to analyze

    Returns:
        True if the tweet is safe to process, False if it should be filtered out

    Note:
        Currently uses Mistral service. OpenAI service is available as fallback.
    """
    prompt = PROMPT_TEMPLATE.format(tweet=tweet)
    # response = openai_service.get_completion_without_limits(prompt)
    response = mistral_service.get_completion_without_limits(prompt)

    if response == "FLAGGED":
        return False
    elif response == "SAFE":
        return True
    else:
        # Default to filtering out if response is unclear
        return False

def categorize_tweet(tweet: str, ticker: str) -> str:
    """
    Categorize a tweet into sector-specific categories using LLM.

    Retrieves the appropriate category list based on the company's sector and
    uses an LLM to classify the tweet into the most relevant category.

    Args:
        tweet: The tweet text to categorize
        ticker: Stock ticker symbol to identify the company and its sector

    Returns:
        Category name as a string. Returns "others" if categorization fails
        or no suitable category is found.

    Processing:
        1. Looks up company sector from ticker
        2. Retrieves sector-specific categories from database
        3. Adds "personal" and "others" as fallback categories
        4. Uses LLM to classify tweet into one category
        5. Extracts category from LLM response using regex

    Note:
        Currently uses Mistral service. Defaults to "software" sector if not found.
    """
    # Lookup company sector
    company_doc = companies_collection.find_one({"ticker": ticker})
    if company_doc and company_doc.get("sector"):
        sector = company_doc["sector"]
    else:
        sector = "software"  # Default sector if not found

    # Build category list for this sector
    sector_doc = categories_collection.find({"sector": sector})
    categories = [doc["category"] for doc in sector_doc]
    if "general description of business" in categories:
        categories.remove("general description of business")
    categories.append("personal")
    categories.append("others")

    # Format categories for LLM prompt
    categories_text = "\n".join([f"  {i+1}. {category}" for i, category in enumerate(categories)])

    # Get LLM classification
    prompt = CATEGORIZATION_PROMPT_TEMPLATE.format(tweet=tweet, categories=categories_text)
    # response = openai_service.get_completion_without_limits(prompt)
    response = mistral_service.get_completion_without_limits(prompt)

    # Extract category from response
    category_match = re.search(r'Category:\s*(?:\d+\.\s*)?(.+)', response, re.IGNORECASE)

    if category_match:
        return category_match.group(1).strip()
    else:
        return "others"