# Tweet Insights Pipeline

This module processes tweet data from the Blackberry database and generates structured insights for analytics. It filters unsafe content, categorizes tweets using LLM services, and transforms raw tweet data into typed schemas.

## Overview

The pipeline processes different tweet types (regular posts, retweets, quoted tweets, and threads) and stores categorized insights in a dedicated collection for downstream analytics.

## Module Structure

### [schemas.py](schemas.py)

Defines TypedDict schemas for structured tweet data representation.

**Tweet Types:**
- `RegularPost` - Standard standalone tweets
- `RetweetPost` - Reposted content from other users
- `QuotedPost` - Tweets that quote/reference another tweet with commentary
- `ThreadPost` - Multi-tweet threads with sequential replies
- `ReplyPost` - Individual replies within a thread

All schemas include metadata like author info, timestamps, images, and categorization.

### [filter.py](filter.py)

Provides content filtering and categorization using LLM services (Mistral/OpenAI).

**Functions:**
- `is_safe_tweet(tweet: str) -> bool` - Filters unsafe/inappropriate content using LLM
  - Flags tweets with religious references, violence, hate speech, political content, or non-English text
  - Returns `True` if safe to process, `False` otherwise

- `categorize_tweet(tweet: str, ticker: str) -> str` - Categorizes tweets into sector-specific categories
  - Looks up company sector from ticker
  - Retrieves sector-specific categories from database
  - Uses LLM to classify tweet into most relevant category
  - Returns category name (defaults to "others" if categorization fails)

### [prompt.py](prompt.py)

Contains LLM prompt templates for tweet processing.

**Templates:**
- `PROMPT_TEMPLATE` - Content safety filtering prompt
  - Expected output: "FLAGGED" or "SAFE"

- `CATEGORIZATION_PROMPT_TEMPLATE` - Tweet categorization prompt
  - Dynamic categories injected based on company sector
  - Expected output format: "Category: <category_name>"

### [processing.py](processing.py)

Transforms raw tweet documents into structured insight objects.

**Function:**
- `generate_insight(tweet_doc: dict, people_doc: dict) -> RegularPost|RetweetPost|QuotedPost|ThreadPost|None`
  - Processes tweet and author documents
  - Determines tweet type (standalone, quoted, retweet, thread)
  - For threads: Recursively collects all replies by following parent-child relationships
  - Applies categorization to all tweet content
  - Returns typed insight object

### [generate_insights.py](generate_insights.py)

Orchestrates the end-to-end insights generation pipeline.

**Main Function:**
- `populate_tweets_insights(start_date: datetime, end_date: datetime) -> None`
  - Batch processes tweets by ticker and date range
  - Works backwards from start_date to end_date, day by day
  - For each ticker/date combination:
    1. Checks if insights already exist (skips if present)
    2. Retrieves all tweets for that ticker/date
    3. Filters unsafe content using `is_safe_tweet()`
    4. Skips reply tweets (processed as part of threads)
    5. Generates insights using `generate_insight()`
    6. Stores in `tweets_insights` collection with upsert

**Database Collections:**
- **Source:** `blackberry.tweets_new`, `blackberry.people`
- **Target:** `tweets_insights` (structured insights storage)

## Processing Flow

```
Raw Tweet Data (Blackberry DB)
    ↓
Safety Filter (is_safe_tweet)
    ↓
Tweet Type Detection (generate_insight)
    ↓
Categorization (categorize_tweet)
    ↓
Structured Insight Schema
    ↓
tweets_insights Collection
```

## Usage Example

```python
from datetime import datetime
from src.tweets.generate_insights import populate_tweets_insights

# Process tweets for a date range
start_date = datetime(2025, 1, 15)
end_date = datetime(2025, 1, 1)

populate_tweets_insights(start_date, end_date)
```

## Database Schema

### Insights Document Structure
```python
{
    "ticker": str,              # Stock ticker symbol
    "cutoff_date": datetime,    # Start of date range (exclusive)
    "end_date": datetime,       # End of date range (inclusive)
    "insights": [               # List of typed insight objects
        {
            "post_type": "regular" | "retweet" | "quoted" | "thread",
            "post_link": str,
            "post_author_name": str,
            "post_body": str,
            "category": str,
            "datetime": datetime,
            # ... additional fields based on post_type
        }
    ]
}
```

## Notes

- LLM service defaults to Mistral (OpenAI available as fallback)
- Reply tweets are only processed as part of thread structures
- Upsert pattern prevents duplicate processing of same ticker/date
- Default sector is "software" if ticker lookup fails
- Thread replies are collected by traversing parent_tweet_id relationships
