from pymongo.collection import Collection
from src.database.factory import DatabaseFactory
from src.tweets.filter import categorize_tweet
from src.tweets.schemas import RegularPost, RetweetPost, QuotedPost, ReplyPost, ThreadPost
from src.core.logging import get_logger

logger = get_logger(__name__)
connection = DatabaseFactory().get_mongo_connection()

# Collections for accessing tweet and user data from Blackberry database
tweets_collection: Collection = connection.get_blackberry_tweets_collection("tweets_new")
people_collection: Collection = connection.get_blackberry_tweets_collection("people")

def generate_insight(tweet_doc: dict, people_doc: dict) -> RegularPost|RetweetPost|QuotedPost|ThreadPost|None:
    """
    Generate structured insight from tweet and people documents.

    Processes a tweet and its associated author information to create a typed insight
    object. Handles different tweet types including regular posts, retweets, quoted tweets,
    and threads with their replies.

    Args:
        tweet_doc: Document from tweets collection containing tweet data including:
                   - tweet_id, tweet_type, tweet_body, permalink, handle
                   - quoted_tweet_data (for quoted tweets)
                   - attached_images, datetime
        people_doc: Document from people collection containing author information:
                    - name, title, company_name, twitter_handle, ticker

    Returns:
        One of RegularPost, RetweetPost, QuotedPost, or ThreadPost depending on tweet type.
        Returns None if the tweet type is not supported.

    Processing Logic:
        - Checks if tweet is part of a thread by counting daughter tweets
        - For standalone tweets: Creates RegularPost, QuotedPost, or RetweetPost
        - For threads: Recursively collects all replies and creates ThreadPost
        - Each post is categorized using the categorize_tweet function
    """

    # Check if this tweet is part of a thread by counting child tweets
    daughter_tweets_count = tweets_collection.count_documents({"root_tweet_id": tweet_doc["tweet_id"]})
    
    if daughter_tweets_count == 0:
        # Standalone tweet (not part of a thread)

        if tweet_doc["tweet_type"] == "original" and tweet_doc["quoted_tweet_data"] is None:
            # Regular standalone post without quoted content
            return RegularPost(
                post_type = "regular",
                post_link = tweet_doc["permalink"],
                post_handle = tweet_doc["handle"],
                post_author_name = people_doc["name"],
                post_author_title = people_doc["title"],
                post_author_company = people_doc["company_name"],
                post_body = tweet_doc["tweet_body"],
                post_images = tweet_doc["attached_images"],
                category = categorize_tweet(tweet_doc["tweet_body"], people_doc["ticker"]),
                datetime = tweet_doc["datetime"],
            )
        
        elif tweet_doc["tweet_type"] == "original" and tweet_doc["quoted_tweet_data"] is not None:
            # Post that quotes another tweet
            source_handle = tweet_doc["quoted_tweet_data"]["handle"]
            source_people = people_collection.find_one({"handle": source_handle})
            quoted_tweet_body = tweet_doc["quoted_tweet_data"].get("tweet_body")

            # Combine original post and quoted content for categorization
            if quoted_tweet_body:
                full_tweet = tweet_doc["tweet_body"] + "\n\nQuoted tweet:\n" + quoted_tweet_body
            else:
                full_tweet = tweet_doc["tweet_body"]

            return QuotedPost(
                post_type = "quoted",
                post_link = tweet_doc["permalink"],
                post_handle = tweet_doc["handle"],
                post_author_name = people_doc["name"],
                post_author_title = people_doc["title"],
                post_author_company = people_doc["company_name"],
                post_body = tweet_doc["tweet_body"],
                post_images = tweet_doc["attached_images"],
                source_handle = source_handle,
                source_author_name = source_people["name"] if source_people else None,
                source_author_title = source_people["title"] if source_people else None,
                source_author_company = source_people["company_name"] if source_people else None,
                source_body = tweet_doc["quoted_tweet_data"]["tweet_body"],
                source_images = tweet_doc["quoted_tweet_data"]["attached_images"],
                category = categorize_tweet(full_tweet, people_doc["ticker"]),
                datetime = tweet_doc["datetime"],
            )

        elif tweet_doc["tweet_type"] == "repost":
            # Retweet/repost of another user's content
            source_people = people_collection.find_one({"handle": tweet_doc["handle"]})
            
            return RetweetPost(
                post_type = "retweet",
                post_link = tweet_doc["permalink"],
                post_handle = people_doc["twitter_handle"],
                post_author_name = people_doc["name"],
                post_author_title = people_doc["title"],
                post_author_company = people_doc["company_name"],
                source_handle = tweet_doc["handle"],
                source_author_name = source_people["name"] if source_people else None,
                source_author_title = source_people["title"] if source_people else None,
                source_author_company = source_people["company_name"] if source_people else None,
                source_body = tweet_doc["tweet_body"],
                source_images = tweet_doc["attached_images"],
                category = categorize_tweet(tweet_doc["tweet_body"], people_doc["ticker"]),
                datetime = tweet_doc["datetime"],
            )
    
    else:
        # Thread with multiple connected tweets
        current_tweet_id = tweet_doc["tweet_id"]
        replies = []

        # Traverse the thread chain by following parent-child relationships
        for i in range(daughter_tweets_count):
            reply = tweets_collection.find_one({"parent_tweet_id": current_tweet_id})
            if not reply:
                logger.warning(f"Reply not found for tweet ID: {current_tweet_id}")
                break

            replies.append(
                ReplyPost(
                    reply_number = i + 1,
                    reply_link = reply["permalink"],
                    reply_handle = reply["handle"],
                    reply_body = reply["tweet_body"],
                    reply_images = reply["attached_images"],
                    category = categorize_tweet(reply["tweet_body"], people_doc["ticker"]),
                    datetime = reply["datetime"],
                )
            )
            # Move to next tweet in the chain
            current_tweet_id = reply["tweet_id"]
        
        return ThreadPost(
            post_type = "thread",
            post_link = tweet_doc["permalink"],
            post_handle = tweet_doc["handle"],
            post_author_name = people_doc["name"],
            post_author_title = people_doc["title"],
            post_author_company = people_doc["company_name"],
            post_body = tweet_doc["tweet_body"],
            post_images = tweet_doc["attached_images"],
            post_replies = replies,
            category = categorize_tweet(tweet_doc["tweet_body"], people_doc["ticker"]),
            datetime = tweet_doc["datetime"],
        )


