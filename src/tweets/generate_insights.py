import pymongo
from datetime import datetime, timedelta
from src.core.logging import get_logger, configure_logging
from src.database.factory import DatabaseFactory
from src.core.constants import LOG_LEVEL
from src.tweets.filter import categorize_tweet, is_safe_tweet
from src.tweets.processing import generate_insight

configure_logging(log_level=LOG_LEVEL)
logger = get_logger(__name__)

def normalize_date(date: datetime) -> datetime:
    """Normalize datetime to midnight (00:00:00) for consistent date handling."""
    return date.replace(hour=0, minute=0, second=0, microsecond=0)

# Database connections
connection = DatabaseFactory().get_mongo_connection()

# Collection for storing processed insights
insights_collection = connection.get_tweets_collection("tweets_insights")
# summary_collection = connection.get_tweets_collection("tweets_summary")  # Reserved for future use

# Source collections from Blackberry database
tweets_collection = connection.get_blackberry_tweets_collection("tweets_new")
people_collection = connection.get_blackberry_tweets_collection("people")

def populate_tweets_insights(start_date: datetime, end_date: datetime) -> None:
    """
    Generate and store tweet insights for all tickers within a date range.

    Processes tweets day by day, working backwards from start_date to end_date.
    For each ticker and date combination, retrieves tweets, filters unsafe content,
    generates structured insights, and stores them in the insights collection.

    Args:
        start_date: Starting date (processed backwards from this date)
        end_date: Ending date (process until this date is reached)

    Processing Flow:
        1. Normalize dates to midnight for consistency
        2. Get all unique tickers from tweets collection
        3. For each day (working backwards):
           - For each ticker:
             - Check if insights already exist (skip if present)
             - Retrieve tweets for that ticker/date
             - Filter out unsafe tweets using is_safe_tweet()
             - Skip reply tweets (handled as part of threads)
             - Generate insights using generate_insight()
             - Store in insights_collection with upsert

    Database Schema:
        Insights documents contain:
        - ticker: Stock ticker symbol
        - cutoff_date: Start of date range (exclusive)
        - end_date: End of date range (inclusive)
        - insights: List of structured post objects

    Note:
        Uses upsert to prevent duplicate processing of same ticker/date combinations.
    """
    # Normalize dates to ensure 00:00:00 time
    current_date = normalize_date(start_date)
    end_date = normalize_date(end_date)
    all_tickers = list(tweets_collection.distinct("ticker"))
    logger.info(f"Starting insights population from {current_date} to {end_date} for all tickers")

    # Process backwards from start_date to end_date
    while current_date > end_date:
        
        next_date = current_date - timedelta(days=1)

        for ticker in all_tickers:
            # Check if insights already exist for this ticker and date range
            existing_insights = insights_collection.find_one({
                "ticker": ticker,
                "cutoff_date": next_date,
                "end_date": current_date
            })

            if existing_insights:
                logger.info(f"Skipping ticker: {ticker} for date {next_date} - insights already exist")
                continue

            # Retrieve all tweets for this ticker within the date range
            tweets = tweets_collection.find(
                {"datetime": {"$gte": next_date, "$lt": current_date}, "ticker": ticker}
            ).sort("datetime", pymongo.DESCENDING)
            insights = []
            tweet_bodies = []

            logger.info(f"Processing ticker: {ticker} for date: {next_date}")

            for tweet in tweets:
                # Get author information
                people = people_collection.find_one({"_id": tweet["people_id"]})

                # Filter out unsafe content
                if not is_safe_tweet(tweet["tweet_body"]):
                    logger.warning(f"Unsafe tweet found for ticker: {ticker} and date: {next_date}")
                    logger.warning(f"Tweet: {tweet['tweet_body']}")
                    continue

                # Skip tweets without author information
                if not people:
                    logger.warning(f"No people found for tweet ID: {tweet['_id']}")
                    continue

                # Skip reply tweets (they're processed as part of thread posts)
                if tweet["tweet_type"] == "reply":
                    continue

                # Generate structured insight
                insight = generate_insight(tweet, people)

                insights.append(insight)
                tweet_bodies.append(tweet["tweet_body"])

            if insights:
                # Store insights using upsert to prevent duplicate processing
                insights_collection.update_one(
                    {
                        "ticker": ticker,
                        "cutoff_date": next_date,
                        "end_date": current_date
                    },
                    {
                        "$set": {
                            "ticker": ticker,
                            "cutoff_date": next_date,
                            "end_date": current_date,
                            "insights": insights,
                        }
                    },
                    upsert=True
                )
                # Summary collection storage - reserved for future use
                # summary_collection.update_one(
                #     {
                #         "ticker": ticker,
                #         "cutoff_date": next_date,
                #         "end_date": current_date
                #     },
                #     {
                #         "$set": {
                #             "ticker": ticker,
                #             "cutoff_date": next_date,
                #             "end_date": current_date,
                #             "summary": tweet_bodies,
                #         }
                #     },
                #     upsert=True
                # )

        # Move to previous day
        current_date = current_date - timedelta(days=1)
