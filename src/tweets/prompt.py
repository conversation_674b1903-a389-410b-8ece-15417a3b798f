# Prompt template for content safety filtering
# Used by: is_safe_tweet() function in filter.py
# Expected output: "FLAGGED" or "SAFE"
PROMPT_TEMPLATE = """You are a content filter. Given a tweet, decide whether it should be flagged or not.
Flag a tweet if any of the following conditions are true:
	1.	It contains references to religion, religious groups, or religious practices.
	2.	It contains references to violence, threats, or promotion of harm.
	3.	It contains antisemitic content, hate speech, offensive slurs, or discriminatory language.
	4.	It discusses controversial or politically sensitive issues (e.g., elections, political figures, government policies, wars, protests).
	5.	It is not written in English.

Output only:
	-	"FLAGGED" if the tweet meets any of the above conditions.
	-	"SAFE" if none of the above conditions are met.

Example:
	-	Input: "We must fight against them, they are destroying our religion!" → Output: "FLAGGED"
	-	Input: "Had a great time at the park with my dog today!" → Output: "SAFE"

Tweet: {tweet}
"""

# Prompt template for tweet categorization
# Used by: categorize_tweet() function in filter.py
# Dynamic categories are injected based on company sector
# Expected output format: "Category: <category_name>"
CATEGORIZATION_PROMPT_TEMPLATE = """You are an expert analyst.
Your task is to classify a given tweet into one of the following categories:

{categories}

Instructions:
	-	Always return only one category.
    -   Do not return only number. Return the category name.
	-	If multiple categories might apply, choose the most specific.
	-	If the tweet is unrelated to the categories, use "others".

Output Format:
    Category: <one of the 19 categories>

Tweet: {tweet}
"""