from typing import TypedDict, Literal, Optional, List
from datetime import datetime

class RegularPost(TypedDict):
    """
    Schema for a regular standalone tweet.
    """
    post_type           : Literal["regular"]
    post_link           : str # Permalink URL to the tweet
    post_handle         : str # Twitter handle of the author
    post_author_name    : str # Full name of the author
    post_author_title   : str # Job title of the author
    post_author_company : str # Company of the author
    post_body           : str # Text content of the tweet
    post_images         : List[str] # Images attached to the tweet
    category            : str # Category of the tweet
    datetime            : datetime # Timestamp of the tweet

class RetweetPost(TypedDict):
    """
    Schema for a retweet/repost.

    Represents when a user reposts another user's content. Contains both
    the reposter's information and the original source information.
    """
    post_type             : Literal["retweet"]
    post_link             : str # Permalink URL to the retweet
    post_handle           : str # Twitter handle of the person who retweeted
    post_author_name      : str # Full name of the person who retweeted
    post_author_title     : str # Job title of the person who retweeted
    post_author_company   : str # Company of the person who retweeted
    source_handle         : str # Twitter handle of the original author
    source_author_name    : Optional[str] # Full name of original author (None if not in DB)
    source_author_title   : Optional[str] # Job title of original author (None if not in DB)
    source_author_company : Optional[str] # Company of original author (None if not in DB)
    source_body           : str # Text content of the original tweet
    source_images         : List[str] # Images attached to the original tweet
    category              : str # Category of the tweet
    datetime              : datetime # Timestamp of the retweet

class QuotedPost(TypedDict):
    """
    Schema for a quoted tweet.

    Represents when a user quotes another tweet with their own commentary.
    Contains the user's post, the quoted content, and both authors' information.
    """
    post_type             : Literal["quoted"]
    post_link             : str # Permalink URL to the quote tweet
    post_handle           : str # Twitter handle of the person quoting
    post_author_name      : str # Full name of the person quoting
    post_author_title     : str # Job title of the person quoting
    post_author_company   : str # Company of the person quoting
    post_body             : str # Commentary text added by the quoter
    post_images           : List[str] # Images attached to the quote tweet
    source_handle         : str # Twitter handle of the quoted tweet's author
    source_author_name    : Optional[str] # Full name of quoted author (None if not in DB)
    source_author_title   : Optional[str] # Job title of quoted author (None if not in DB)
    source_author_company : Optional[str] # Company of quoted author (None if not in DB)
    source_body           : str # Text content of the quoted tweet
    source_images         : List[str] # Images attached to the quoted tweet
    category              : str # Category of the tweet
    datetime              : datetime # Timestamp of the quote tweet

class ReplyPost(TypedDict):
    """
    Schema for an individual reply within a thread.

    Represents a single tweet in a multi-tweet thread. Used as a component
    within ThreadPost to represent sequential replies.
    """
    reply_number : int # 1-indexed position in the thread
    reply_link   : str # Permalink URL to this specific reply
    reply_handle : str # Twitter handle of the reply author
    reply_body   : str # Text content of the reply
    reply_images : List[str] # Images attached to the reply
    category     : str # Category of the reply
    datetime     : datetime # Timestamp of the reply

class ThreadPost(TypedDict):
    """
    Schema for a multi-tweet thread.

    Represents a thread consisting of an initial post followed by one or more
    sequential replies. The thread is identified by having "daughter tweets"
    (child tweets with the same root_tweet_id).
    """
    post_type           : Literal["thread"]
    post_link           : str # Permalink URL to the initial tweet
    post_handle         : str # Twitter handle of the thread author
    post_author_name    : str # Full name of the thread author
    post_author_title   : str # Job title of the thread author
    post_author_company : str # Company of the thread author
    post_body           : str # Text content of the initial tweet
    post_images         : List[str] # Images attached to the initial tweet
    post_replies        : List[ReplyPost] # Sequential replies in the thread
    category            : str # Category of the initial tweet
    datetime            : datetime # Timestamp of the initial tweet