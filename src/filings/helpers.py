
def pre_process_diff_json(diff_json, year_old=2023, year_new=2024):
    processed_output = []
    for section in diff_json:
        heading = section["heading"]
        old_lines = section.get("old", [])
        new_lines = section.get("new", [])

        updated_old = []
        updated_new = []

        # Zip the two lists so we compare line-by-line
        for old_line, new_line in zip(old_lines, new_lines):
            # Replace 2023->2024 in old
            replaced_old_line = old_line.replace(str(year_old), str(year_new))

            # If they differ *after* the replacement, keep them
            if replaced_old_line != new_line:
                updated_old.append(replaced_old_line)
                updated_new.append(new_line)

        # Only keep the entire section if, after removing identical lines,
        # there's something left in either old or new.
        if updated_old or updated_new:
            processed_output.append({
                "heading": heading,
                "old": updated_old,
                "new": updated_new
            })

    return processed_output
