import json
import requests
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService

from src.core.logging import get_logger
from src.filings.helpers import pre_process_diff_json
from src.helpers.parse_json_from_markdown import parse_json_from_markdown

logger = get_logger(__name__)


def get_tenk_insight_prompt(processed_filing):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collecton = connection.get_collection("prompts")
    prompt = prompts_collecton.find_one({'sector': 'software', 'file': 'create_filing_insights', 'function': 'create_tenk_insights'})
    if not prompt:
        logger.error("Prompt not found for 10k insights")
        raise
    tenk_insights = prompt['prompt']
    text = json.dumps(processed_filing)

    tenk_insights = tenk_insights + text
    tenk_insights = tenk_insights[:300000]
    return tenk_insights


def get_tenk_summary_prompt(tenk_insight):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collecton = connection.get_collection("prompts")
    prompt = prompts_collecton.find_one({
        'prompt_name': 'tenk_insight_summary_prompt',
        'version': 1
    })
    if not prompt:
        logger.error("No prompt found for ten-k summaries")
        raise
    tenk_summary_prompt = prompt.get('prompt', '')
    tenk_summary_prompt = tenk_summary_prompt + tenk_insight
    return tenk_summary_prompt


def create_tenk_insights(cutoff_date, lookback_days):
    """
    Creates insights from tenk
    Note: Both blackberry and filings collection has filings_diff_io collection
    """
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    filings_diff_collection = connection.get_blackberry_filings_collection("filings_diff_io")
    filings_insights_collection = connection.get_filings_collection("filings_insights_io")
    filings_summaries_collection = connection.get_filings_collection("filings_summaries_io")

    tickers = companies_collection.distinct('ticker')
    start_date = cutoff_date - timedelta(days=lookback_days)
    bb_filings_cursor = filings_diff_collection.find({
        "uploaded_date_new": {"$gte": start_date, "$lte": cutoff_date},
        "s3_diff_url": {"$exists": True},
        "accession": {"$exists": True},
        "cik": {"$exists": True},
        "ticker": {"$in": tickers},
        "type": {"$in": ["10-K", "20-F"]}
    })

    for filing in bb_filings_cursor:
        ticker = filing["ticker"]
        type = filing["type"]
        cik = filing["cik"]
        accession = filing["accession"]
        url = filing["s3_diff_url"]
        date = filing["uploaded_date_new"]
        year_new = filing["year_new"]
        year_old = filing["year_old"]
        logger.info(f"Processing 10-k insight for {accession}")
        if not all([ticker, type, accession]):
            logger.info(f"Missing data for filing: ticker={ticker}, type={type}, accession={accession}. Skipping.")
            continue
        file_name = str(ticker) + "_" + str(type) + "_" + str(accession) + ".json"

        if filings_insights_collection.find_one({"accession": accession}):
            logger.info("skipping")
            continue
        if not url or not url.startswith("http"):
            logger.info(f"Invalid or missing URL for filing {file_name}. Skipping.")
            continue

        response = requests.get(url)

        if response.status_code != 200:
            logger.info(f"Request to {url} returned status code {response.status_code}. Skipping.")
            continue

        try:
            data = response.json()
            processed_filing = pre_process_diff_json(data, year_old, year_new)
        except requests.exceptions.JSONDecodeError:
            logger.info(f"Unable to decode JSON for {file_name}. Skipping.")
            continue

        prompt = get_tenk_insight_prompt(processed_filing=processed_filing)

        tenk_insight = openai_service.get_completion(prompt=prompt, system_prompt="You are a helpful assistant who reads and extracts information. Please respond in a json", temperature=0, max_token=10000, response_format={"type": "json_object"})
        insights_list = parse_json_from_markdown(tenk_insight)

        if isinstance(insights_list, dict):
            insights_list = [insights_list]

        # Ensure insights_list is a list of dictionaries
        if not isinstance(insights_list, list):
            logger.info(f"Unexpected JSON structure for {file_name}. Skipping.")
            continue

        if not insights_list:
            logger.info(f"No insights found for {file_name}. Skipping.")
            continue

        # Add ticker and file_name to each document
        for insight in insights_list:
            if not isinstance(insight, dict):
                logger.info(f"Unexpected insight format in {file_name}. Skipping.")
                continue
            insight.update({
                "ticker": ticker,
                "file_name": file_name,
                "uploaded_date_new": date,
                "cik": cik,
                "accession": accession,
                "show": True,
                "year_new": year_new,
                "year_old": year_old,
                "type": type,
                "updated_at": datetime.now()
            })

        filings_insights_collection.insert_many(insights_list)

        prompt_summary = get_tenk_summary_prompt(tenk_insight=tenk_insight)

        filing_summary = openai_service.get_completion(prompt=prompt_summary, system_prompt="You are a helpful assistant who is NOT verbose and answers in text under 70 words without bullets or numbers.", temperature=0, max_token=300)

        filings_summaries_collection.update_one(
            {"ticker": ticker, "file_name": file_name, "cik": cik, "accession": accession},
            {"$set": {"summary": filing_summary, "updated_at": datetime.now(), "show": True, "type": "10-K", "year_new": year_new, "year_old": year_old, "uploaded_date_new": date}},
            upsert=True
        )

        filings_diff_collection.update_one({"_id": filing["_id"]}, {"$set": {"insights_created": True}})
        logger.info(f"Completed processing {accession}")


if __name__ == "__main__":
    CUTOFF_DATE = datetime.now()
    LOOKBACK_DAYS = 2
    create_tenk_insights(cutoff_date=CUTOFF_DATE, lookback_days=LOOKBACK_DAYS)
