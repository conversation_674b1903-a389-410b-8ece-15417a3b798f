import json
import requests
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService

from src.core.logging import get_logger
from src.filings.helpers import pre_process_diff_json
from src.helpers.parse_json_from_markdown import parse_json_from_markdown


logger = get_logger(__name__)


def get_tenq_insight_prompt(processed_filings):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collecton = connection.get_collection("prompts")
    prompt = prompts_collecton.find_one({
        'sector': 'software',
        'file': 'create_filing_insights',
        'function': 'create_tenq_insights'
    })
    if not prompt:
        logger.error("No prompt found for 10-Q insights.")
        raise
    prompt_tenq_insight = prompt.get('prompt', '')
    prompt_tenq_insight = prompt_tenq_insight + json.dumps(processed_filings)
    prompt_tenq_insight = prompt_tenq_insight[:300000]
    return prompt_tenq_insight


def get_tenq_summary_prompt(tenq_insight):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collecton = connection.get_collection("prompts")
    prompt = prompts_collecton.find_one({
        'prompt_name': 'tenq_insight_summary_prompt',
        'version': 1
    })
    if not prompt:
        logger.error("No prompt found for eight-k summaries")
        raise
    tenq_summary_prompt = prompt.get('prompt', '')
    tenq_summary_prompt = tenq_summary_prompt + tenq_insight
    return tenq_summary_prompt


def create_tenq_insights(cutoff_date, lookback_days):
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    filings_diff_collection = connection.get_blackberry_filings_collection("filings_diff_io")
    filings_insights_collection = connection.get_filings_collection("filings_insights_io")
    filings_summaries_collection = connection.get_filings_collection("filings_summaries_io")

    tickers = companies_collection.distinct('ticker')

    start_date = cutoff_date - timedelta(days=lookback_days)

    filings_cursor = filings_diff_collection.find({
        "uploaded_date_new": {"$gte": start_date, "$lte": cutoff_date},
        "s3_diff_url": {"$exists": True},
        "accession": {"$exists": True},
        "cik": {"$exists": True},
        "ticker": {"$in": tickers},
        "type": "10-Q"
    })

    filings_count = filings_diff_collection.count_documents({
        "uploaded_date_new": {"$gte": start_date, "$lte": cutoff_date},
        "s3_diff_url": {"$exists": True},
        "cik": {"$exists": True},
        "accession": {"$exists": True},
        "ticker": {"$in": tickers},
        "type": "10-Q"}
    )
    logger.info(f"Total filings to process: {filings_count}")

    for index, filing in enumerate(filings_cursor, start=1):
        ticker = filing["ticker"]
        filing_type = filing["type"]
        cik = filing["cik"]
        accession = filing["accession"]
        url = filing["s3_diff_url"]
        uploaded_date = filing["uploaded_date_new"]
        year_new = filing["year_new"]
        year_old = filing["year_old"]
        logger.info(f"Processing 10-Q with Accession No: {accession}")
        if not ticker or not filing_type or not accession:
            logger.info(f"Missing data for filing. ticker: {ticker}, type: {filing_type}, accession: {accession}. Skipping.")
            continue
        file_name = f"{ticker}_{filing_type}_{accession}.json"

        logger.info(f"Processing {index}/{filings_count}: {file_name}")

        # Skip if the file already exists
        if filings_insights_collection.find_one({"accession": accession}):
            logger.info("Skipping existing file.")
            continue

        if not url or not url.startswith("http"):
            logger.info(f"Invalid or missing URL for filing {file_name}. Skipping.")
            continue

        response = requests.get(url)
        if response.status_code != 200:
            logger.info(f"Failed to fetch {url}, status code: {response.status_code}. Skipping.")
            continue

        try:
            data = response.json()
            processed_filings = pre_process_diff_json(data, year_old, year_new)
        except (requests.exceptions.JSONDecodeError, json.JSONDecodeError) as e:
            logger.info(f"Error decoding JSON for {file_name}: {e}")
            continue

        # Trim prompt to 400,000 characters if needed
        tenq_insight_prompt = get_tenq_insight_prompt(processed_filings=processed_filings)
        tenq_insight = openai_service.get_completion(prompt=tenq_insight_prompt, system_prompt="You are a helpful assistant who reads and extracts information. Please respond in a json", temperature=0, max_token=10000, response_format={'type': 'json_object'})
        insights_list = parse_json_from_markdown(tenq_insight)
        if isinstance(insights_list, dict):
            insights_list = [insights_list]

        # Add metadata to each insight
        for insight in insights_list:
            insight.update({
                "ticker": ticker,
                "file_name": file_name,
                "uploaded_date_new": uploaded_date,
                "cik": cik,
                "accession": accession,
                "show": True,
                "year_new": year_new,
                "year_old": year_old,
                "type": filing_type,
                "updated_at": datetime.utcnow()
            })

        # Insert insights into database
        filings_insights_collection.insert_many(insights_list)

        # Summarization
        prompt_summary = get_tenq_summary_prompt(tenq_insight=tenq_insight)

        filing_summary = openai_service.get_completion(prompt=prompt_summary, system_prompt="You are a helpful assistant who is NOT verbose and answers in text under 70 words without bullets or numbers.", temperature=0, max_token=300)

        # Store summary
        filings_summaries_collection.update_one(
            {"ticker": ticker, "file_name": file_name, "cik": cik, "accession": accession},
            {"$set": {
                "summary": filing_summary,
                "updated_at": datetime.now(),
                "show": True,
                "type": "10-Q",
                "year_new": year_new,
                "year_old": year_old,
                "uploaded_date_new": uploaded_date
            }},
            upsert=True
        )

        # Mark insights as created in `filings_diff_io`
        filings_diff_collection.update_one(
            {"_id": filing["_id"]},
            {"$set": {"insights_created": True}}
        )
        logger.info(f"Completed processing 10-Q with Accession No: {accession}")


if __name__ == "__main__":
    CUTOFF_DATE = datetime.now()
    LOOKBACK_DAYS = 2
    create_tenq_insights(cutoff_date=CUTOFF_DATE, lookback_days=LOOKBACK_DAYS)
