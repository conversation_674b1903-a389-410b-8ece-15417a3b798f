from datetime import datetime, timedelta
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService


logger = get_logger(__name__)


def get_filing_trend_classifier_prompt():
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collecton = connection.get_collection("prompts")
    prompt = prompts_collecton.find_one({
        'prompt_name': 'filing_trend_classifier_prompt',
        'version': 2
    })
    if not prompt:
        logger.error("No prompt found for filing trend classifier")
        raise
    prompt = prompt.get('prompt', '')
    return prompt


def find_trend_in_filings(cutoff_date, lookback_days):
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    filings_insights_collection = connection.get_filings_collection("filings_insights_io")
    filings_summaries_collection = connection.get_filings_collection("filings_summaries_io")

    query = {
        "uploaded_date_new": {"$gte": cutoff_date - timedelta(days=lookback_days), "$lte": cutoff_date}
    }
    insights = list(filings_insights_collection.find(query))
    if not insights:
        logger.warning("No filings found in the given date range.")
        return
    # group by accession and get the latest updated_at date
    grouped_filings = {}
    for insight in insights:
        accession = insight["accession"]
        if accession not in grouped_filings:
            grouped_filings[accession] = []
        grouped_filings[accession].append(insight)

    for accession, insights in grouped_filings.items():
        if filings_summaries_collection.find_one({"accession": accession, "trend": {"$exists": True}}):
            logger.info("skipping")
            continue
        filing_trends_classifier_prompt = get_filing_trend_classifier_prompt()
        filing_trends_classifier_prompt += f"Filing {accession}:\n"
        for insight in insights:
            filing_trends_classifier_prompt += f"{insight['insight']}\n"
            filing_trends_classifier_prompt += f"Reference: {insight['reference']}\n"
        filing_trends_classifier_prompt += "\n"
        logger.info(f"Prompt for {accession}: {filing_trends_classifier_prompt}")

        llm_trend = openai_service.get_completion_without_limits(prompt=filing_trends_classifier_prompt, temperature=0, response_format={"type": "text"})
        logger.info(f"Results for {accession}: {llm_trend}")

        if not llm_trend:
            logger.info(f"No results for {accession}. Skipping.")
            continue
        if llm_trend.strip().lower() == "positive":
            trend = "Neutral"
        elif llm_trend.strip().lower() == "negative":
            trend = "Negative"
        elif llm_trend.strip().lower() == "neutral":
            trend = "Neutral"

        filings_summaries_collection.update_one(
            {"accession": accession},
            {"$set": {"trend": trend, "trend2": llm_trend, "updated_at": datetime.now()}},
            upsert=True
        )
        logger.info(f"Updated trend for {accession} to {trend}")


if __name__ == "__main__":
    CUTOFF_DATE = datetime.now()
    LOOKBACK_DAYS = 1
    find_trend_in_filings(cutoff_date=CUTOFF_DATE, lookback_days=LOOKBACK_DAYS)
