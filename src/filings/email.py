from datetime import datetime, timedelta
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory


logger = get_logger(__name__)


def create_filings_emails(cutoff_date, lookback_days):
    connection = DatabaseFactory().get_mongo_connection()
    filings_email_collection = connection.get_email_collection("filings_emails")
    filings_summaries_collection = connection.get_filings_collection("filings_summaries_io")

    query = {
        "uploaded_date_new": {"$gte": cutoff_date - timedelta(days=lookback_days), "$lte": cutoff_date},
        "summary": {"$exists": True}
    }

    filings = list(filings_summaries_collection.find(query))

    if not filings:
        logger.error("No filings found in the given date range.")
        return

    for filing in filings:
        accession = filing["accession"]
        logger.info(f"Generating email for Accession: {accession}")
        if filings_email_collection.find_one({"accession": accession}):
            logger.info(f"Email already exists for Accession: {accession}. Skipping.")
            continue
        email = {
            "is_approved": 0,
            "is_delivered": 0,
            "is_summary_approved": 0,
            "accession": accession,
            "uploaded_date_new": filing["uploaded_date_new"],
            "updated_at": datetime.now()
        }
        filings_email_collection.insert_one(email)
        logger.info(f"Inserted email for accession: {accession}")
    logger.info("Emails inserted successfully for")


if __name__ == "__main__":
    CUTOFF_DATE = datetime.now()
    LOOKBACK_DAYS = 1
    create_filings_emails(cutoff_date=CUTOFF_DATE, lookback_days=LOOKBACK_DAYS)
