from datetime import datetime
from src.core.constants import FILINGS_LOOKBACK_DAYS
from src.filings.email import create_filings_emails
from src.filings.insights.eightk_insights import create_eightk_insights
from src.filings.insights.tenk_insights import create_tenk_insights
from src.filings.insights.tenq_insights import create_tenq_insights
from src.filings.insights.proxy_insights import create_proxy_insights
from src.filings.insights.find_trend_in_filings import find_trend_in_filings


def process_filings():
    cutoff_date = datetime.now()
    lookback_days = FILINGS_LOOKBACK_DAYS
    create_tenk_insights(cutoff_date, lookback_days)
    create_tenq_insights(cutoff_date, lookback_days)
    create_proxy_insights(cutoff_date, lookback_days)
    create_eightk_insights(cutoff_date, lookback_days)
    create_filings_emails(cutoff_date, lookback_days)
    find_trend_in_filings(cutoff_date, lookback_days)
    return


if __name__ == "__main__":
    process_filings()
