import json
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.helpers.parse_json_from_markdown import parse_json_from_markdown


def get_thesis_qna_generator_prompt(ticker, combined_insights):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collection = connection.get_collection("prompts")
    prompt = prompts_collection.find_one({
        "prompt_name": "thesis_qna_generator_prompt",
        "version": 1
    })
    thesis_qna_generator_prompt = prompt["prompt"].format(ticker=ticker, combined_insights=combined_insights)
    return thesis_qna_generator_prompt

def ask_follow_up_questions_insightdriven(event_lookback_days=7, insight_lookback_days=60):
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    llm_insights_collection = connection.get_collection("LLM_insights")
    questions_to_mgmt_collection = connection.get_collection("questions_to_mgmt")
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    
    last_seven_days = datetime.now() - timedelta(days=event_lookback_days)
    pipeline = [
        {
            "$match": {
                "date": {"$gte": last_seven_days}
            }
        },
        {"$sort": {"date": -1}},
        {
            "$group": {
                "_id": "$ticker",
                "latest_event_id": {"$first": "$event_id"},
                "latest_date": {"$first": "$date"}
            }
        },
        {
            "$project": {
                "_id": 0,
                "ticker": "$_id",
                "latest_event_id": 1,
                "latest_date": 1
            }
        }
    ]
    relevant_events = list(public_investor_events_outputs_collection.aggregate(pipeline))

    for event in relevant_events:
        ticker = event["ticker"]
        event_id = event["latest_event_id"]
        if questions_to_mgmt_collection.find_one({"event_id": event_id}):
            print(f"This event for ticker: {ticker} has already been processed. Moving to the next ticker.")
            continue
        #extract qnaId, insight,trend from llm_insights collection for ticker for last 30 days
        insights = llm_insights_collection.find({"ticker": ticker, "date": {"$gte": datetime.now() - timedelta(days=insight_lookback_days)}})
        if not insights:
            print(f"No insights found for {ticker}")
            continue
        #combine all trend, insight into one string to be passed into openai function call
        combined_insights = " ".join([f"Trend: {insight['trend']}, Insight: {insight['insight']}" for insight in insights])
        if not combined_insights:
            print(f"No insights found for {ticker}")
            continue
        
        thesis_qna_generator_prompt = get_thesis_qna_generator_prompt(ticker=ticker, combined_insights=combined_insights)
        response = openai_service.get_completion(prompt=thesis_qna_generator_prompt, system_prompt="You are a helpful analyst", temperature=0, max_token=10000, response_format={"type": "json_object"})
    
        try:
            response_parsed = parse_json_from_markdown(response)
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON categories : {e}")
            continue
        if isinstance(response_parsed, dict):
            # Wrap single dictionary in a list
            response_parsed = [response_parsed] 
        for response in response_parsed:
            if isinstance(response, dict):
                questions_to_mgmt_collection.update_one(
                    {"ticker": ticker},
                    {
                        "$set": {
                            "event_id": event_id,
                            "question1_bull": response.get("question1_bull", ""),
                            "rationale1_bull": response.get("rationale1_bull", ""),
                            "question2_bull": response.get("question2_bull", ""),
                            "rationale2_bull": response.get("rationale2_bull", ""),
                            "question3_bull": response.get("question3_bull", ""),
                            "rationale3_bull": response.get("rationale3_bull", ""),
                            "question1_bear": response.get("question1_bear", ""),
                            "rationale1_bear": response.get("rationale1_bear", ""),
                            "question2_bear": response.get("question2_bear", ""),
                            "rationale2_bear": response.get("rationale2_bear", ""),
                            "question3_bear": response.get("question3_bear", ""),
                            "rationale3_bear": response.get("rationale3_bear", ""),
                            "updated_at": datetime.now()
                        }
                    },
                    upsert=True
                )
            print(f"Updated thesis for {ticker} with questions.")

if __name__ == "__main__":
    ask_follow_up_questions_insightdriven(event_lookback_days=5)
