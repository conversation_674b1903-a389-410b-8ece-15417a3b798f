import threading
from typing import Dict, Any
from datetime import datetime
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger

logger = get_logger(__name__)


class FeedGenerator:
    def __init__(self):
        self.connection = DatabaseFactory().get_mongo_connection()
        self.feed_collection = self.connection.get_feed_collection("feed")
        self.is_running = False
        self.change_streams = []
        self.threads = []
        self.collections_config = [
            {
                "collection": self.connection.get_collection("public_investor_events_outputs"),
                "transform_func": self.transform_public_investor_events,
                "name": "public_investor_events",
                "filter": {"show": True},
                "pipeline": [
                    {"$match": {
                        "operationType": {"$in": ["insert", "update", "delete"]},
                        "$or": [
                            {"fullDocument.show": True},
                            {"operationType": "delete"}
                        ]
                    }}
                ]
            },
            {
                "collection": self.connection.get_podcast_collection("podcast_events_final"),
                "transform_func": self.transform_podcast_events,
                "name": "podcast_events",
                "filter": {"status": "COMPLETED"},
                "pipeline": [
                    {"$match": {
                        "operationType": {"$in": ["insert", "update", "delete"]},
                        "$or": [
                            {"fullDocument.status": "COMPLETED"},
                            {"operationType": "delete"}
                        ]
                    }}
                ]
            },
            {
                "collection": self.connection.get_filings_collection("filings_summaries_io"),
                "transform_func": self.transform_filings_summaries,
                "name": "filings_summaries",
            },
            {
                "collection": self.connection.get_employee_insight_collection("blind_summary"),
                "transform_func": self.transform_blind_insights,
                "name": "employee_insights",
            },
            {
                "collection": self.connection.get_product_insight_collection("tp_summary"),
                "transform_func": self.transform_product_insights,
                "name": "product_insights",
            },
            {
                "collection": self.connection.get_sales_collection("sales_summary"),
                "transform_func": self.transform_sales_insights,
                "name": "sales_insights",
            },
            {
                "collection": self.connection.get_tweets_collection("tweets_insights"),
                "transform_func": self.transform_tweets_insights,
                "name": "tweets_insights",
            }
        ]

    def _get_company_name(self, ticker: str):
        connection = DatabaseFactory().get_mongo_connection()
        companies_collection = connection.get_collection("companies")
        company = companies_collection.find_one({"ticker": ticker})
        company_name = company.get("company_name")
        return company_name

    def transform_public_investor_events(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """Transform public investor events data for feed"""
        return {
            "feed_type": "events",
            "feed_id": document.get("_id"),
            "event_id": document.get("event_id"),
            "datetime": document.get("datetime"),
            "downticks": document.get("downticks"),
            "upticks": document.get("upticks"),
            "total_downticks": document.get("total_downticks"),
            "total_upticks": document.get("total_upticks"),
            "event_type": document.get("event_type"),
            "score": document.get("score"),
            "sentiment": document.get("sentiment"),
            "earnings_analysis": document.get("earnings_analysis"),
            "summary": document.get("summary_bullet"),
            "summary_sources": document.get("summary_bullet_sources"),
            "output_type": document.get("output_type"),
            "ticker": document.get("ticker"),
            "title": document.get("title"),
            "s3_slides_url": document.get("s3_slides_url"),
            "is_active": True
        }

    def transform_podcast_events(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """Transform podcast events data for feed"""
        return {
            "feed_type": "podcasts",
            "feed_id": document.get("_id"),
            "title": f'{document.get("podcast_title")} - {document.get("episode_title")}',
            "datetime": document.get("date"),
            "speakers": document.get("speakers"),
            "summary": document.get("summary2"),
            "ticker": document.get("ticker"),
            "is_active": True
        }

    def transform_filings_summaries(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """Transform filings summaries data for feed"""
        ticker = document.get("ticker")
        company_name = self._get_company_name(ticker=ticker)

        return {
            "feed_type": "filings",
            "feed_id": document.get("_id"),
            "accession": document.get("accession"),
            "datetime": document.get("uploaded_date_new"),
            "filing_type": document.get("type"),
            "insights": [
                {
                    "ticker": document.get("ticker"),
                    "summary": document.get("summary")
                }
            ],
            "sentiment": document.get("trend"),
            "ticker": document.get("ticker"),
            "title": f"{company_name}({ticker}), {document.get('type')} FY-{document.get('year_new')}",
            "is_active": True
        }

    def transform_blind_insights(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """Transform employee insights data for feed"""
        ticker = document.get("ticker")
        company_name = self._get_company_name(ticker=ticker)
        return {
            "feed_type": "employee-reviews",
            "feed_id": document.get("_id"),
            "datetime": document.get("end_date"),
            "insights": document.get("reviews"),
            "ticker": ticker,
            "title": f"{company_name}: Employee reviews",
            "is_active": True
        }

    def transform_product_insights(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """Transform product insights data for feed"""
        ticker = document.get("ticker")
        company_name = self._get_company_name(ticker=ticker)
        return {
            "feed_type": "product-reviews",
            "feed_id": document.get("_id"),
            "datetime": document.get("end_date"),
            "insights": document.get("reviews"),
            "ticker": ticker,
            "title": f"{company_name}: Product reviews",
            "is_active": True
        }

    def transform_sales_insights(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """Transform sales insights data for feed"""
        ticker = document.get("ticker")
        company_name = self._get_company_name(ticker=ticker)
        return {
            "feed_type": "sales",
            "feed_id": document.get("_id"),
            "datetime": document.get("end_date"),
            "insights": document.get("reviews"),
            "ticker": ticker,
            "title": f"{company_name}: Sales Insights",
            "is_active": True
        }
    
    def transform_tweets_insights(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """Transform tweets insights data for feed"""
        ticker = document.get("ticker")
        company_name = self._get_company_name(ticker=ticker)
        return {
            "feed_type": "tweets",
            "feed_id": document.get("_id"),
            "datetime": document.get("end_date"),
            "insights": document.get("insights"),
            "ticker": ticker,
            "title": f"{company_name}: Tweets Insights",
            "is_active": True
        }

    def process_change_event(self, change_event: Dict[str, Any], transform_func):
        """Process a single change event from a change stream"""
        try:
            operation_type = change_event.get("operationType")

            if operation_type == "insert":
                document = change_event.get("fullDocument")
                if document:
                    transformed_doc = transform_func(document)
                    self.feed_collection.insert_one(transformed_doc)
                    logger.info(f"Inserted new feed item of type {transformed_doc.get('feed_type')}")

            elif operation_type == "update":
                document_key = change_event.get("documentKey", {})
                feed_id = document_key.get("_id")

                full_document = change_event.get("fullDocument")

                if full_document:
                    transformed_doc = transform_func(full_document)

                    transformed_doc["updated_at"] = datetime.now()

                    result = self.feed_collection.update_one(
                        {"feed_id": feed_id},
                        {"$set": transformed_doc}
                    )

                    if result.matched_count > 0:
                        logger.info(f"Updated feed item with feed_id: {feed_id}")
                    else:
                        self.feed_collection.insert_one(transformed_doc)
                        logger.info(f"Inserted new feed item during update with feed_id: {feed_id}")
                else:
                    logger.warning(f"No full document available for update operation with feed_id: {feed_id}")

            elif operation_type == "delete":
                document_key = change_event.get("documentKey", {})
                feed_id = document_key.get("_id")

                result = self.feed_collection.delete_one({"feed_id": feed_id})

                if result.deleted_count > 0:
                    logger.info(f"Deleted feed item with feed_id: {feed_id}")
                else:
                    logger.warning(f"No feed item found to delete with feed_id: {feed_id}")

        except Exception as e:
            logger.warning(f"Error processing change event: {e}")

    def watch_collection(self, collection, transform_func, source_name: str, pipeline):
        """Watch a collection for changes using change streams"""
        try:
            logger.info(f"Starting change stream for {source_name}")
            if not pipeline:
                pipeline = [
                    {"$match": {"operationType": {"$in": ["insert", "update", "delete"]}}}
                ]

            change_stream = collection.watch(pipeline, full_document="updateLookup")
            self.change_streams.append(change_stream)

            for change_event in change_stream:
                if not self.is_running:
                    break
                self.process_change_event(change_event, transform_func)

        except Exception as e:
            logger.warning(f"Error in change stream for {source_name}: {e}")
        finally:
            if change_stream:
                change_stream.close()

    def start_monitoring(self):
        """Start monitoring all collections using change streams"""
        self.is_running = True

        # Start a thread for each collection's change stream
        for config in self.collections_config:
            thread = threading.Thread(
                target=self.watch_collection,
                args=(config["collection"], config["transform_func"], config["name"], config.get("pipeline", None)),
                daemon=True
            )
            thread.start()
            self.threads.append(thread)

        logger.info("Started monitoring all collections for changes")

    def stop_monitoring(self):
        """Stop monitoring all collections"""
        self.is_running = False

        # Close all change streams
        for stream in self.change_streams:
            try:
                stream.close()
            except Exception as e:
                logger.warning(f"Error closing change stream: {e}")

        # Wait for all threads to complete
        for thread in self.threads:
            thread.join(timeout=5)

        logger.info("Stopped monitoring all collections")

    def initial_sync(self):
        """Perform initial sync of existing data from all collections"""
        logger.info("Starting initial sync...")

        for config in self.collections_config:
            try:
                # Process existing documents in batches
                batch_size = 100
                transformed_docs = []
                filter = config.get("filter", {})

                with config["collection"].find(filter).batch_size(batch_size) as documents:
                    for doc in documents:
                        try:
                            transformed_doc = config["transform_func"](doc)
                            transformed_docs.append(transformed_doc)
                        except Exception as e:
                            logger.warning(f"Error transforming {config.get('name')} document {doc.get('_id')}: {e}")

                    if transformed_docs:
                        # Use upsert to avoid duplicates
                        for doc in transformed_docs:
                            self.feed_collection.update_one(
                                {"feed_id": doc["feed_id"]},
                                {"$set": doc},
                                upsert=True
                            )

                logger.info(f"Completed initial sync for collection {config.get('name')}")

            except Exception as e:
                logger.warning(f"Error during initial sync: {e}")

        logger.info("Initial sync completed")


def generate_feed():
    """Main function to start the feed generation process"""
    feed_generator = FeedGenerator()

    try:
        # Perform initial sync of existing data
        feed_generator.initial_sync()

        # Start real-time monitoring
        feed_generator.start_monitoring()

        # Keep the main thread alive
        logger.info("Feed generator is running. Press Ctrl+C to stop.")
        while True:
            threading.Event().wait(1)

    except KeyboardInterrupt:
        logger.info("Received interrupt signal, stopping...")
        feed_generator.stop_monitoring()
    except Exception as e:
        logger.warning(f"Error in feed generation: {e}")
        feed_generator.stop_monitoring()


if __name__ == "__main__":
    generate_feed()
