import logging
import logging.config
from datetime import datetime
from pathlib import Path
import sentry_sdk

from src.core.constants import LOG_DIRECTORY, LOG_LEVEL, SENTRY_DSN, ENVIRONMENT, RELEASE

LOG_DIR = Path(__file__).parents[2] / LOG_DIRECTORY
LOG_DIR.mkdir(exist_ok=True)


def get_log_filename(prefix="app"):
    timestamp = datetime.now().strftime("%Y%m%d")
    return f"{prefix}_{timestamp}.log"


def initialize_sentry():
    """Initialize Sentry for error tracking"""
    environment = ENVIRONMENT
    dsn = SENTRY_DSN
    release = RELEASE

    if dsn:
        sentry_sdk.init(
            dsn=dsn,
            environment=environment,
            release=release,
            traces_sample_rate=1.0,
            send_default_pii=True,
        )
        print("Sentry initialized successfully")
    else:
        print("Sentry DSN not provided, skipping Sentry initialization")


def configure_logging(log_level=LOG_LEVEL):
    """Configure logging for the application with Sentry integration"""
    if ENVIRONMENT == "production":
        initialize_sentry()
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "standard": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            },
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(message)s"
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": "INFO",
                "formatter": "standard",
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "DEBUG",
                "formatter": "detailed",
                "filename": LOG_DIR / get_log_filename(),
                "maxBytes": 10485760,
                "backupCount": 5,
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "detailed",
                "filename": LOG_DIR / get_log_filename(prefix="error"),
                "maxBytes": 10485760,
                "backupCount": 5,
            }
        },
        "loggers": {
            "": {
                "handlers": ["console", "file", "error_file"],
                "level": log_level,
                "propagate": True
            },
            "database": {
                "handlers": ["console", "file", "error_file"],
                "level": log_level,
                "propagate": False
            },
            "services": {
                "handlers": ["console", "file", "error_file"],
                "level": log_level,
                "propagate": False
            }
        }
    }

    logging.config.dictConfig(log_config)


def get_logger(name):
    """Get a logger with the specified name"""
    return logging.getLogger(name)
