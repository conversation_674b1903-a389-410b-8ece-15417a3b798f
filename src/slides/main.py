from src.slides.helpers import process_one_company
from src.database.factory import DatabaseFactory
from datetime import datetime, timedelta
from src.core.logging import get_logger, configure_logging
from tqdm import tqdm
import logging

logger = get_logger("slides.main")
configure_logging()

connection = DatabaseFactory().get_mongo_connection()
slides_collection = connection.get_slides_collection("public_slides")

connection = DatabaseFactory().get_mongo_connection()
company_collection = connection.get_collection("companies")


def update_slides_collection(start_date: datetime, end_date: datetime):
    """Process slides for companies within the specified date range."""
    companies = list(company_collection.find({}))
    for company in tqdm(companies):
        current_end = end_date
        while current_end > start_date:
            current_start = max(current_end - timedelta(days=89), start_date)
            logger.info(f"Processing company {company['ticker']} from {current_start.strftime('%Y-%m-%d')} to {current_end.strftime('%Y-%m-%d')}")
            process_one_company(company, current_start.strftime("%Y-%m-%d"), current_end.strftime("%Y-%m-%d"))
            current_end = current_start