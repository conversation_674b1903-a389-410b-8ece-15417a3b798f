import os
import boto3
from typing import BinaryIO, Union, Optional
from botocore.exceptions import Client<PERSON>rror

from src.core.logging import get_logger
from src.core.constants import SLIDES_S3_ACCESS_KEY_ID, SLIDES_S3_SECRET_ACCESS_KEY, SLIDES_S3_REGION, SLIDES_S3_BUCKET_NAME


logger = get_logger(__name__)


class SlidesS3Client:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(SlidesS3Client, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self.client = boto3.client(
            "s3",
            aws_access_key_id=SLIDES_S3_ACCESS_KEY_ID,
            aws_secret_access_key=SLIDES_S3_SECRET_ACCESS_KEY,
            region_name=SLIDES_S3_REGION,
        )
        self.bucket_name = SLIDES_S3_BUCKET_NAME
        self._initialized = True
        logger.info(f"S3 client initialized for region: {SLIDES_S3_REGION}")

    def upload_file(
        self,
        file_path: str = None,
        file_obj: BinaryIO = None,
        s3_key: str = None,
        bucket_name: str = None,
        extra_args: dict = None,
    ) -> bool:
        """
        Upload a file to S3 bucket.

        Args:
            file_path: Local path of the file to upload (if uploading from path)
            file_obj: File object to upload (if uploading from file object)
            s3_key: S3 object key where the file will be stored
            extra_args: Extra arguments to pass to boto3 (e.g., ContentType)

        Returns:
            bool: True if upload succeeded, False otherwise
        """
        if not bucket_name:
            bucket_name = self.bucket_name
        if not s3_key:
            if file_path:
                s3_key = os.path.basename(file_path)
            else:
                raise ValueError(
                    "s3_key must be provided when uploading from file object"
                )

        try:
            if file_path:
                logger.info(f"Uploading file from path: {file_path} to S3 key: {s3_key}")
                self.client.upload_file(
                    Filename=file_path,
                    Bucket=bucket_name,
                    Key=s3_key,
                    ExtraArgs=extra_args,
                )
            elif file_obj:
                logger.info(f"Uploading file object to S3 key: {s3_key}")
                self.client.upload_fileobj(
                    Fileobj=file_obj,
                    Bucket=bucket_name,
                    Key=s3_key,
                    ExtraArgs=extra_args,
                )
            else:
                raise ValueError("Either file_path or file_obj must be provided")

            logger.info(f"Successfully uploaded to s3://{bucket_name}/{s3_key}")
            return True

        except ClientError as e:
            logger.error(f"Error uploading to S3: {e}")
            return False

    def download_file(
        self, s3_key: str, local_path: str = None, file_obj: BinaryIO = None, bucket_name: str = None
    ) -> Union[bool, BinaryIO]:
        """
        Download a file from S3 bucket.

        Args:
            s3_key: S3 object key to download
            local_path: Local path where the file will be saved
            file_obj: File-like object to write the data to

        Returns:
            bool: True if download succeeded, False otherwise
            or file_obj if provided
        """
        try:
            if not bucket_name:
                bucket_name = self.bucket_name
            if local_path:
                logger.info(f"Downloading S3 key: {s3_key} to local path: {local_path}")
                os.makedirs(os.path.dirname(local_path), exist_ok=True)
                self.client.download_file(
                    Bucket=bucket_name, Key=s3_key, Filename=local_path
                )
                logger.info(f"Successfully downloaded s3://{bucket_name}/{s3_key} to {local_path}")
                return True

            elif file_obj:
                logger.info(f"Downloading S3 key: {s3_key} to file object")
                self.client.download_fileobj(Bucket=bucket_name, Key=s3_key, Fileobj=file_obj)
                logger.info(f"Successfully downloaded s3://{bucket_name}/{s3_key} to file object")
                return file_obj

            else:
                raise ValueError("Either local_path or file_obj must be provided")

        except ClientError as e:
            logger.error(f"Error downloading from S3: {e}")
            return False

    def head_object(self, s3_key: str, bucket_name: Optional[str] = None) -> Union[dict, None]:
        """
        Get metadata for an object in S3 bucket without retrieving the object itself.

        Args:
            s3_key: S3 object key to check

        Returns:
            dict: Object metadata if successful, None otherwise
        """
        try:
            if not bucket_name:
                bucket_name = self.bucket_name
            logger.info(f"Getting metadata for object: s3://{bucket_name}/{s3_key}")
            response = self.client.head_object(
                Bucket=bucket_name,
                Key=s3_key
            )
            logger.info(f"Successfully retrieved metadata for s3://{bucket_name}/{s3_key}")
            return response
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                logger.warning(f"Object not found: s3://{bucket_name}/{s3_key}")
            else:
                logger.error(f"Error checking S3 object: {e}")
            return None

    def put_object(self, s3_key: str, data: Union[str, bytes], bucket_name: str, content_type: str, content_disposition: str) -> bool:
        """
        Directly put data into an S3 object.

        Args:
            s3_key: S3 object key where the data will be stored
            data: String or bytes data to store
            metadata: Optional metadata for the S3 object

        Returns:
            bool: True if operation succeeded, False otherwise
        """
        try:
            logger.info(f"Putting object data to S3 key: {s3_key}")

            self.client.put_object(
                Bucket=bucket_name,
                Key=s3_key,
                Body=data,
                ContentType=content_type,
                ContentDisposition=content_disposition
            )
            logger.info(f"Successfully put data to s3://{bucket_name}/{s3_key}")
            return True

        except ClientError as e:
            logger.error(f"Error putting object to S3: {e}")
            return False

    def generate_presigned_url(
        self,
        s3_key: str,
        bucket_name: Optional[str] = None,
        expiration: int = 3600,
        method: str = 'get_object',
        params: dict = None
    ) -> Union[str, None]:
        """
        Generate a presigned URL for an S3 object.

        Args:
            s3_key: S3 object key
            expiration: Expiration time in seconds (default: 1 hour)
            method: S3 operation to allow ('get_object', 'put_object', etc.)
            params: Additional parameters for the operation

        Returns:
            str: Presigned URL if successful, None otherwise
        """
        try:
            if not bucket_name:
                bucket_name = self.bucket_name
            logger.info(f"Generating presigned URL for {method} operation on s3://{bucket_name}/{s3_key}")

            client_params = {
                'Bucket': bucket_name,
                'Key': s3_key,
            }

            if params:
                client_params.update(params)

            url = self.client.generate_presigned_url(
                ClientMethod=method,
                Params=client_params,
                ExpiresIn=expiration
            )

            logger.info(f"Successfully generated presigned URL for s3://{bucket_name}/{s3_key} (expires in {expiration}s)")
            return url

        except ClientError as e:
            logger.error(f"Error generating presigned URL for S3 object: {e}")
            return None
