from src.services.sp_global import SPGlobalClient
from src.slides.schemas import PublicSlidesDoc
from src.database.factory import DatabaseFactory
from src.slides.s3_client import SlidesS3Client
from bson import ObjectId
from typing import List, Optional
import uuid, time
from datetime import datetime, timezone
from src.core.logging import get_logger
from src.core.constants import SLIDES_S3_BUCKET_NAME, SLIDES_S3_REGION

logger = get_logger("slides.helpers")

connection = DatabaseFactory().get_mongo_connection()
sp_client = SPGlobalClient()
s3_client = SlidesS3Client()

events_collection = connection.get_collection("public_investor_events")
output_collection = connection.get_collection("public_investor_events_outputs")
slides_collection = connection.get_slides_collection("public_slides")

def transfer_doc_to_s3(doc_id: int, s3_key: str) -> str|None:
    """Transfer a document from SP Global to S3 bucket."""
    try:
        doc_content = sp_client.download_pdf_from_sp(doc_id)
        if not doc_content:
            logger.error(f"Document {doc_id} not found or could not be downloaded.")
            return None
        result = s3_client.put_object(
            s3_key=s3_key, 
            bucket_name=SLIDES_S3_BUCKET_NAME, 
            data=doc_content, 
            content_type="application/pdf",
            content_disposition="inline"
        )
        if result:
            presigned_url = s3_client.generate_presigned_url(
                bucket_name=SLIDES_S3_BUCKET_NAME,
                s3_key=s3_key,
                expiration=7*24*3600  # 7 days in seconds
            )
            return presigned_url
        else:
            logger.error(f"Failed to upload document {doc_id} to S3.")
            return None

    except Exception as e:
        logger.error(f"Error transferring document {doc_id} to S3: {e}")
        return None

def process_one_company(company: dict, start_date: str, end_date: str):
    sp_events = sp_client.search_events_by_company(company["company_id"], start_date, end_date)
    for sp_event in sp_events:
        if sp_event["fileType"] not in ["IP", "EP", "MP"]:
            continue

        investor_event = events_collection.find_one({"unique_event_id": sp_event["ciqKeyDevId"]})
        if investor_event:
            event_id = investor_event["_id"]
        else:
            logger.info(f"No investor event found for SP Global event {sp_event['ciqKeyDevId']}")
            event_id = None

        if slides_collection.find_one({"document_id": sp_event["documentId"], "ciq_key_dev_id": sp_event["ciqKeyDevId"], "slide_type": sp_event["fileType"]}):
            logger.info(f"Slide already processed for event {sp_event['ciqKeyDevId']}")
            continue

        file_name = f'{uuid.uuid4()}.pdf'
        doc_creation_datetime = datetime.strptime(sp_event["keydevDate"], "%Y-%m-%d %H:%M:%S.%f %z")
        file_s3_key = f'{company["ticker"]}/{doc_creation_datetime.strftime("%Y-%m-%d")}/{file_name}'

        s3_object_url = transfer_doc_to_s3(sp_event["documentId"], file_s3_key)
        if s3_object_url:
            doc: PublicSlidesDoc = {
                "event_id": event_id,
                "company_id": company["company_id"],
                "company_name": company["company_name"],
                "ticker": company["ticker"],
                "datetime": doc_creation_datetime,
                "slide_type": sp_event["fileType"],
                "title": sp_event["keydevTitle"],
                "file_name": file_name,
                "document_id": sp_event["documentId"],
                "ciq_key_dev_id": sp_event["ciqKeyDevId"],
                "s3_object_url": s3_object_url,
                "updated_at": datetime.now(timezone.utc)
            }
            result = slides_collection.insert_one(doc)
            logger.info(result)
            
            if event_id:
                result1 = events_collection.find_one_and_update(
                    {"_id": event_id},
                    {"$set": {"s3_slides_url": s3_object_url}},
                )
                result2 = output_collection.find_one_and_update(
                    {"event_id": event_id},
                    {"$set": {"s3_slides_url": s3_object_url}},
                )