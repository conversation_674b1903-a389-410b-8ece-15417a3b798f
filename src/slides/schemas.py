from typing import TypedDict, Literal
from bson import ObjectId
from datetime import datetime

class PublicSlidesDoc(TypedDict):
    event_id: ObjectId|None
    company_id: ObjectId
    company_name: str
    ticker: str
    datetime: datetime
    slide_type: Literal["IP","EP","MP"]
    title: str
    file_name: str
    document_id: int
    ciq_key_dev_id: int
    s3_object_url: str
    updated_at: datetime