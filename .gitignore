# Python bytecode
__pycache__/
*.py[cod]
*$py.class
*.so
.Python

# Distribution / packaging
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
virtualenv/
.venv/
.env/

# Environment variables
.env
.env.*
!.env.example

# Log files
logs/
*.log

# Test cache
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# IDE files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.project
.pydevproject
.settings/
*.sublime-workspace
*.sublime-project

# Project specific
data/*
!data/.gitkeep
tmp/
temp/

# OS specific
.DS_Store
Thumbs.db
desktop.ini

# Backup files
*~
*.bak
*.backup
.notes
.todo

notebook
results
quarterly*

*.csv
combine_podcasts.py
scripts
test.py
google-service-account.json