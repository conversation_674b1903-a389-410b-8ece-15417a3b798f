#!/usr/bin/env python3
"""
Cleanup script to delete log files older than 3 days.
Removes application logs, cron job logs, and cleanup logs.
"""

import os
import time
from pathlib import Path
from datetime import datetime, timedelta

# Configuration
DAYS_TO_KEEP = 3
BASE_DIR = "/home/<USER>/slatedheraklion"
APP_LOG_DIR = os.path.join(BASE_DIR, "logs")
CRON_LOG_BASE = "/home/<USER>/cron_logs"
CLEANUP_LOG_DIR = os.path.join(BASE_DIR, "cleanup_logs")

# Cron log subdirectories
CRON_SUBDIRS = [
    "events",
    "podcasts",
    "filings",
    "reviews",
    "slides",
    "earning_dates",
    "backtest",
    "short_term_ideas",
    "interview_questions"
]


def delete_old_files(directory, days_to_keep, pattern="*.log"):
    """
    Delete files older than specified days in the given directory.

    Args:
        directory: Path to the directory to clean
        days_to_keep: Number of days to keep files
        pattern: File pattern to match (default: *.log)

    Returns:
        Number of files deleted
    """
    if not os.path.exists(directory):
        print(f"Directory {directory} not found, skipping...")
        return 0

    deleted_count = 0
    cutoff_time = time.time() - (days_to_keep * 86400)  # 86400 seconds in a day

    try:
        path = Path(directory)
        for file_path in path.glob(pattern):
            if file_path.is_file():
                file_mtime = file_path.stat().st_mtime
                if file_mtime < cutoff_time:
                    try:
                        file_path.unlink()
                        print(f"Deleted: {file_path}")
                        deleted_count += 1
                    except Exception as e:
                        print(f"Error deleting {file_path}: {e}")
    except Exception as e:
        print(f"Error processing directory {directory}: {e}")

    return deleted_count


def main():
    """Main cleanup function."""
    print("=" * 60)
    print(f"Starting log cleanup - removing files older than {DAYS_TO_KEEP} days")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    total_deleted = 0

    # Cleanup application logs
    print(f"\nCleaning application logs in {APP_LOG_DIR}...")
    app_deleted = delete_old_files(APP_LOG_DIR, DAYS_TO_KEEP)
    print(f"Deleted {app_deleted} application log file(s)")
    total_deleted += app_deleted

    # Cleanup cron logs for each job type
    print("\nCleaning cron logs...")
    cron_deleted = 0
    for subdir in CRON_SUBDIRS:
        cron_dir = os.path.join(CRON_LOG_BASE, subdir)
        print(f"\nProcessing {cron_dir}...")
        deleted = delete_old_files(cron_dir, DAYS_TO_KEEP)
        print(f"Deleted {deleted} log file(s) from {subdir}")
        cron_deleted += deleted

    total_deleted += cron_deleted
    print(f"\nTotal cron log files deleted: {cron_deleted}")

    # Cleanup cleanup logs
    print(f"\nCleaning cleanup logs in {CLEANUP_LOG_DIR}...")
    cleanup_deleted = delete_old_files(CLEANUP_LOG_DIR, DAYS_TO_KEEP)
    print(f"Deleted {cleanup_deleted} cleanup log file(s)")
    total_deleted += cleanup_deleted

    # Summary
    print("\n" + "=" * 60)
    print("Cleanup completed!")
    print(f"Total files deleted: {total_deleted}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)


if __name__ == "__main__":
    main()
