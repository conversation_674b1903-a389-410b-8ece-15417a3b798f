import asyncio
import websockets
import json
import time
from src.core.constants import SEC_API_KEY
from src.core.logging import get_logger, configure_logging
from src.database.factory import DatabaseFactory
from src.modules.earnings_message.core.store_eightk import store_eightk_data

logger = get_logger(__name__)
configure_logging()

connection = DatabaseFactory.get_mongo_connection()
companies_collection = connection.get_collection("companies")
tickers = list(companies_collection.distinct("ticker"))

SERVER_URL = "wss://stream.sec-api.io"
WS_ENDPOINT = SERVER_URL + "?apiKey=" + (SEC_API_KEY or "")

async def websocket_client():
    reconnect_delay = 5
    max_reconnect_delay = 300
    
    while True:
        try:
            logger.info(f"Attempting to connect to: {SERVER_URL}")
            async with websockets.connect(
                WS_ENDPOINT,
                ping_interval=30,  # Send ping every 30 seconds (more frequent)
                ping_timeout=10,   # Wait 10 seconds for pong response
                close_timeout=10,  # Timeout for close handshake
                max_size=None,     # No message size limit
                compression=None,  # Disable compression for stability
            ) as websocket:
                logger.info(f"Successfully connected to: {SERVER_URL}")
                reconnect_delay = 5  # Reset delay on successful connection
                
                last_message_time = time.time()
                
                async def receive_messages():
                    nonlocal last_message_time
                    while True:
                        try:
                            message = await asyncio.wait_for(websocket.recv(), timeout=70)
                            last_message_time = time.time()
                            logger.debug(f"Received message at {last_message_time}")
                            
                            filings = json.loads(message)
                            for f in filings:
                                if f['ticker'] in tickers:
                                    store_eightk_data([f], f['ticker'])
                                    
                        except asyncio.TimeoutError:
                            current_time = time.time()
                            time_since_last_message = current_time - last_message_time
                            logger.warning(f"No message received in 70 seconds. Time since last message: {time_since_last_message:.1f}s")
                            
                            # Check if connection is still alive by sending a ping
                            try:
                                pong_waiter = await websocket.ping()
                                await asyncio.wait_for(pong_waiter, timeout=10)
                                logger.info("Ping/pong successful - connection is alive")
                                continue
                            except Exception as ping_error:
                                logger.error(f"Ping failed: {ping_error}")
                                break
                                
                        except websockets.exceptions.ConnectionClosed as e:
                            logger.warning(f"WebSocket connection closed: {e}")
                            break
                        except json.JSONDecodeError as e:
                            logger.error(f"Failed to decode JSON message: {e}")
                            continue
                        except Exception as e:
                            logger.error(f"Unexpected error in message handling: {e}")
                            continue
                
                # Monitor connection health
                async def connection_monitor():
                    while True:
                        await asyncio.sleep(60)  # Check every minute
                        current_time = time.time()
                        time_since_last_message = current_time - last_message_time
                        
                        if time_since_last_message > 180:  # 3 minutes without messages
                            logger.warning(f"No messages for {time_since_last_message:.1f}s, testing connection")
                            try:
                                pong_waiter = await websocket.ping()
                                await asyncio.wait_for(pong_waiter, timeout=10)
                                logger.info("Connection health check passed")
                            except Exception as health_error:
                                logger.error(f"Connection health check failed: {health_error}")
                                return
                
                # Run both tasks concurrently
                await asyncio.gather(
                    receive_messages(),
                    connection_monitor(),
                    return_exceptions=True
                )

        except websockets.exceptions.ConnectionClosed as e:
            logger.warning(f"WebSocket connection closed during setup: {e}")
        except OSError as e:
            logger.error(f"Connection error: {e}")
        except Exception as e:
            logger.error(f"An unexpected error occurred: {e}")
        
        logger.info(f"Reconnecting in {reconnect_delay} seconds...")
        await asyncio.sleep(reconnect_delay)
        
        # Exponential backoff with max delay
        reconnect_delay = min(reconnect_delay * 2, max_reconnect_delay)


if __name__ == "__main__":
    asyncio.run(websocket_client())